package com.mc.tool.framework.systemedit.view;

import com.mc.graph.AbstractCellSkin;
import com.mc.graph.interfaces.CellBehavior;
import com.mc.graph.interfaces.CellBindedObject;
import com.mc.graph.interfaces.CellObject;
import com.mc.graph.interfaces.Connector;
import com.mc.graph.interfaces.ConnectorIdentifier;
import com.mc.graph.interfaces.ConnectorSkin;
import com.mc.graph.interfaces.SkinManager;
import com.mc.tool.framework.systemedit.datamodel.VisualEditMatrix;
import com.mc.tool.framework.systemedit.datamodel.VisualEditNode;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import java.io.IOException;
import java.net.URL;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;
import java.util.ResourceBundle;
import java.util.Set;
import javafx.collections.ListChangeListener;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.geometry.Insets;
import javafx.scene.Parent;
import javafx.scene.control.Label;
import javafx.scene.control.Tooltip;
import javafx.scene.input.MouseButton;
import javafx.scene.input.MouseEvent;
import javafx.scene.layout.Background;
import javafx.scene.layout.BackgroundFill;
import javafx.scene.layout.Pane;
import javafx.scene.layout.Region;
import javafx.scene.paint.Color;
import javafx.scene.text.TextAlignment;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public final class MatrixCellSkin extends AbstractCellSkin implements Initializable {
  private static final int CONNECTOR_GAP = 20;
  private static final int MATRIX_CELL_WIDTH = 200;
  private Map<Object, ConnectorSkin> connectorSkins;
  @FXML
  private Pane region;
  @FXML
  private Label nameLabel;

  private ListChangeListener<VisualEditTerminal> allTerminalsChangeListener;

  /**
   * Contructor.
   *
   * @param cellobject  cell object
   * @param parent      parent
   * @param container   container
   * @param skinManager skin manager
   */
  public MatrixCellSkin(CellObject cellobject, Parent parent, Parent container,
                        SkinManager skinManager) {
    super(cellobject, parent, container, skinManager);
    //matrix的bound是计算connector时计算好的
    this.getRegion().prefWidthProperty().removeListener(boundChangeListener);
    this.getRegion().prefHeightProperty().removeListener(boundChangeListener);
  }

  protected ListChangeListener<VisualEditTerminal> getAllTerminalsChangeListener() {
    if (allTerminalsChangeListener == null) {
      allTerminalsChangeListener = weakAdapter
          .wrap((ListChangeListener<VisualEditTerminal>) (change) -> {
            layoutConnector2();
          });
    }
    return allTerminalsChangeListener;
  }

  @Override
  public Region getRegion() {
    return region;
  }

  @Override
  public Color getSelectionBorderColor() {
    return SystemeditConstants.SELECTED_COLOR;
  }

  @Override
  public boolean isSelectionEffectEnabled() {
    return false;
  }

  @Override
  public boolean isResizeble() {
    return false;
  }

  @Override
  public boolean isMovable() {
    return false;
  }

  @Override
  protected void onConnectorChange() {
    if (cellObject == null) {
      return;
    }
    // 删除没有用的connectorskin，创建新的
    Map<Object, ConnectorSkin> newSkins = new HashMap<>();
    for (Connector connector : cellObject.getConnectors()) {
      ConnectorSkin cs = null;
      if (connectorSkins.containsKey(connector.getId())) {
        cs = connectorSkins.get(connector.getId());
      }
      if (cs == null) {
        cs = new MatrixConnectorSkin(connector, getRegion(), container);
        skinManager.setConnectorSkin(connector, cs);
        for (CellBehavior cellBehavior : cellBehaviors) {
          cellBehavior.createConnectorBehavior(cs);
        }
        if (getRegion().getParent() != null) {
          cs.add();
        }
      }
      newSkins.put(connector.getId(), cs);
    } // end for

    for (Entry<Object, ConnectorSkin> entry : connectorSkins.entrySet()) {
      if (!newSkins.containsKey(entry.getKey())) {
        entry.getValue().remove();
      }
    }

    connectorSkins.clear();
    connectorSkins.putAll(newSkins);

    layoutConnectors();
  }

  @Override
  protected void layoutConnectors() {
    layoutConnector2();
  }

  protected void layoutConnector2() {
    if (getCell() == null) {
      return;
    }
    VisualEditMatrix matrix = (VisualEditMatrix) getCell().getBindedObject();
    if (matrix == null) {
      return;
    }

    Collection<ConnectorIdentifier> leftConnectors = matrix.getLeftConnectorIds();
    Collection<ConnectorIdentifier> rightConnectors = matrix.getRightConnectorIds();
    int leftCount = leftConnectors.size();
    int rightCount = rightConnectors.size();

    double minHeight = (int) (Math.max(leftCount, rightCount) + 0.6)
        * (CONNECTOR_GAP + MatrixConnectorSkin.SKIN_HEIGHT) + CONNECTOR_GAP;
    double minwidth = MATRIX_CELL_WIDTH;
    getRegion().setMinSize(minwidth, minHeight);
    getRegion().setPrefSize(minwidth, minHeight);

    if (matrix.getConnectorId().size() != getCell().getConnectors().size()) {
      return;
    }

    double width = getRegion().getPrefWidth();
    double height = getRegion().getPrefHeight();

    double leftTopInset =
        (height - leftCount * MatrixConnectorSkin.SKIN_HEIGHT - (leftCount - 1) * CONNECTOR_GAP)
            / 2;
    double rightTopInset =
        (height - rightCount * MatrixConnectorSkin.SKIN_HEIGHT - (rightCount - 1) * CONNECTOR_GAP)
            / 2;
    if (this.getParent() == null) {
      return;
    }
    // 布局左边的connector
    int index = 0;
    for (Object connectorId : leftConnectors) {
      ConnectorSkin skin = connectorSkins.get(connectorId);
      if (skin == null) {
        log.warn("Do not exist connector : {}", connectorId.toString());
        continue;
      }
      double xpos = 0;
      double ypos = leftTopInset + index * (CONNECTOR_GAP + MatrixConnectorSkin.SKIN_HEIGHT);
      skin.getNode().setLayoutX(xpos);
      skin.getNode().setLayoutY(ypos);
      if (skin instanceof MatrixConnectorSkin) {
        MatrixConnectorSkin connectorSkin = (MatrixConnectorSkin) skin;
        connectorSkin.setLeft();
      }
      skin.getNode().setVisible(true);
      index++;
    }
    // 布局右边的connector
    index = 0;
    for (Object connectorId : rightConnectors) {
      ConnectorSkin skin = connectorSkins.get(connectorId);
      if (skin == null) {
        log.warn("Do not exist connector : {}", connectorId);
        continue;
      }
      double xpos = width - MatrixConnectorSkin.SKIN_WIDTH;
      double ypos = rightTopInset + index * (CONNECTOR_GAP + MatrixConnectorSkin.SKIN_HEIGHT);
      skin.getNode().setLayoutX(xpos);
      skin.getNode().setLayoutY(ypos);
      if (skin instanceof MatrixConnectorSkin) {
        MatrixConnectorSkin connectorSkin = (MatrixConnectorSkin) skin;
        connectorSkin.setRight();
      }
      skin.getNode().setVisible(true);
      index++;
    }
    // 隐藏不需要的connector
    Set<Object> connectorSet = new HashSet<>(connectorSkins.keySet());
    connectorSet.removeAll(leftConnectors);
    connectorSet.removeAll(rightConnectors);
    for (Object connectorId : connectorSet) {
      connectorSkins.get(connectorId).getNode().setVisible(false);
    }
  }

  @Override
  protected void initRegion() {
    // createRegionByFxml();
    createRegionByCode();
  }

  protected void createRegionByFxml() {
    try {
      FXMLLoader loader = new FXMLLoader(
          getClass().getResource("/com/mc/tool/framework/systemedit/matrix_cell_skin.fxml"));
      loader.setController(this);
      loader.load();
    } catch (IOException exc) {
      log.warn("Can not load matrix_cell_skin.fxml", exc);
    }
  }

  private void createRegionByCode() {
    region = new NoCssPane();
    region.setPrefWidth(MATRIX_CELL_WIDTH);
    region.setMinWidth(MATRIX_CELL_WIDTH);
    region.getStyleClass().add("matrix");
    region.getStylesheets().add(SystemeditConstants.RESOURCE_PATH + "matrix_cell_skin.css");

    nameLabel = new NoCssLabel();
    nameLabel.setTextAlignment(TextAlignment.CENTER);
    nameLabel.getStyleClass().add("name-label");
    region.getChildren().add(nameLabel);

    // css
    BackgroundFill outterFill = new BackgroundFill(Color.web("#969899"), null, null);
    BackgroundFill innerFill = new BackgroundFill(Color.web("#e9eef2"), null, new Insets(1));
    Background background = new Background(outterFill, innerFill);
    region.setBackground(background);
    nameLabel.setTextFill(Color.web("#333333"));

    initialize(null, null);
  }

  @Override
  protected void initInner() {
    connectorSkins = new HashMap<>();
  }

  @Override
  public Collection<ConnectorSkin> getConnectorSkins() {
    return connectorSkins.values();
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {
    region.setUserData(this);
    nameLabel.layoutXProperty()
        .bind(region.widthProperty().subtract(nameLabel.widthProperty()).divide(2.0));
    nameLabel.layoutYProperty()
        .bind(region.heightProperty().subtract(nameLabel.heightProperty()).divide(2.0));
    nameLabel.maxWidthProperty().bind(region.widthProperty().subtract(100));
    nameLabel.maxHeightProperty().bind(region.heightProperty().subtract(CONNECTOR_GAP * 2));
    nameLabel.setWrapText(true);
    Tooltip tooltip = new Tooltip();
    tooltip.textProperty().bind(nameLabel.textProperty());
    nameLabel.setTooltip(tooltip);
    region.addEventFilter(MouseEvent.MOUSE_CLICKED, (event) -> {
      onMouseClicked(event);
    });

    updateNameBinding();
    updateAllTerminalListener(null, getCell().getBindedObject());
    getCell().getBindedObjectProperty().addListener(weakAdapter.wrap((change, oldVal, newVal) -> {
      updateNameBinding();
      updateAllTerminalListener(oldVal, newVal);
    }));
  }

  @Override
  public void destroy() {
    super.destroy();
    connectorSkins.clear();

  }

  private void updateNameBinding() {
    nameLabel.textProperty().unbind();
    if (getCell().getBindedObject() instanceof VisualEditNode) {
      VisualEditNode node = (VisualEditNode) getCell().getBindedObject();
      nameLabel.textProperty().bindBidirectional(node.nameProperty());
    }
  }

  private void updateAllTerminalListener(CellBindedObject oldVal, CellBindedObject newValue) {
    if (newValue instanceof VisualEditMatrix) {
      VisualEditMatrix matrix = (VisualEditMatrix) newValue;
      matrix.getAllTerminalChild().addListener(getAllTerminalsChangeListener());
    }
    if (oldVal instanceof VisualEditMatrix) {
      VisualEditMatrix matrix = (VisualEditMatrix) oldVal;
      matrix.getAllTerminalChild().removeListener(getAllTerminalsChangeListener());
    }
  }

  private void onMouseClicked(MouseEvent event) {
    if (event.getButton() == MouseButton.PRIMARY && event.getClickCount() == 2) {
      // 双击修改名称
      Optional<String> result =
          ViewUtility.getNameFromDialog(nameLabel.getScene().getWindow(), nameLabel.getText());
      if (result.isPresent()) {
        nameLabel.setText(result.get());
      }
    }
  }
}
