package com.mc.tool.framework.operation.videowall.controller;

import static org.junit.Assert.assertEquals;

import java.util.Arrays;
import javafx.geometry.Rectangle2D;
import org.junit.Test;

public class VideoWallUtilityTest {

  @Test
  public void testIsRectCovered() {
    Rectangle2D rect = new Rectangle2D(0, 0, 100, 100);
    Rectangle2D r1 = new Rectangle2D(0, 0, 50, 50);
    Rectangle2D r2 = new Rectangle2D(50, 0, 50, 50);
    Rectangle2D r3 = new Rectangle2D(0, 50, 50, 50);
    Rectangle2D r4 = new Rectangle2D(50, 50, 50, 50);
    assertEquals(true, VideoWallUtility.isRectCovered(rect, Arrays.asList(r1, r2, r3, r4)));
    Rectangle2D r5 = new Rectangle2D(50, 50, 50, 49);
    assertEquals(false, VideoWallUtility.isRectCovered(rect, Arrays.asList(r1, r2, r3, r5)));


  }

}
