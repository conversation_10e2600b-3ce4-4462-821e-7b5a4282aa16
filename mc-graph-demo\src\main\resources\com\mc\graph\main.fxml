<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.*?>
<?import java.lang.*?>
<?import javafx.scene.layout.*?>
<?import org.controlsfx.control.*?>

<VBox maxHeight="-Infinity" maxWidth="-Infinity" minHeight="-Infinity"
	minWidth="-Infinity" prefHeight="540" prefWidth="360"
	xmlns="http://javafx.com/javafx/8" xmlns:fx="http://javafx.com/fxml/1" fx:id="container"
	fx:controller="com.mc.graph.GraphMain">
	<MenuBar>
			<Menu mnemonicParsing="false" text="Operation">
				<items>
					<MenuItem mnemonicParsing="false" text="Add" onAction="#onAdd"/>
					<MenuItem mnemonicParsing="false" text="Undo" onAction="#onUndo"/>
					<MenuItem mnemonicParsing="false" text="Redo" onAction="#onRedo"/>
					<MenuItem mnemonicParsing="false" text="onUp" onAction="#onUp"/>
					<MenuItem mnemonicParsing="false" text="onDown" onAction="#onDown"/>
				</items>
			</Menu>
	</MenuBar>
	<HBox>
		<Slider fx:id="slider" min="0.5" max="2"/>
		<Region HBox.Hgrow="ALWAYS" />
		<ToggleButton text="Preview" fx:id="previewButton"/>
	</HBox>
</VBox>
