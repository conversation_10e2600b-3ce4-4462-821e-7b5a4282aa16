package com.mc.common.beans;

import javafx.beans.binding.StringBinding;
import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.StringProperty;

public class BindingUtilities {
  /**
   * 创建一个stringproperty来绑定一个stringbinding.
   * @param binding binding.
   * @return property
   */
  public static StringProperty toProperty(StringBinding binding) {
    StringProperty property = new SimpleStringProperty();
    property.bind(binding);
    return property;
  }
}
