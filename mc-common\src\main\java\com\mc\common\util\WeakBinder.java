package com.mc.common.util;

import javafx.beans.InvalidationListener;
import javafx.beans.WeakInvalidationListener;
import javafx.beans.property.Property;
import javafx.beans.value.ObservableValue;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class WeakBinder {
  private final List<Object> hardRefs = new ArrayList<>();
  private final Map<ObservableValue<?>, WeakInvalidationListener> listeners = new HashMap<>();

  /**
   * 全部绑定过的属性解绑.
   */
  public void unbindAll() {
    for (Map.Entry<ObservableValue<?>, WeakInvalidationListener> entry : listeners.entrySet()) {
      entry.getKey().removeListener(entry.getValue());
    }

    hardRefs.clear();
    listeners.clear();
  }

  /**
   * 绑定属性.
   * @param property 需要绑定其他值的属性
   * @param dest 值的来源.
   */
  public <T> void bind(final Property<T> property, final ObservableValue<? extends T> dest) {
    InvalidationListener invalidationListener = (obs) -> {
      property.setValue(dest.getValue());
    };

    WeakInvalidationListener weakInvalidationListener =
        new WeakInvalidationListener(invalidationListener);

    listeners.put(dest, weakInvalidationListener);

    dest.addListener(weakInvalidationListener);
    property.setValue(dest.getValue());

    hardRefs.add(invalidationListener);
  }
}
