package com.mc.common.beans;

import javafx.beans.binding.ObjectBinding;
import javafx.beans.binding.StringBinding;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.scene.paint.Color;

public class BackgroundColorStyleBinding extends StringBinding {
  private ObjectProperty<Color> color;

  public BackgroundColorStyleBinding(ObjectProperty<Color> color) {
    this.color = color;
    bind(color);
  }
  
  /**
   * Contructor.
   * @param color color binding
   */
  public BackgroundColorStyleBinding(ObjectBinding<Color> color) {
    this.color = new SimpleObjectProperty<>();
    this.color.bind(color);
    bind(color);
  }

  @Override
  protected String computeValue() {
    return String.format("-fx-background-color:#%s;", color.get().toString().substring(2));
  }
}
