package com.mc.tool.framework.systemedit.controller;

import com.mc.graph.interfaces.CellObject;
import com.mc.graph.interfaces.Connector;
import com.mc.graph.interfaces.ConnectorIdentifier;
import com.mc.graph.interfaces.ConnectorSkin;
import com.mc.graph.interfaces.LinkObject;
import com.mc.graph.util.SelectableNode;
import com.mc.tool.framework.systemedit.datamodel.SystemEditDefinition;
import com.mc.tool.framework.systemedit.datamodel.VisualEditGroup;
import com.mc.tool.framework.systemedit.datamodel.VisualEditMatrix;
import com.mc.tool.framework.systemedit.datamodel.VisualEditModel;
import com.mc.tool.framework.systemedit.datamodel.VisualEditNode;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import com.mc.tool.framework.systemedit.graph.SystemEditGraph;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import javafx.beans.InvalidationListener;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.collections.ListChangeListener;
import javafx.event.EventHandler;
import javafx.scene.input.MouseEvent;
import javafx.util.Pair;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class SystemEditMatrixWrapper implements GraphWrapper {

  @Getter
  protected final SystemEditGraph graph;

  /**
   * 保存需要保留的cell，便于删除无用的cell.
   */
  protected List<CellObject> reservedCells = new ArrayList<>();
  /**
   * 保存需要保留的link，便于删除无用的link.
   */
  protected List<LinkObject> reservedLinks = new ArrayList<>();

  protected final VisualEditModel systemEditModel;

  protected final BooleanProperty onlineEdit;

  protected List<ListChangeListener<SelectableNode>> selectChangeListeners = new ArrayList<>();
  protected List<EventHandler<MouseEvent>> clickEventHandlers = new ArrayList<>();
  protected List<EventHandler<MouseEvent>> clickEventFilters = new ArrayList<>();
  protected List<InvalidationListener> scaleChangeListeners = new ArrayList<>();

  protected BooleanProperty attached = new SimpleBooleanProperty(false);

  @Getter
  @Setter
  protected VisualEditMatrix matrix;

  /**
   * .
   *
   * @param model      model
   * @param graph      graph
   * @param onlineEdit onlineedit
   */
  public SystemEditMatrixWrapper(VisualEditModel model, SystemEditGraph graph,
                                 BooleanProperty onlineEdit) {
    this.graph = graph;
    this.systemEditModel = model;
    this.onlineEdit = onlineEdit;
    init();

    attached.addListener((obs, oldVal, newVal) -> updateModelToGraph());
  }

  public void init() {
    graph.init();
  }

  /**
   * 添加选择的节点修改后的listener.
   *
   * @param scaleChangeListener listener
   */
  @Override
  public void attachScaleChangeListener(InvalidationListener scaleChangeListener) {
    graph.getGraphCanvas().getScaleProperty().addListener(scaleChangeListener);
    scaleChangeListeners.add(scaleChangeListener);
  }

  @Override
  public void attachSelectChangeListener(ListChangeListener<SelectableNode> selectChangeListener) {
    graph.getSelectionModel().getSelectedItems().addListener(selectChangeListener);
    selectChangeListeners.add(selectChangeListener);
  }

  /**
   * 绑定点击事件handler.
   *
   * @param clickEventHandler handler
   */
  @Override
  public void attachClickEventHandler(EventHandler<MouseEvent> clickEventHandler) {
    graph.getGraphCanvas().getContainer().addEventHandler(
        MouseEvent.MOUSE_CLICKED, clickEventHandler);
    clickEventHandlers.add(clickEventHandler);
  }

  /**
   * 绑定点击事件filter.
   *
   * @param clickEventFilter filter
   */
  @Override
  public void attachClickEventFilter(EventHandler<MouseEvent> clickEventFilter) {
    graph.getGraphCanvas().getContainer().addEventFilter(MouseEvent.MOUSE_CLICKED,
        clickEventFilter);
    clickEventFilters.add(clickEventFilter);
  }

  @Override
  public void attach() {
    attached.set(true);
  }

  @Override
  public void detach() {
    attached.set(false);
    for (ListChangeListener<SelectableNode> listener : selectChangeListeners) {
      graph.getSelectionModel().getSelectedItems().removeListener(listener);
    }
    for (EventHandler<MouseEvent> handler : clickEventHandlers) {
      graph.getGraphCanvas().getContainer().removeEventHandler(MouseEvent.MOUSE_CLICKED, handler);
    }
    for (EventHandler<MouseEvent> filter : clickEventFilters) {
      graph.getGraphCanvas().getContainer().removeEventFilter(MouseEvent.MOUSE_CLICKED, filter);
    }
    for (InvalidationListener listener : scaleChangeListeners) {
      graph.getGraphCanvas().getScaleProperty().removeListener(listener);
    }

    selectChangeListeners.clear();
    clickEventHandlers.clear();
    clickEventFilters.clear();
    scaleChangeListeners.clear();

    graph.retainLinks();
    graph.retainCells();
  }

  /**
   * 更新graph.
   */
  @Override
  public void updateModelToGraph() {
    // 如果没有添加到界面中，就不刷新

    if (!attached.get()) {
      return;
    }
    reservedCells.clear();
    reservedLinks.clear();
    //如果有矩阵信息，只刷新矩阵的内容，否则刷新全部矩阵
    if (matrix != null) {
      updateSystemTree(matrix, matrix);
    } else {
      for (VisualEditNode root : systemEditModel.getRoots()) {
        VisualEditMatrix matrix = null;
        if (root instanceof VisualEditMatrix) {
          matrix = (VisualEditMatrix) root;
        }
        updateSystemTree(matrix, root);
      }
    }
    graph.retainCells(reservedCells.toArray(new CellObject[0]));
    graph.retainLinks(reservedLinks.toArray(new LinkObject[0]));
    reservedCells.clear();
    reservedLinks.clear();
  }

  @Override
  public void clearGraph() {
    if (matrix != null) {
      clearSystemTree(matrix);
    } else {
      for (VisualEditNode root : systemEditModel.getRoots()) {
        clearSystemTree(root);
      }
    }
    graph.retainCells();
    graph.retainLinks();
  }

  @Override
  public String getName() {
    if (getMatrix() != null) {
      return getMatrix().getName();
    } else {
      return "null";
    }
  }

  @Override
  public boolean isAttached() {
    return attached.get();
  }

  protected List<LinkInfo> getLinks(VisualEditMatrix matrix, VisualEditNode parent,
                                    VisualEditNode child) {
    List<LinkInfo> result = new ArrayList<>();
    if (parent instanceof VisualEditMatrix) {
      // matrix的连接点为与terminal连接的端口，child的连接为group的固定点或者terminal自己的端口
      Collection<VisualEditTerminal> terminals = child.getAllOnlineTerminalChild();
      boolean isChildGroup = child instanceof VisualEditGroup;
      for (VisualEditTerminal terminal : terminals) {
        Collection<Pair<ConnectorIdentifier, ConnectorIdentifier>> pairs =
            matrix.getChildConnectorPair(terminal);
        for (Pair<ConnectorIdentifier, ConnectorIdentifier> pair : pairs) {
          if (isChildGroup) {
            result.add(
                new LinkInfo(pair.getKey(), VisualEditGroup.GROUP_PARENT_CONNECTOR, pair.getKey()));
          } else {
            result.add(new LinkInfo(pair.getKey(), pair.getValue(), pair.getKey()));
          }
        }
      }
    } else if (parent instanceof VisualEditGroup) {
      if (child instanceof VisualEditGroup) {
        Collection<VisualEditTerminal> terminals = child.getAllOnlineTerminalChild();
        List<ConnectorIdentifier> relatedMatrixConnectors = new ArrayList<>();
        for (VisualEditTerminal terminal : terminals) {
          Collection<Pair<ConnectorIdentifier, ConnectorIdentifier>> pairs =
              matrix.getChildConnectorPair(terminal);
          for (Pair<ConnectorIdentifier, ConnectorIdentifier> pair : pairs) {
            relatedMatrixConnectors.add(pair.getKey());
          }
        }
        result.add(new LinkInfo(VisualEditGroup.GROUP_CHILD_CONNECTOR,
            VisualEditGroup.GROUP_PARENT_CONNECTOR,
            relatedMatrixConnectors.toArray(new Object[0])));
      } else {
        // child 为terminal
        Collection<Pair<ConnectorIdentifier, ConnectorIdentifier>> pairs =
            matrix.getChildConnectorPair((VisualEditTerminal) child);
        for (Pair<ConnectorIdentifier, ConnectorIdentifier> pair : pairs) {
          result.add(
              new LinkInfo(VisualEditGroup.GROUP_CHILD_CONNECTOR, pair.getValue(), pair.getKey()));
        }
      }
    } else {
      log.error("Error parent type : {}", parent.getClass().getName());
    }

    return result;
  }

  protected boolean isNodeVisible(VisualEditNode node, boolean onlineEdit) {
    return SystemEditUtility.isNodeVisible(node, onlineEdit);
  }

  /**
   * 更新root下的所有节点.
   *
   * @param matrix matrix
   * @param root   root
   * @return root对应的cell
   */
  protected CellObject updateSystemTree(VisualEditMatrix matrix, VisualEditNode root) {
    if (!isNodeVisible(root, onlineEdit.get())) {
      root.setCellObject(null);
      return null;
    }
    CellObject cellObject = updateGraphCell(root);
    reservedCells.add(cellObject);
    for (VisualEditNode child : root.getChildren()) {
      CellObject childCell = updateSystemTree(matrix, child);
      if (childCell == null) {
        continue;
      }
      // 当父节点显示而且不是collapse的情况下显示
      updateChildProperty(root, cellObject, childCell);
      updateChildLinks(matrix, root, cellObject, child, childCell);
    }
    return cellObject;
  }

  protected void clearSystemTree(VisualEditNode root) {
    root.setCellObject(null);
    for (VisualEditNode child : root.getChildren()) {
      clearSystemTree(child);
    }
  }

  protected void updateChildProperty(VisualEditNode parent, CellObject parentCell, CellObject childCell) {
    CellVisibilityUpdater updater = SystemEditUtility.getCellPropertyUpdater(childCell,
        SystemEditDefinition.CELL_VISIBILITY_UPDATER, CellVisibilityUpdater.class);
    if (updater == null) {
      updater = new CellVisibilityUpdater(childCell);
      childCell.getPropertyUpdaters().put(SystemEditDefinition.CELL_VISIBILITY_UPDATER, updater);
    }
    updater.setParent(parent);
  }

  protected void calculateYPos(VisualEditMatrix matrix, VisualEditNode node, CellObject cellObject) {
    // 计算固定位置y
    if (matrix != node && node.isFixToMatrix(matrix) && matrix != null) {
      Collection<VisualEditTerminal> terminals = node.getAllOnlineTerminalChild();
      final List<ConnectorSkin> matrixConnectorSkins = new ArrayList<>();

      if (terminals.size() == 0) {
        log.error("The length of terminal child should be greater than 0.");
      }
      for (VisualEditTerminal terminal : terminals) {
        Collection<Pair<ConnectorIdentifier, ConnectorIdentifier>> connectorsPair =
            matrix.getChildConnectorPair(terminal);

        for (Pair<ConnectorIdentifier, ConnectorIdentifier> pair : connectorsPair) {
          Connector connector = matrix.getCellObject().getConnector(pair.getKey());
          if (connector == null) {
            log.error("Can not find the connector.");
            continue;
          }

          ConnectorSkin skin = graph.getSkinManager().getConnectorSkin(connector);
          if (skin == null) {
            log.error("Can not find the connector skin.");
          } else {
            matrixConnectorSkins.add(skin);
          }
        }
      }

      CellYposUpdater updater = SystemEditUtility.getCellPropertyUpdater(cellObject,
          SystemEditDefinition.CELL_YPOS_UPDATER, CellYposUpdater.class);
      if (updater == null) {
        updater = new CellYposUpdater(cellObject);
        cellObject.getPropertyUpdaters().put(SystemEditDefinition.CELL_YPOS_UPDATER, updater);
      }
      updater.setRelatedMatrixConnectorSkins(matrixConnectorSkins);
    }
  }

  /**
   * 更新节点之间的链接.
   *
   * @param matrix     matrix
   * @param parent     parent节点
   * @param parentCell parent cell
   * @param child      child节点
   * @param childCell  child cell
   */
  public void updateChildLinks(VisualEditMatrix matrix, VisualEditNode parent,
                               CellObject parentCell, VisualEditNode child, CellObject childCell) {
    List<LinkInfo> conns = getLinks(matrix, parent, child);
    Connector parentConnector = null;
    for (LinkInfo conn : conns) {
      Connector first = parentCell.getConnector(conn.getParentConnector());
      Connector second = childCell.getConnector(conn.getChildConnector());
      if (first == null || second == null) {
        continue;
      }
      LinkObject linkObject = graph.connect(first, second, false);
      reservedLinks.add(linkObject);
      if (linkObject == null) {
        log.warn("Can not link to child.");
      } else {
        Set<Connector> relatedMatrixConnector = new HashSet<>();
        for (Object connector : conn.relatedMatrixConnector) {
          Connector matrixConnector = matrix.getCellObject().getConnector(connector);
          if (matrixConnector == null) {
            log.warn("Can not find the matrix connector");
          } else {
            relatedMatrixConnector.add(matrixConnector);
          }
        }
        linkObject.getConnectorIntegration().setConnectors(relatedMatrixConnector);
      }
      parentConnector = first;
    }

    Set<Connector> relatedMatrixConnector = new HashSet<>();
    for (LinkInfo conn : conns) {
      for (Object connector : conn.relatedMatrixConnector) {
        Connector matrixConnector = matrix.getCellObject().getConnector(connector);
        if (matrixConnector == null) {
          log.warn("Fail to find the connector : {}!", connector);
          continue;
        }
        relatedMatrixConnector.add(matrixConnector);
      }
    }
    childCell.getConnectorIntegration().setConnectors(relatedMatrixConnector);

    calculateXPos(matrix, child, childCell, parentConnector);
    calculateYPos(matrix, child, childCell);
  }

  @SuppressWarnings("checkstyle:AbbreviationAsWordInName")
  protected void calculateXPos(VisualEditMatrix matrix, VisualEditNode child, CellObject childCell,
                               Connector parentConnector) {
    if (child.isFixToMatrix(matrix) && matrix != null) {
      // 计算固定位置x坐标
      if (parentConnector == null) {
        log.error("No connection with parent!");
        return;
      }

      CellXposUpdater updater = SystemEditUtility.getCellPropertyUpdater(childCell,
          SystemEditDefinition.CELL_XPOS_UPDATER, CellXposUpdater.class);
      if (updater == null) {
        updater = new CellXposUpdater(graph.getSkinManager(), childCell);
        childCell.getPropertyUpdaters().put(SystemEditDefinition.CELL_XPOS_UPDATER, updater);
      }
      updater.setParentConnectorSkin(graph.getSkinManager().getConnectorSkin(parentConnector),
          SystemEditDefinition.nodeAtLeft(child));
    }
  }

  protected CellObject updateGraphCell(VisualEditNode root) {
    if (root.getCellObject() == null || !graph.getGraphModel().hasCell(root.getCellObject())) {
      CellObject cellObject = graph
          .createCell("0", root.getExtraData(), root.locationXposProperty().get(),
              root.locationYposProperty().get(), 100, 100, root.getNodeType(), root);
      root.setCellObject(cellObject);
      return cellObject;
    } else {
      return root.getCellObject();
    }
  }


  static class LinkInfo {

    @Getter
    private Object parentConnector;
    @Getter
    private Object childConnector;
    @Getter
    private Collection<Object> relatedMatrixConnector;

    public LinkInfo(Object parentConnector, Object childConnector, Object... relatedMatrixConnector) {
      this.parentConnector = parentConnector;
      this.childConnector = childConnector;
      this.relatedMatrixConnector = Arrays.asList(relatedMatrixConnector);
    }
  }
}
