package com.mc.tool.framework.interfaces;

import com.mc.tool.framework.beans.ConfigBean;
import java.util.Locale;

/**
 * .
 */
public interface AppConfig {
  /**
   * Get the software's language.
   *
   * @return current language locale
   */
  Locale getLanguage();

  /**
   * Get the default import path.
   *
   * @return default import path
   */
  String getDefaultImportPath();

  /**
   * Get the default firmware path.
   *
   * @return default firmware path
   */
  String getDefaultFirmwarePath();

  ConfigBean getConfigBean();
}
