package com.mc.common.util;

import javafx.beans.InvalidationListener;
import javafx.beans.WeakInvalidationListener;
import javafx.beans.value.ChangeListener;
import javafx.beans.value.WeakChangeListener;
import javafx.collections.ListChangeListener;
import javafx.collections.MapChangeListener;
import javafx.collections.WeakListChangeListener;
import javafx.collections.WeakMapChangeListener;
import javafx.event.Event;
import javafx.event.EventHandler;
import javafx.event.WeakEventHandler;

import java.beans.PropertyChangeListener;
import java.util.ArrayList;
import java.util.List;

public class WeakAdapter {
  private List<ChangeListener> changeListeners = new ArrayList<>();
  private List<EventHandler> eventHandlers = new ArrayList<>();
  private List<PropertyChangeListener> propertyChangeListeners = new ArrayList<>();
  private List<ListChangeListener> listChangeListeners = new ArrayList<>();
  private List<MapChangeListener> mapChangeListeners = new ArrayList<>();
  private List<InvalidationListener> invalidationListeners = new ArrayList<>();

  /**
   * 把ChangeListener转换成WeakChangeListener.
   * @param listener listener
   * @return 转换的WeakChangeListener
   */
  public <T> WeakChangeListener<T> wrap(ChangeListener<T> listener) {
    WeakChangeListener<T> weakChangeListener = new WeakChangeListener<>(listener);
    changeListeners.add(listener);
    return weakChangeListener;
  }

  /**
   * 把EventHandler转换成WeakEventHandler.
   * @param handler handler
   * @return 转换的WeakEventHandler
   */
  public <T extends Event> WeakEventHandler<T> wrap(EventHandler<T> handler) {
    WeakEventHandler<T> weakEventHandler = new WeakEventHandler<>(handler);
    eventHandlers.add(handler);
    return weakEventHandler;
  }

  /**
   * 把PropertyChangeListener转换成WeakPropertyChangeListener.
   * @param listener listener
   * @return 转换的WeakPropertyChangeListener
   */
  public WeakPropertyChangeListener wrap(PropertyChangeListener listener) {
    WeakPropertyChangeListener weakPropertyChangeListener =
        new WeakPropertyChangeListener(listener);
    propertyChangeListeners.add(listener);
    return weakPropertyChangeListener;
  }

  /**
   * 把ListChangeListener转换成WeakListChangeListener.
   * @param listChangeListener listener
   * @return 转换的WeakListChangeListener
   */
  public <T> WeakListChangeListener<T> wrap(ListChangeListener<T> listChangeListener) {
    WeakListChangeListener<T> weakListChangeListener =
        new WeakListChangeListener<>(listChangeListener);
    listChangeListeners.add(listChangeListener);
    return weakListChangeListener;
  }

  /**
   * 把InvalidationListener转换成WeakInvalidationListener.
   * @param listener listener
   * @return 转换的WeakInvalidationListener
   */
  public WeakInvalidationListener wrap(InvalidationListener listener) {
    WeakInvalidationListener weakInvalidationListener = new WeakInvalidationListener(listener);
    invalidationListeners.add(listener);
    return weakInvalidationListener;
  }
  
  /**
   * 把MapChangeListener转换成WeakMapChangeListener.
   * @param listener listener
   * @return 转换的WeakMapChangeListener
   */
  public <K, V> WeakMapChangeListener<K, V> wrap(MapChangeListener<K, V> listener) {
    WeakMapChangeListener<K, V> weakMapChangeListener = new WeakMapChangeListener<>(listener);
    mapChangeListeners.add(listener);
    return weakMapChangeListener;
  }
}
