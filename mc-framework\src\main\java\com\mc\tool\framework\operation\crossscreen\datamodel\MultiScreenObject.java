package com.mc.tool.framework.operation.crossscreen.datamodel;

import com.mc.tool.framework.systemedit.datamodel.VisualEditConnection;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import java.util.Collection;
import javafx.beans.property.IntegerProperty;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.StringProperty;
import javafx.collections.ObservableMap;

/**
 * .
 */
public interface MultiScreenObject {
  StringProperty getName();

  IntegerProperty getRows();

  IntegerProperty getColumns();

  Collection<ObjectProperty<VisualEditTerminal>> getTargets();

  ObjectProperty<VisualEditTerminal> getTarget(int index);

  ObservableMap<VisualEditTerminal, VisualEditConnection> getConnections();

  int indexOfTarget(VisualEditTerminal target);

  void insertTarget(VisualEditTerminal target, int index);

  <T extends MultiScreenObject> void copyTo(T item);
}
