package com.mc.graph.connector;

import com.google.gson.annotations.Expose;

import com.mc.graph.interfaces.ConnectorIdentifier;
import javafx.beans.property.ReadOnlyStringWrapper;
import javafx.beans.property.StringProperty;
import lombok.Getter;

public class IntegerConnectorIdentifier implements ConnectorIdentifier {
  @Getter
  @Expose
  private Integer value;

  @Override
  public int hashCode() {
    return value.hashCode();
  }

  public IntegerConnectorIdentifier(int value) {
    this.value = value;
  }

  @Override
  public boolean equals(Object obj) {
    if (obj instanceof IntegerConnectorIdentifier) {
      return ((IntegerConnectorIdentifier) obj).getValue().equals(getValue());
    }
    return false;
  }

  @Override
  public String toString() {
    return String.format("%03d", value);
  }

  @Override
  public StringProperty textProperty() {
    return new ReadOnlyStringWrapper(toString());
  }
}
