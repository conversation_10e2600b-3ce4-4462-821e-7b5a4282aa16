package com.mc.tool.framework.view;

import com.mc.tool.framework.OemInfo;
import com.mc.tool.framework.interfaces.ApplicationBase;
import com.mc.tool.framework.utility.I18nUtility;
import com.mc.tool.framework.utility.InjectorProvider;
import com.mc.tool.framework.utility.UndecoratedDialog;
import java.io.IOException;
import java.net.URL;
import java.util.ResourceBundle;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.scene.Node;
import javafx.scene.control.ButtonType;
import javafx.scene.control.Label;
import javafx.scene.layout.VBox;
import javafx.stage.Modality;
import javafx.stage.Window;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public final class AboutView implements Initializable {
  @FXML
  private Label companyLabel;
  @FXML
  private Label versionLabel;
  @FXML
  private Label javaVersionLabel;
  @FXML
  private Label copyRightLabel;

  @FXML
  private Label fullNameLabel;
  @FXML
  private VBox phoneBox;

  private Node view;

  /**
   * Constructor.
   */
  public AboutView() {
    FXMLLoader loader = new FXMLLoader(getClass().getResource(
        "/com/mc/tool/framework/aboutview.fxml"));

    try {
      loader.setController(this);
      view = (Node) loader.load();
    } catch (IOException exception) {
      log.warn("Fail to load aboutview.fxml!", exception);
    }
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {
    ApplicationBase base = InjectorProvider.getInjector().getInstance(ApplicationBase.class);
    if (base == null) {
      log.warn("ApplicationBase is null!");
      return;
    }

    OemInfo info = base.getOemInfo();
    if (info.getCompany() != null) {
      companyLabel.setText(info.getCompany());
    }
    if (info.getCopyright() != null) {
      copyRightLabel.setText(info.getCopyright());
    }
    if (info.getFullName() != null) {
      fullNameLabel.setText(info.getFullName());
    }
    if (info.getPhones() != null) {
      for (String phone : info.getPhones()) {
        Label phoneLabel = new Label();
        phoneLabel.setText(phone);
        phoneBox.getChildren().add(phoneLabel);
      }
    }

    String title = base.getAppTitle();
    if (info.getTitle() != null) {
      title = info.getTitle();
    }
    String version = String.format("%s v%s", title, base.getFullVersion());
    if (info.getVersionAppend() != null) {
      version += " " + info.getVersionAppend();
    }
    versionLabel.setText(version);

    javaVersionLabel
        .setText(String.format("Installed java version : %s", System.getProperty("java.version")));
  }

  public Node getView() {
    return view;
  }

  /**
   * 显示关于页面.
   */
  public static void show(Window owner) {
    UndecoratedDialog<ButtonType> dialog = new UndecoratedDialog<>();
    dialog.initModality(Modality.WINDOW_MODAL);
    dialog.initOwner(owner);
    dialog.setTitle(I18nUtility.getI18nBundle("main").getString("framework.menu.about.title"));
    dialog.getDialogPane().getButtonTypes().add(ButtonType.OK);

    AboutView view = new AboutView();
    dialog.getDialogPane().setContent(view.getView());
    dialog.showAndWait();
  }

}
