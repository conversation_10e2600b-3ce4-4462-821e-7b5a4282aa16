/* Style for decoration. */
@import "common.css";


* {
    HEADER-HEIGHT: 28;
    -fx-font-family: "Microsoft YaHei";
}

#decorationRoot {
    -fx-pref-height: HEADER-HEIGHT;
}

#title {
    -fx-text-fill: #ffffff;
}

.dialog-title-bar {
    -fx-min-height: 28;
    -fx-background-color: #333333;
}

.dialog-logo {
    -fx-background-image: url("./img/dialog_logo.png");
    -fx-background-position: center;
    -fx-background-size: stretch;
}

.dialog-pane {
    -fx-background-color: white;
}

.decoration-button-close {
    -fx-pref-width: 13;
    -fx-pref-height: 13;
    -fx-background-image: url("./img/close.png");
}

.decoration-button-close:hover {
    -fx-background-image: url("./img/close_hover.png");
}


.decorator-sub-container-left {
    -fx-pref-height: HEADER-HEIGHT;
    -fx-padding: 4 0 0 0;
    -fx-spacing: 7;
}

.decorator-sub-container-right {
    -fx-pref-height: HEADER-HEIGHT;
    -fx-padding: 10 0 0 0;
}

.dialog-pane .button {
    -fx-background-radius: 0;
    -fx-border-style: solid;
    -fx-border-size: 1;
    -fx-border-color: #cfcfcf;
    -fx-border-radius: 4;
    -fx-background-color: #f7f7f7;
    -fx-background-repeat: no-repeat;
}

.dialog-pane .button:hover {
    -fx-border-color: #f0ba84;
}

.dialog-pane .button:pressed {
    -fx-border-color: #f08519;
}

.dialog-pane .button:disabled {
    -fx-border-color: #cfcfcf;
    -fx-background-color: #e6e6e6;
}

.dialog-pane > .button-bar {
    -fx-border-width: 1 0 0 0;
    -fx-border-color: #e6e6e6;
}

.text-field:focused {
    -fx-background-color: -fx-shadow-highlight-color, -fx-text-box-border, -fx-control-inner-background;
    -fx-background-insets: 0, 1, 2;
    -fx-background-radius: 3, 2, 2;
    -fx-prompt-text-fill: transparent;
}
