package com.mc.tool.framework.systemedit.view;

import com.mc.tool.framework.systemedit.datamodel.VisualEditMatrix;
import com.mc.tool.framework.systemedit.datamodel.VisualEditNode;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import com.mc.tool.framework.utility.I18nUtility;
import com.mc.tool.framework.utility.UndecoratedAlert;
import com.mc.tool.framework.utility.UndecoratedDialog;
import com.mc.tool.framework.utility.UndecoratedHeavyWeightDialog;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Locale;
import java.util.Optional;
import java.util.function.Predicate;
import java.util.function.UnaryOperator;
import javafx.beans.binding.Bindings;
import javafx.geometry.Insets;
import javafx.scene.Node;
import javafx.scene.control.AlertEx.AlertExType;
import javafx.scene.control.ButtonType;
import javafx.scene.control.Control;
import javafx.scene.control.DialogEx;
import javafx.scene.control.FxDialogEx;
import javafx.scene.control.Label;
import javafx.scene.control.PasswordField;
import javafx.scene.control.TextField;
import javafx.scene.control.TextFormatter;
import javafx.scene.control.TextFormatter.Change;
import javafx.scene.control.TextInputDialogEx;
import javafx.scene.layout.GridPane;
import javafx.scene.layout.Priority;
import javafx.stage.FileChooser;
import javafx.stage.FileChooser.ExtensionFilter;
import javafx.stage.Modality;
import javafx.stage.Window;
import javax.xml.bind.DatatypeConverter;
import lombok.extern.slf4j.Slf4j;
import org.controlsfx.validation.ValidationResult;
import org.controlsfx.validation.ValidationSupport;
import org.controlsfx.validation.Validator;

/**
 * .
 */
@Slf4j
public class ViewUtility {
  /**
   * Show a dialog and input a name.
   *
   * @param initText initial name text to display
   * @return user inputed name
   */
  public static Optional<String> getNameFromDialog(Window owner, String initText) {
    return getNameFromDialog(owner, initText, null, null);
  }

  /**
   * Show a dialog and input a name.
   *
   * @param initText      initial name text to display
   * @param textFormatter text formatter
   * @param validation    text validator
   * @return user inputed name
   */
  public static Optional<String> getNameFromDialog(Window owner, String initText,
                                                   TextFormatter<?> textFormatter, Predicate<String> validation) {
    DialogEx<String> dialog = getTextDialog(owner, initText, textFormatter, validation);
    dialog.setTitle(
        I18nUtility.getI18nBundle("systemedit").getString("cell.wizard.nameconfig_title"));
    dialog.setContentText(
        I18nUtility.getI18nBundle("systemedit").getString("cell.wizard.nameconfig_content"));
    Optional<String> result = dialog.showAndWait();
    return result;
  }

  /**
   * 获取输入文本的对话框.
   *
   * @param owner         owner
   * @param initText      initial name text to display
   * @param textFormatter text formatter
   * @param validation    text validator
   * @return 对话框
   */
  public static DialogEx<String> getTextDialog(Window owner, String initText,
                                               TextFormatter<?> textFormatter, Predicate<String> validation) {
    TextInputDialogEx dialog = new TextInputDialogEx(initText) {
      @Override
      protected FxDialogEx createDialog() {
        return new UndecoratedHeavyWeightDialog(this);
      }
    };
    dialog.initOwner(owner);
    dialog.initModality(Modality.WINDOW_MODAL);
    dialog.setHeaderText(null);
    dialog.setGraphic(null);
    dialog.getDialogPane().setMinWidth(300);
    if (textFormatter != null) {
      dialog.getEditor().setTextFormatter(textFormatter);
    }
    if (validation != null) {
      Node node = dialog.getDialogPane().lookupButton(ButtonType.OK);
      node.disableProperty().bind(Bindings.createBooleanBinding(
          () -> !validation.test(dialog.getEditor().getText()), dialog.getEditor().textProperty()));
    }
    return dialog;
  }

  /**
   * 获取输入密码的对话框.
   *
   * @param owner owner
   * @return 对话框
   */
  public static DialogEx<String> getPasswordDialog(Window owner) {
    TextInputDialogEx dialog = new TextInputDialogEx() {
      @Override
      protected FxDialogEx createDialog() {
        return new UndecoratedHeavyWeightDialog(this);
      }

      @Override
      protected TextField createTextField(String initText) {
        return new PasswordField();
      }
    };
    dialog.initOwner(owner);
    dialog.initModality(Modality.WINDOW_MODAL);
    dialog.setHeaderText(null);
    dialog.setGraphic(null);
    dialog.getDialogPane().setMinWidth(300);
    return dialog;
  }

  /**
   * 显示确认提示框.
   *
   * @param owner   父窗口
   * @param title   标题
   * @param content 确认内容
   * @return 确认结果
   */
  public static Optional<ButtonType> getConfirmResultFromDialog(Window owner, String title,
                                                                String content) {
    UndecoratedDialog<ButtonType> dialog = new UndecoratedDialog<>();
    dialog.initOwner(owner);
    dialog.initModality(Modality.WINDOW_MODAL);
    dialog.setTitle(title);
    dialog.setHeaderText(null);
    dialog.setContentText(content);
    dialog.getDialogPane().getButtonTypes().addAll(ButtonType.YES, ButtonType.NO);
    return dialog.showAndWait();
  }

  /**
   * 获取系统编辑的部件的logo.
   *
   * @param node node
   * @return logo的url
   */
  public static String getLogoUrl(VisualEditNode node) {
    String defaultUrl =
        SystemeditConstants.RESOURCE_PATH + SystemeditConstants.COMPUTER_ONLINE_LOGO;
    if (node instanceof VisualEditMatrix) {
      return SystemeditConstants.RESOURCE_PATH + SystemeditConstants.MATRIX_ONLINE_LOGO;
    } else if (node instanceof VisualEditTerminal) {
      VisualEditTerminal terminal = (VisualEditTerminal) node;
      switch (terminal.getTargetDeviceType()) {
        case DVD:
          return SystemeditConstants.RESOURCE_PATH + SystemeditConstants.DVD_ONLINE_LOGO;
        case COMPUTER:
          return SystemeditConstants.RESOURCE_PATH + SystemeditConstants.COMPUTER_ONLINE_LOGO;
        case MONITOR:
          return SystemeditConstants.RESOURCE_PATH + SystemeditConstants.MONITOR_ONLINE_LOGO;
        case PROJECTOR:
          return SystemeditConstants.RESOURCE_PATH + SystemeditConstants.PROJECTOR_ONLINE_LOGO;
        default:
          return defaultUrl;
      }
    } else {
      return defaultUrl;
    }
  }

  /**
   * 显示提示信息.
   *
   * @param window owner
   * @param text   显示文本
   * @param title  标题
   * @param type   提示类型
   */
  public static void showAlert(Window window, String text, String title, AlertExType type) {
    UndecoratedAlert alert = new UndecoratedAlert(type);
    alert.initOwner(window);
    alert.getDialogPane().setContentText(text);
    alert.setTitle(title);
    alert.showAndWait();
  }

  /**
   * 显示提示信息.
   *
   * @param window owner
   * @param text   显示文本
   * @param type   提示类型
   */
  public static void showAlert(Window window, String text, AlertExType type) {
    showAlert(window, text, "Warning", type);
  }

  /**
   * 弹出文件保存对话框，保存输入的edid字符串.
   *
   * @param window 父window
   * @param text   要保存的edid字符串
   */
  public static void saveEdid(Window window, String text) {
    FileChooser chooser = new FileChooser();
    chooser.setInitialFileName("HDMIMonitor");
    chooser.getExtensionFilters().add(new ExtensionFilter("EDID files (*.edid)", "*.edid"));
    File file = chooser.showSaveDialog(window);
    if (file != null) {
      FileOutputStream fileOutputStream = null;
      try {
        fileOutputStream = new FileOutputStream(file);
        fileOutputStream.write(DatatypeConverter.parseHexBinary(text));
      } catch (IOException exp) {
        log.warn("Can not write the file : {}", file.getAbsolutePath());
      } finally {
        if (fileOutputStream != null) {
          try {
            fileOutputStream.close();
          } catch (IOException exc) {
            log.warn("Can not close the file output stream!");
          }
        }
      }
    }
  }

  /**
   * 弹出文件选择对话框选择edid文件加载.
   *
   * @param window 父window
   * @return 如果加载成功，返回加载到的字符串，否则返回null
   */
  public static String loadEdid(Window window) {
    FileChooser chooser = new FileChooser();
    chooser.getExtensionFilters().add(new ExtensionFilter("EDID files (*.edid)", "*.edid"));
    File file = chooser.showOpenDialog(window);
    if (file != null) {
      FileInputStream fis = null;
      try {
        fis = new FileInputStream(file);
        byte[] data = new byte[256];
        int readedlength = fis.read(data);
        if (readedlength != 256) {
          UndecoratedAlert alert = new UndecoratedAlert(AlertExType.WARNING);
          alert.initOwner(window);
          alert.setContentText("Error edid file!");
          alert.showAndWait();
        } else {
          return DatatypeConverter.printHexBinary(data).toLowerCase(Locale.US);
        }
      } catch (IOException exception) {
        log.warn("Fail to load edid!", exception);
      } finally {
        if (fis != null) {
          try {
            fis.close();
          } catch (IOException exception) {
            log.warn("Fail to close the file input stream!", exception);
          }
        }
      }
    }
    return null;
  }

  /**
   * 获取设置IP地址、子网掩码和网关的对话框.
   *
   * @param owner 父窗口
   * @param defaultIp 默认IP地址
   * @param defaultSubnetMask 默认子网掩码
   * @param defaultGateway 默认网关
   * @return 对话框
   */
  public static DialogEx<NetworkConfigInfo> getNetworkConfigDialog(Window owner, String defaultIp,
                                                                 String defaultSubnetMask,
                                                                 String defaultGateway) {
    // 创建对话框
    UndecoratedDialog<NetworkConfigInfo> dialog = new UndecoratedDialog<>();
    dialog.initOwner(owner);
    dialog.initModality(Modality.WINDOW_MODAL);
    dialog.setTitle(I18nUtility.getI18nBundle("systemedit").getString("network.config.title"));
    dialog.setHeaderText(null);
    dialog.setGraphic(null);

    // 创建网格布局
    GridPane grid = new GridPane();
    grid.setHgap(10);
    grid.setVgap(10);
    grid.setPadding(new Insets(20, 150, 10, 10));

    // IP地址输入框
    TextField ipTextField = new TextField(defaultIp);
    ipTextField.setMaxWidth(Double.MAX_VALUE);
    ipTextField.setId("ip-input");
    GridPane.setHgrow(ipTextField, Priority.ALWAYS);

    // 子网掩码输入框
    TextField subnetTextField = new TextField(defaultSubnetMask);
    subnetTextField.setMaxWidth(Double.MAX_VALUE);
    subnetTextField.setId("subnet-input");
    GridPane.setHgrow(subnetTextField, Priority.ALWAYS);

    // 网关输入框
    TextField gatewayTextField = new TextField(defaultGateway);
    gatewayTextField.setMaxWidth(Double.MAX_VALUE);
    gatewayTextField.setId("gateway-input");
    GridPane.setHgrow(gatewayTextField, Priority.ALWAYS);

    // IP地址标签
    Label ipLabel = new Label(I18nUtility.getI18nBundle("systemedit").getString("network.config.ip"));

    // 子网掩码标签
    Label subnetLabel = new Label(I18nUtility.getI18nBundle("systemedit").getString("network.config.subnet"));

    // 网关标签
    Label gatewayLabel = new Label(I18nUtility.getI18nBundle("systemedit").getString("network.config.gateway"));

    // 添加到网格
    grid.add(ipLabel, 0, 0);
    grid.add(ipTextField, 1, 0);
    grid.add(subnetLabel, 0, 1);
    grid.add(subnetTextField, 1, 1);
    grid.add(gatewayLabel, 0, 2);
    grid.add(gatewayTextField, 1, 2);

    // 设置IP地址格式验证
    ValidationSupport validationSupport = new ValidationSupport();
    validationSupport.setValidationDecorator(null);

    // IP地址验证器
    validationSupport.registerValidator(ipTextField, new IpValidator());
    validationSupport.registerValidator(subnetTextField, new IpValidator());
    validationSupport.registerValidator(gatewayTextField, new IpValidator());

    // 设置IP地址输入过滤器
    UnaryOperator<Change> ipFilter = change -> {
      if (!change.getText().matches("((\\.)|[0-9])*")) {
        change.setText("");
      }
      return change;
    };

    ipTextField.setTextFormatter(new TextFormatter<>(ipFilter));
    subnetTextField.setTextFormatter(new TextFormatter<>(ipFilter));
    gatewayTextField.setTextFormatter(new TextFormatter<>(ipFilter));

    // 设置对话框内容
    dialog.getDialogPane().setContent(grid);

    // 添加按钮
    dialog.getDialogPane().getButtonTypes().addAll(ButtonType.OK, ButtonType.CANCEL);

    // 绑定确定按钮状态
    Node okButton = dialog.getDialogPane().lookupButton(ButtonType.OK);
    okButton.disableProperty().bind(validationSupport.invalidProperty());

    // 设置结果转换器
    dialog.setResultConverter(dialogButton -> {
      if (dialogButton == ButtonType.OK) {
        // 显示重启确认对话框
        UndecoratedDialog<ButtonType> confirmDialog = new UndecoratedDialog<>();
        confirmDialog.initOwner(owner);
        confirmDialog.initModality(Modality.WINDOW_MODAL);
        confirmDialog.setTitle(I18nUtility.getI18nBundle("systemedit").getString("network.config.restart_confirm.title"));
        confirmDialog.setHeaderText(null);
        confirmDialog.setContentText(I18nUtility.getI18nBundle("systemedit").getString("network.config.restart_confirm.message"));
        confirmDialog.getDialogPane().getButtonTypes().addAll(ButtonType.YES, ButtonType.NO);

        Optional<ButtonType> result = confirmDialog.showAndWait();
        if (result.isPresent() && result.get() == ButtonType.YES) {
          return new NetworkConfigInfo(
              ipTextField.getText(),
              subnetTextField.getText(),
              gatewayTextField.getText());
        } else {
          return null;
        }
      }
      return null;
    });

    return dialog;
  }

  /**
   * IP地址验证器.
   */
  private static class IpValidator implements Validator<String> {
    private static final String IP_REGEX = "((2(5[0-5]|[0-4]\\d))|[0-1]?\\d{1,2})"
        + "(\\.((2(5[0-5]|[0-4]\\d))|[0-1]?\\d{1,2})){3}";

    @Override
    public ValidationResult apply(Control target, String value) {
      return ValidationResult.fromErrorIf(
          target,
          I18nUtility.getI18nBundle("systemedit").getString("network.config.ip_format_error"),
          !value.matches(IP_REGEX));
    }
  }
}
