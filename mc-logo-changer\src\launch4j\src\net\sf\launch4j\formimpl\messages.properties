#
#	Launch4j (http://launch4j.sourceforge.net/)
#	Cross-platform Java application wrapper for creating Windows native executables.
#
#	Copyright (c) 2004, 2015 <PERSON><PERSON><PERSON><PERSON>
#	All rights reserved.
#
#	Redistribution and use in source and binary forms, with or without modification,
#	are permitted provided that the following conditions are met:
#	
#	1. Redistributions of source code must retain the above copyright notice,
#	   this list of conditions and the following disclaimer.
#	
#	2. Redistributions in binary form must reproduce the above copyright notice,
#	   this list of conditions and the following disclaimer in the documentation
#	   and/or other materials provided with the distribution.
#	
#	3. Neither the name of the copyright holder nor the names of its contributors
#	   may be used to endorse or promote products derived from this software without
#	   specific prior written permission.
#	
#	THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
#	AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
#	THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
#	ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE
#	FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
#	(INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
#	LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED
#	AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
#	OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
#	OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
#

tab.basic=Basic
tab.classpath=Classpath
tab.header=Header
tab.singleInstance=Single instance
tab.jre=JRE
tab.envVars=Set env. variables
tab.splash=Splash
tab.version=Version Info
tab.messages=Messages

# Basic
jar=Jar:
jarPath=Jar runtime path:
jarTip=Application jar.
jarPathTip=Optional runtime path of the jar relative to the executable. For example, if the executable launcher and the application jar named calc.exe and calc.jar are in the same directory, it would be: calc.jar.

# Classpath
specifyClassPath=Specify classpath item to add.
confirmClassPathRemoval=Remove selected classpath items?
noManifest=The selected jar does not have a manifest.

# JRE
specifyVar=Specify environment variable to add.
otherVar=Other var

jdkPreference.jre.only=Only use public JREs
jdkPreference.prefer.jre=Prefer public JRE, but use JDK runtime if newer
jdkPreference.prefer.jdk=Prefer JDK runtime, but use public JRE if newer
jdkPreference.jdk.only=Only use private JDK runtimes

runtimeBits.64=64-bit only
runtimeBits.64And32=First 64-bit, then 32-bit
runtimeBits.32And64=First 32-bit, then 64-bit
runtimeBits.32=32-bit only

MainFrame.config.files=launch4j config files (.xml, .cfg)
MainFrame.new.config=New configuration
MainFrame.open.config=Open configuration or import 1.x
MainFrame.save.config=Save configuration
MainFrame.build.wrapper=Build wrapper
MainFrame.test.wrapper=Test wrapper
MainFrame.about.launch4j=About launch4j
MainFrame.discard.changes=Discard changes?
MainFrame.confirm=Confirm
MainFrame.untitled=untitled
MainFrame.executing=Executing: 
MainFrame.jar.integrity.test=Jar integrity test, executing: 
