package com.mc.tool.framework.office.menu;

import com.mc.tool.framework.office.controller.OfficeControllable;
import com.mc.tool.framework.utility.I18nUtility;
import javafx.scene.control.MenuItem;

/**
 * .
 */
public class MenuCreateTest extends MenuItem {
  private OfficeControllable controllable;

  /**
   * Constructor.
   *
   * @param controllable controllable
   */
  public MenuCreateTest(OfficeControllable controllable) {
    this.controllable = controllable;
    //this.disableProperty().bind(new DisableBinding(controllable));
    this.setOnAction((event) -> {
      onAction();
    });
    this.setText(I18nUtility.getI18nBundle("office").getString("menu.create_test"));
  }

  private void onAction() {
    controllable.createTestData();
  }

}
