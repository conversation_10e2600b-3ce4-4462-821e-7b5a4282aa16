package com.mc.tool.caesar.vpm;

import com.google.gson.GsonBuilder;
import com.mc.common.io.StringInputStream;
import com.mc.common.util.WeakAdapter;
import com.mc.common.util.ZipUtility;
import com.mc.tool.caesar.api.CaesarSwitchDataModel;
import com.mc.tool.caesar.api.datamodel.MatrixDefinitionData;
import com.mc.tool.caesar.api.datamodel.MultiScreenData;
import com.mc.tool.caesar.api.datamodel.MultiScreenData.MultiScreenConInfo;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.utils.Utilities;
import com.mc.tool.caesar.vpm.devices.CaesarDemoDeviceController;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.devices.CaesarUserRightGetter;
import com.mc.tool.caesar.vpm.pages.extenderosdupdate.CaesarExtenderOsdUpdatePage;
import com.mc.tool.caesar.vpm.pages.extenderupdate.CaesarExtenderUpdatePage;
import com.mc.tool.caesar.vpm.pages.hostconfiguration.CaesarHostConfigurationPage;
import com.mc.tool.caesar.vpm.pages.hostupdate.CaesarHostUpdatePage;
import com.mc.tool.caesar.vpm.pages.hotkeyconfiguration.CaesarHotkeyConfigurationPage;
import com.mc.tool.caesar.vpm.pages.office.CaesarOfficePage;
import com.mc.tool.caesar.vpm.pages.operation.CaesarOperationPageNew;
import com.mc.tool.caesar.vpm.pages.permissionconfiguration.CaesarPermissionConfigurationPage;
import com.mc.tool.caesar.vpm.pages.splicingscreen.CaesarSplicingScreenPage;
import com.mc.tool.caesar.vpm.pages.systemedit.CaesarSystemEditPage;
import com.mc.tool.caesar.vpm.util.CaesarI18nCommonResource;
import com.mc.tool.caesar.vpm.util.CaesarModelJsonBuilder;
import com.mc.tool.caesar.vpm.util.DkmHttpApi;
import com.mc.tool.caesar.vpm.util.SaveStatusUtility;
import com.mc.tool.caesar.vpm.view.ConfigListView;
import com.mc.tool.framework.AbstractVisualEditEntity;
import com.mc.tool.framework.OemInfo;
import com.mc.tool.framework.interfaces.ApplicationBase;
import com.mc.tool.framework.interfaces.Page;
import com.mc.tool.framework.utility.EntityUtility;
import com.mc.tool.framework.utility.InjectorProvider;
import com.mc.tool.framework.utility.TypeWrapper;
import com.mc.tool.framework.utility.UndecoratedAlert;
import com.mc.tool.framework.utility.dialogues.FileDialogue;
import com.mc.tool.framework.utility.dialogues.FileDialogueFactory;
import com.mc.tool.framework.view.OnlineStatus;
import java.awt.image.BufferedImage;
import java.io.BufferedOutputStream;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStreamWriter;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.util.zip.ZipOutputStream;
import javafx.application.Platform;
import javafx.beans.binding.Bindings;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.concurrent.Task;
import javafx.embed.swing.SwingFXUtils;
import javafx.geometry.Pos;
import javafx.scene.Node;
import javafx.scene.control.AlertEx;
import javafx.scene.control.AlertEx.AlertExType;
import javafx.scene.control.Label;
import javafx.scene.image.Image;
import javafx.scene.layout.Background;
import javafx.scene.layout.BackgroundImage;
import javafx.scene.layout.BackgroundPosition;
import javafx.scene.layout.BackgroundRepeat;
import javafx.scene.layout.HBox;
import javafx.scene.text.TextAlignment;
import javafx.stage.FileChooser;
import javafx.stage.FileChooser.ExtensionFilter;
import javafx.util.Pair;
import javax.imageio.ImageIO;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * Caesar设备实体类，负责管理系统状态、配置和页面，提供导出系统图、状态保存、日志导出等功能.
 */
@Slf4j
public class CaesarEntity extends AbstractVisualEditEntity {
  public static final String MENU_DOWNLOAD = "menu.download";
  public static final String MENU_UPLOAD = "menu.upload";
  public static final String MENU_SAVE = "menu.save";
  public static final String MENU_REFRESH = "menu.refresh";
  private static final String EXPORT_SYSTEM_GRAPH = "export.graph";
  private static final String EXPORT_STATUS = "export.status";
  private static final String EXPORT_MULTI_SCREEN = "export.multiscreen";

  private static final String EXPORT_STATUS_AND_LOG = "export.status_and_log";

  private List<Page> pages = new ArrayList<>();
  @Getter
  private CaesarDeviceController controller;

  private ObservableList<Node> rightStatus = FXCollections.observableArrayList();

  private final CaesarSystemEditPage systemEditPage;

  private String filePath;

  private boolean refreshing = false;
  private final WeakAdapter weakAdapter = new WeakAdapter();

  private final ObjectProperty<Background> activeBgProperty = new SimpleObjectProperty<>(null);
  private final ObjectProperty<Background> backupBgProperty = new SimpleObjectProperty<>(null);

  private static final String ACTIVATE_IMAGE = "com/mc/tool/caesar/vpm/img/online.png";
  private static final String UNACTIVATED_IMAGE = "com/mc/tool/caesar/vpm/img/offline.png";
  private static final Image ACTIVATE_BG_IMG;
  private static final Image UNACTIVATED_BG_IMG;
  private static final Background ACTIVATE_BG;
  private static final Background UNACTIVATED_BG;
  private static final String BACKUP_IMAGE = "com/mc/tool/caesar/vpm/img/backup.png";
  private static final Image BACKUP_BG_IMG;
  private static final Background BACKUP_BG;

  static {
    ClassLoader loader = Thread.currentThread().getContextClassLoader();
    ACTIVATE_BG_IMG = new Image(Objects.requireNonNull(loader.getResourceAsStream(ACTIVATE_IMAGE)));
    UNACTIVATED_BG_IMG =
        new Image(Objects.requireNonNull(loader.getResourceAsStream(UNACTIVATED_IMAGE)));
    ACTIVATE_BG =
        new Background(
            new BackgroundImage(
                ACTIVATE_BG_IMG,
                BackgroundRepeat.NO_REPEAT,
                BackgroundRepeat.NO_REPEAT,
                BackgroundPosition.CENTER,
                null));
    UNACTIVATED_BG =
        new Background(
            new BackgroundImage(
                UNACTIVATED_BG_IMG,
                BackgroundRepeat.NO_REPEAT,
                BackgroundRepeat.NO_REPEAT,
                BackgroundPosition.CENTER,
                null));
    BACKUP_BG_IMG = new Image(Objects.requireNonNull(loader.getResourceAsStream(BACKUP_IMAGE)));
    BACKUP_BG =
        new Background(
            new BackgroundImage(
                BACKUP_BG_IMG,
                BackgroundRepeat.NO_REPEAT,
                BackgroundRepeat.NO_REPEAT,
                BackgroundPosition.CENTER,
                null));
  }

  /**
   * Constructor.
   */
  public CaesarEntity(CaesarDeviceController controller) {
    this.controller = controller;
    controller.init();
    deserializeAndInit();
    CaesarUserRightGetter userRightGetter = controller.getCaesarUserRight();
    pages.add(systemEditPage = new CaesarSystemEditPage(this));
    if (userRightGetter.isPageVisible(CaesarOfficePage.NAME)) {
      pages.add(new CaesarOfficePage(this));
    }
    if (userRightGetter.isPageVisible(CaesarOperationPageNew.NAME)) {
      pages.add(new CaesarOperationPageNew(this));
    }
    if (userRightGetter.isPageVisible(CaesarHostConfigurationPage.NAME)) {
      pages.add(new CaesarHostConfigurationPage(this));
    }
    if (userRightGetter.isPageVisible(CaesarPermissionConfigurationPage.NAME)) {
      pages.add(new CaesarPermissionConfigurationPage(this));
    }
    if (userRightGetter.isPageVisible(CaesarHotkeyConfigurationPage.NAME)) {
      pages.add(new CaesarHotkeyConfigurationPage(this));
    }
    if (userRightGetter.isPageVisible(CaesarSplicingScreenPage.NAME)) {
      pages.add(new CaesarSplicingScreenPage(this));
    }
    if (userRightGetter.isPageVisible(CaesarHostUpdatePage.NAME)) {
      pages.add(new CaesarHostUpdatePage(this));
    }
    if (userRightGetter.isPageVisible(CaesarExtenderUpdatePage.NAME)) {
      pages.add(new CaesarExtenderUpdatePage(this));
    }
    if (userRightGetter.isPageVisible(CaesarExtenderUpdatePage.NAME)) {
      pages.add(new CaesarExtenderOsdUpdatePage(this));
    }

    if (!controller.isDemo()) {
      // 添加在线状态
      Label label = new Label("(" + controller.getLoginUser() + ")");
      label.setTextAlignment(TextAlignment.CENTER);
      label.setAlignment(Pos.CENTER);
      HBox userBox = new HBox();
      userBox.getChildren().add(label);
      userBox.setAlignment(Pos.CENTER);

      rightStatus.add(new OnlineStatus(controller.onlineProperty()));
      rightStatus.add(userBox);

      controller
          .getHasMatrixInfo()
          .addListener(
              weakAdapter.wrap(
                  (obs, oldVal, newVal) -> {
                    if (!oldVal.equals(newVal) && newVal.equals(true)) {
                      activeBgProperty.unbind();
                      backupBgProperty.unbind();
                      activeBgProperty.bind(
                          Bindings.when(controller.getActive().and(controller.onlineProperty()))
                              .then(ACTIVATE_BG)
                              .otherwise(UNACTIVATED_BG));
                      backupBgProperty.bind(
                          Bindings.when(controller.getBackup())
                              .then(BACKUP_BG)
                              .otherwise(Background.EMPTY));
                    } else if (!oldVal.equals(newVal) && newVal.equals(false)) {
                      activeBgProperty.unbind();
                      backupBgProperty.unbind();
                      activeBgProperty.set(null);
                      backupBgProperty.set(null);
                    }
                  }));
    }
  }

  @Override
  public String getName() {
    OemInfo info = InjectorProvider.getInjector().getInstance(ApplicationBase.class).getOemInfo();
    String product = "Caesar";
    if (info.getProductName() != null) {
      product = info.getProductName();
    }

    return String.format("%s(%s)", product, controller.getDefaultIp());
  }

  @Override
  public String getType() {
    return EntityUtility.DEVICE_TYPE + "caesar";
  }

  @Override
  public Collection<Page> getPages() {
    return pages;
  }

  @Override
  public boolean isImportable() {
    return false;
  }

  @Override
  public boolean exports(String type, String path) {
    switch (type) {
      case EXPORT_SYSTEM_GRAPH:
        return exportSystemGraph();
      case EXPORT_STATUS:
        return saveStatus();
      case EXPORT_STATUS_AND_LOG:
        return saveStatusAndLog();
      case EXPORT_MULTI_SCREEN:
        return exportMultiscreen();
      default:
        return false;
    }
  }

  @Override
  public boolean imports(String path) {
    return false;
  }

  @Override
  public ObservableList<Node> getLeftStatus() {
    return FXCollections.emptyObservableList();
  }

  @Override
  protected GsonBuilder createModelSerializationBuilder() {
    return CaesarModelJsonBuilder.getBuilder();
  }

  @Override
  public ObservableList<Node> getRightStatus() {
    return rightStatus;
  }

  @Override
  public ObjectProperty<Background> getActiveBgProperty() {
    return activeBgProperty;
  }

  @Override
  public ObjectProperty<Background> getBackupBgProperty() {
    return backupBgProperty;
  }

  @Override
  public Collection<Pair<String, Collection<TypeWrapper>>> getMenuGroup() {
    TypeWrapper downloadWrapper =
        new TypeWrapper(
            CaesarI18nCommonResource.getString("caesar_menu.download"), MENU_DOWNLOAD, "");
    TypeWrapper uploadWrapper =
        new TypeWrapper(CaesarI18nCommonResource.getString("caesar_menu.upload"), MENU_UPLOAD, "");
    TypeWrapper saveWrapper =
        new TypeWrapper(CaesarI18nCommonResource.getString("caesar_menu.save"), MENU_SAVE, "");
    TypeWrapper refresh =
        new TypeWrapper(
            CaesarI18nCommonResource.getString("caesar_menu.refresh"), MENU_REFRESH, "");

    List<TypeWrapper> typeWrappers = new ArrayList<>();
    typeWrappers.add(downloadWrapper);
    typeWrappers.add(uploadWrapper);
    typeWrappers.add(saveWrapper);
    typeWrappers.add(refresh);

    List<Pair<String, Collection<TypeWrapper>>> result = new ArrayList<>();
    result.add(
        new Pair<>(CaesarI18nCommonResource.getString("caesar_menu.configuration"), typeWrappers));
    return result;
  }

  @Override
  public void onMenu(TypeWrapper menuType) {
    if (menuType.getType().equals(MENU_DOWNLOAD)) {
      ConfigListView.show(this, controller, true);
    } else if (menuType.getType().equals(MENU_UPLOAD)) {
      ConfigListView.show(this, controller, false);
    } else if (menuType.getType().equals(MENU_SAVE)) {
      ApplicationBase app = InjectorProvider.getInjector().getInstance(ApplicationBase.class);
      if (app != null) {
        app.getTaskManager().addForegroundTask(new Task<Void>() {
          @Override
          protected Void call() {
            updateMessage(CaesarI18nCommonResource.getString("page.saving"));
            controller.execute(
                () -> {
                  try {
                    serialize(); // 保存model
                    controller.getDataModel().save();
                  } catch (ConfigException | BusyException cex) {
                    log.error("Save online changes failed", cex);
                  } catch (Exception ex) {
                    log.error("Save online changes failed", ex);
                  }
                });
            return null;
          }
        });
      }
    } else if (menuType.getType().equals(MENU_REFRESH)) {
      if (refreshing) {
        return;
      }
      ApplicationBase app = InjectorProvider.getInjector().getInstance(ApplicationBase.class);
      Task<Void> task =
          new Task<Void>() {
            @Override
            protected Void call() {
              refreshing = true;
              try {
                updateMessage(CaesarI18nCommonResource.getString("page.refreshing"));
                for (Page page : pages) {
                  if (page.getVisibleProperty().get()) {
                    page.refresh();
                  }
                }
              } finally {
                refreshing = false;
              }
              return null;
            }
          };
      app.getTaskManager().addForegroundTask(task);
    }
  }

  @Override
  public Collection<TypeWrapper> getExportableTypes() {
    List<TypeWrapper> wrappers = new ArrayList<>();
    wrappers.add(
        new TypeWrapper(
            CaesarI18nCommonResource.getString("export.graph"), EXPORT_SYSTEM_GRAPH, ""));
    wrappers.add(
        new TypeWrapper(CaesarI18nCommonResource.getString("export.status"), EXPORT_STATUS, ""));
    wrappers.add(new TypeWrapper(CaesarI18nCommonResource.getString("export.status_and_log"),
        EXPORT_STATUS_AND_LOG, ""));

    return wrappers;
  }

  protected boolean exportSystemGraph() {
    FileDialogueFactory factory =
        InjectorProvider.getInjector().getInstance(FileDialogueFactory.class);
    FileDialogue fileDialogue = factory.createFileDialogue();
    fileDialogue.addExtensionFilter(new ExtensionFilter("PNG", "*.png"));
    File file =
        fileDialogue.showSaveDialog(
            InjectorProvider.getInjector().getInstance(ApplicationBase.class).getMainWindow());
    if (file == null) {
      return true;
    }

    Image image = systemEditPage.getSystemGraphImage();
    BufferedImage bufferedImage = SwingFXUtils.fromFXImage(image, null);
    try {
      log.info(String.format("User(%s) export system graph, path: %s.", controller.getLoginUser(), file.getAbsolutePath()));
      ImageIO.write(bufferedImage, "png", file);
    } catch (IOException exception) {
      log.warn("Fail to export system graph!", exception);
    }
    return true;
  }

  protected boolean exportMultiscreen() {
    // 参数校验
    if (getController() == null || getController().getDataModel() == null 
        || getController().getDataModel().getConfigData() == null) {
      log.warn("Invalid data model state for multiscreen export");
      return false;
    }

    // 获取保存文件路径
    File file = getSaveFilePath();
    if (file == null) {
      return true; // 用户取消操作
    }

    // 记录导出操作
    log.info("User({}) export multiscreen data, path:{}.", controller.getLoginUser(),
        file.getAbsolutePath());

    // 执行导出操作
    return writeMultiscreenData(file);
  }

  private File getSaveFilePath() {
    FileDialogueFactory factory = InjectorProvider.getInjector().getInstance(FileDialogueFactory.class);
    FileDialogue fileDialogue = factory.createFileDialogue();
    fileDialogue.addExtensionFilter(new ExtensionFilter("CSV", "*.csv"));
    return fileDialogue.showSaveDialog(
        InjectorProvider.getInjector().getInstance(ApplicationBase.class).getMainWindow());
  }

  private boolean writeMultiscreenData(File file) {
    try (BufferedWriter writer = new BufferedWriter(
        new OutputStreamWriter(Files.newOutputStream(file.toPath()), StandardCharsets.UTF_8))) {
      // 写入CSV头
      writer.write("name,status,horz,vert,ctrlid,coninfos\n");

      // 写入数据行
      for (MultiScreenData data : getController().getDataModel().getConfigData().getMultiScreenDatas()) {
        writer.write(formatMultiscreenDataLine(data));
      }
      return true;
    } catch (IOException exception) {
      log.warn("Failed to export multiscreen data", exception);
      return false;
    }
  }

  private String formatMultiscreenDataLine(MultiScreenData data) {
    if (data == null) {
      return "";
    }
    return new StringBuilder()
        .append(data.getName()).append(",")
        .append(data.getStatus()).append(",")
        .append(data.getHorizontalNumber()).append(",")
        .append(data.getVerticalNumber()).append(",")
        .append(data.getCtrlConId()).append(",")
        .append(formatConInfos(data.getConInfos()))
        .append("\n")
        .toString();
  }

  private String formatConInfos(Collection<MultiScreenConInfo> conInfos) {
    if (conInfos == null || conInfos.isEmpty()) {
      return "";
    }
    return conInfos.stream()
        .map(info -> String.valueOf(info.getId()))
        .collect(Collectors.joining(" "));
  }

  /**
   * 保存系统状态.
   *
   * @return 保存是否成功
   */
  protected boolean saveStatus() {
    if (controller == null || visualEditModel == null) {
      log.warn("Controller or visualEditModel is null");
      return false;
    }

    FileChooser chooser = new FileChooser();
    chooser.getExtensionFilters().add(new ExtensionFilter("Status", "*.status"));
    ApplicationBase base = InjectorProvider.getInjector().getInstance(ApplicationBase.class);
    File file = chooser.showSaveDialog(base.getMainWindow());
    if (file == null) {
      return false;
    }

    base.getTaskManager().addForegroundTask(new Task<Void>() {
      @Override
      protected Void call() {
        updateMessage(CaesarI18nCommonResource.getString("export.status.saving"));
        log.info("User({}) saved system status, path:{}.",
            controller.getLoginUser(),
            file.getAbsolutePath());

        String model = createModelSerializationBuilder().create().toJson(visualEditModel);
        boolean result = saveStatusToFile(file, model);

        showSaveResult(result);
        return null;
      }
    });

    return true;
  }

  /**
   * 导出系统状态和日志.
   *
   * @return 保存是否成功
   */
  protected boolean saveStatusAndLog() {
    if (controller == null || visualEditModel == null) {
      log.warn("Controller or visualEditModel is null");
      return false;
    }

    // 获取保存文件
    File file = showSaveFileDialog();
    if (file == null) {
      return false;
    }

    // 准备模型数据
    String model = createModelSerializationBuilder().create().toJson(visualEditModel);

    // 添加保存任务
    ApplicationBase base = InjectorProvider.getInjector().getInstance(ApplicationBase.class);
    base.getTaskManager().addForegroundTask(createSaveStatusAndLogTask(file, model));
    return true;
  }

  /**
   * 创建保存状态和日志的任务.

   * @param targetFile 目标文件
   * @param model 系统状态模型数据
   * @return 后台任务对象
   */
  private Task<Object> createSaveStatusAndLogTask(File targetFile, String model) {
    return new Task<Object>() {
      @Override
      protected Object call() throws Exception {
        updateMessage(CaesarI18nCommonResource.getString("export.status.saving"));
        Path tempDir = null;
        try {
          // 创建临时目录
          tempDir = Files.createTempDirectory("status");
          // 保存状态和日志
          boolean result = saveStatusAndLogsToTemp(tempDir, model);
          if (result) {
            // 打包文件
            zipTempDirectoryToFile(tempDir, targetFile);
          }
          showSaveResult(result);
        } finally {
          // 异步清理临时目录
          if (tempDir != null) {
            Path finalTempDir = tempDir;
            CompletableFuture.runAsync(() -> {
              try {
                deleteDirectory(finalTempDir);
                log.debug("Temp directory cleaned: {}", finalTempDir);
              } catch (Exception ex) {
                log.error("Failed to clean temp directory", ex);
              }
            }).thenRun(() -> log.info("Async cleanup completed"));
          }
        }
        return null;
      }
    };
  }

  private void deleteDirectory(Path directory) {
    try {
      Files.walk(directory)
          .sorted((a, b) -> -a.compareTo(b))
          .forEach(path -> {
            try {
              Files.delete(path);
            } catch (IOException e) {
              log.error("Failed to delete: {}", path, e);
            }
          });
    } catch (IOException e) {
      log.error("Failed to clean up temp directory: {}", directory, e);
    }
  }

  private File showSaveFileDialog() {
    FileChooser chooser = new FileChooser();
    String systemName = getController().getDataModel().getConfigData()
        .getSystemConfigData().getSystemData().getName();
    String timestamp = new SimpleDateFormat("yyyyMMddhhmmssSSS").format(new Date());
    String initName = String.format("%s_%s_status_and_log", systemName, timestamp);

    chooser.setInitialFileName(initName);
    chooser.getExtensionFilters().add(new ExtensionFilter("Zip", "*.zip"));
    return chooser.showSaveDialog(
        InjectorProvider.getInjector().getInstance(ApplicationBase.class).getMainWindow());
  }

  private boolean saveStatusToFile(File file, String model) {
    try {
      return getController()
          .submit(() -> SaveStatusUtility.saveStatus(file, getController(), model))
          .get();
    } catch (Exception e) {
      log.error("Failed to save status", e);
      return false;
    }
  }

  /**
   * 保存状态和日志到临时目录.

   * @param tempDir 临时目录
   * @param model 系统状态模型数据
   * @return 保存是否成功
   */
  private boolean saveStatusAndLogsToTemp(Path tempDir, String model) {
    try {
      // 保存状态文件
      File statusFile = createStatusFile(tempDir);
      boolean result = saveStatusToFile(statusFile, model);
      if (!result) {
        return false;
      }
      // 保存设备日志
      saveSystemLogs(tempDir);
      // 保存本地日志
      copyExistingLogs(tempDir);
      
      return true;
    } catch (Exception e) {
      log.error("Failed to save status and logs", e);
      return false;
    }
  }

  private File createStatusFile(Path tempDir) {
    String systemName = getController().getDataModel().getConfigData()
        .getSystemConfigData().getSystemData().getName();
    return tempDir.resolve(systemName + ".status").toFile();
  }

  private void saveSystemLogs(Path dir) throws Exception {
    getController().submit(() -> {
      for (Pair<MatrixDefinitionData, CaesarSwitchDataModel> data :
          Utilities.getMatrixModels(getController().getDataModel())) {
        DkmHttpApi.saveLog(dir, data.getKey().getAddress());
      }
    }).get();
  }

  /**
   * 复制现有日志文件到目标目录.
   * 包括当前日志和历史日志目录.
   */
  private void copyExistingLogs(Path targetDir) {
    Path vpmPath = Paths.get(System.getProperty("user.dir"));
    try (Stream<Path> pathStream = Files.list(vpmPath)) {
      pathStream.forEach(path -> {
        String fileName = path.getFileName().toString();
        try {
          if (isOldLogDirectory(path)) {
            copyOldLogs(path, targetDir);
          } else if (isCurrentLog(fileName)) {
            Files.copy(path, targetDir.resolve(fileName));
            log.info("Copied file {}", path);
          }
        } catch (IOException ex) {
          log.error("Failed to copy {}", fileName, ex);
        }
      });
    } catch (IOException ex) {
      log.error("Failed to list files in {}", vpmPath, ex);
    }
  }

  private boolean isOldLogDirectory(Path path) {
    boolean directory = Files.isDirectory(path);
    Path fileNamePath = path.getFileName();
    if (fileNamePath == null) {
      return false;
    }
    boolean matches = fileNamePath.toString().matches("VPM-\\d{4}-\\d{2}");
    return directory && matches;
  }

  private boolean isCurrentLog(String fileName) {
    return fileName.startsWith("app.log") || fileName.endsWith(".json");
  }

  private void copyOldLogs(Path sourcePath, Path targetDir) throws IOException {
    Path targetPath = targetDir.resolve(sourcePath.getFileName());
    Files.createDirectories(targetPath);
    try (Stream<Path> stream = Files.list(sourcePath)) {
      stream.forEach(innerPath -> {
        try {
          Files.copy(innerPath, targetPath.resolve(innerPath.getFileName()));
          log.info("Copied file {}", innerPath);
        } catch (IOException e) {
          log.error("Failed to copy file from old log directory", e);
        }
      });
    }
  }

  /**
   * 将临时目录打包为zip文件.
   */
  private void zipTempDirectoryToFile(Path sourceDir, File targetFile) {
    try (FileOutputStream fos = new FileOutputStream(targetFile);
         ZipOutputStream zos = new ZipOutputStream(new BufferedOutputStream(fos));
         Stream<Path> pathStream = Files.list(sourceDir)) {
      pathStream.forEach(path -> {
        try {
          ZipUtility.zipFile(zos, new File(path.toString()));
        } catch (Exception e) {
          log.error("Failed to zip file", e);
        }
      });
    } catch (IOException e) {
      log.error("Failed to create zip file", e);
    }
  }

  private void showSaveResult(boolean result) {
    Platform.runLater(() -> {
      AlertEx alert = new UndecoratedAlert(
          result ? AlertExType.INFORMATION : AlertExType.WARNING);
      alert.setContentText(CaesarI18nCommonResource.getString(
          result ? "export.status.ok" : "export.status.fail"));
      alert.showAndWait();
    });
  }

  @Override
  public boolean close() {
    log.info("Close entity {}.", getName());
    try {
      serialize();
    } catch (Exception exc) {
      log.error("Failed to save model", exc);
    }
    for (Page page : pages) {
      page.close();
    }
    pages.clear();
    controller.getIsClose().set(true);
    controller.close();
    return true;
  }

  @Override
  protected InputStream getDeserializeData() {
    if (controller instanceof CaesarDemoDeviceController && isUseStatusModel()) {
      CaesarDemoDeviceController demoDeviceController = (CaesarDemoDeviceController) controller;
      if (demoDeviceController.getModel() != null) {
        return new StringInputStream(demoDeviceController.getModel());
      } else {
        return super.getDeserializeData();
      }
    }
    return super.getDeserializeData();
  }

  protected boolean isUseStatusModel() {
    try {
      String value = System.getProperty("use-status-model");
      if (value == null) {
        return true;
      } else {
        return Boolean.parseBoolean(value);
      }
    } catch (RuntimeException exception) {
      return true;
    }
  }
}
