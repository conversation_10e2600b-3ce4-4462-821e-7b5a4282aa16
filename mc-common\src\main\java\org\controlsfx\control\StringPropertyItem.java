package org.controlsfx.control;

import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.StringProperty;
import javafx.beans.value.ObservableValue;

import java.util.Optional;

public class StringPropertyItem extends AbstractPropertyItem {
  private final StringProperty value;
  private StringProperty observableValue = null;

  public StringPropertyItem(StringProperty value) {
    this.value = value;
  }

  @Override
  public Class<?> getType() {
    return String.class;
  }

  @Override
  public Object getValue() {
    if (value == null) {
      return null;
    }
    return value.get();
  }

  @Override
  public void setValue(Object value) {
    if (this.value != null && value instanceof String) {
      this.value.set((String) value);
    }
  }

  @Override
  public Optional<ObservableValue<? extends Object>> getObservableValue() {
    if (observableValue == null) {
      observableValue = new SimpleStringProperty();
      observableValue.bind(value);
    }
    return Optional.of(observableValue);
  }

  @Override
  public void delete() {
    if (observableValue != null) {
      observableValue.unbind();
      observableValue = null;
    }
  }

}
