package com.mc.tool.framework.utility;

import com.google.common.base.Charsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;
import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.SecretKeySpec;
import lombok.extern.slf4j.Slf4j;

/**
 * SymmetricEncryptionUtils.
 */
@Slf4j
public class SymmetricEncryptionUtils {
  private static final String ALGORITHM = "AES";
  private static final String SK = "SeItoavHOnmMIvW0NW7gpA==";

  /**
   * encrypt.
   */
  public static String encrypt(String plainText)
      throws NoSuchPaddingException, IllegalBlockSizeException, NoSuchAlgorithmException,
      BadPaddingException, InvalidKeyException {
    byte[] encryptedBytes =
        performCipherOperation(Cipher.ENCRYPT_MODE, SK, plainText.getBytes(Charsets.UTF_8));
    return Base64.getEncoder().encodeToString(encryptedBytes);
  }

  /**
   * decrypt.
   */
  public static String decrypt(String encryptedText) {
    try {
      byte[] encryptedBytes = Base64.getDecoder().decode(encryptedText);
      byte[] decryptedBytes = performCipherOperation(Cipher.DECRYPT_MODE, SK, encryptedBytes);
      return new String(decryptedBytes, Charsets.UTF_8);
    } catch (Exception ex) {
      log.error("fail to decrypt", ex);
    }
    return "";
  }

  private static byte[] performCipherOperation(int cipherMode, String secretKey, byte[] inputBytes)
      throws NoSuchPaddingException, NoSuchAlgorithmException, IllegalBlockSizeException,
      BadPaddingException, InvalidKeyException {
    SecretKeySpec keySpec = new SecretKeySpec(secretKey.getBytes(Charsets.UTF_8), ALGORITHM);
    Cipher cipher = Cipher.getInstance(ALGORITHM);
    cipher.init(cipherMode, keySpec);
    return cipher.doFinal(inputBytes);
  }
}
