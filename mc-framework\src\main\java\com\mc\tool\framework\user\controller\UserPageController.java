package com.mc.tool.framework.user.controller;

import com.mc.tool.framework.systemedit.datamodel.VisualEditModel;
import java.net.URL;
import java.util.ResourceBundle;
import javafx.fxml.Initializable;

/**
 * .
 */
public class UserPageController implements Initializable, UserControllable {

  public VisualEditModel systemEditModel;


  @Override
  public void initialize(URL location, ResourceBundle resources) {
    // TODO Auto-generated method stub
  }

  @Override
  public void initModel(VisualEditModel model) {
    systemEditModel = model;
  }


}
