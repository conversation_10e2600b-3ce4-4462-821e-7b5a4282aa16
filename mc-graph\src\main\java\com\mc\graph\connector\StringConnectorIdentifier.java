package com.mc.graph.connector;

import com.google.gson.annotations.Expose;

import com.mc.graph.interfaces.ConnectorIdentifier;
import javafx.beans.property.ReadOnlyStringWrapper;
import javafx.beans.property.StringProperty;
import lombok.Getter;

public class StringConnectorIdentifier implements ConnectorIdentifier {
  @Getter
  @Expose
  private String value;

  public StringConnectorIdentifier(String value) {
    this.value = value;
  }

  @Override
  public int hashCode() {
    return value.hashCode();
  }

  @Override
  public boolean equals(Object obj) {
    if (obj instanceof StringConnectorIdentifier) {
      return ((StringConnectorIdentifier) obj).getValue().equals(getValue());
    }
    return false;
  }

  @Override
  public String toString() {
    return value;
  }

  @Override
  public StringProperty textProperty() {
    return new ReadOnlyStringWrapper(value);
  }
}
