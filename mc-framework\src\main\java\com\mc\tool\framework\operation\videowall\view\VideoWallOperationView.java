package com.mc.tool.framework.operation.videowall.view;

import com.mc.tool.framework.operation.interfaces.OperationControllable;
import com.mc.tool.framework.operation.interfaces.OperationView;
import com.mc.tool.framework.operation.videowall.controller.VideoWallControllable;
import com.mc.tool.framework.systemedit.datamodel.VisualEditFunc;
import com.mc.tool.framework.systemedit.datamodel.VisualEditModel;
import com.mc.tool.framework.utility.InjectorProvider;
import java.io.IOException;
import javafx.fxml.FXMLLoader;
import javafx.scene.Node;
import javafx.scene.layout.VBox;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public final class VideoWallOperationView extends VBox implements OperationView {
  @Getter
  private final VideoWallControllable controllable;

  /**
   * Constructor.
   */
  public VideoWallOperationView(VisualEditModel model, VisualEditFunc func) {
    FXMLLoader loader = new FXMLLoader(
        getClass().getResource("/com/mc/tool/framework/operation/videowall/videowall.fxml"));
    loader.setRoot(this);
    controllable =
        InjectorProvider.getInjector().getInstance(VideoWallControllable.class);
    controllable.init(model, func);
    loader.setController(controllable);

    try {
      loader.load();
    } catch (IOException exception) {
      log.warn("Fail to load videowall.fxml", exception);
    }
  }

  @Override
  public Node getView() {
    return this;
  }

  @Override
  public OperationControllable getOperationControllable() {
    return controllable;
  }


}
