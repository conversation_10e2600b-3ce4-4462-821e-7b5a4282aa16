package com.mc.graph;

import com.mc.graph.interfaces.ColorComponent;
import com.mc.graph.interfaces.GraphObject;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.beans.property.SimpleObjectProperty;

/**
 * .
 */
public abstract class AbstractGraphObject implements GraphObject {
  protected SimpleBooleanProperty visibleProperty = new SimpleBooleanProperty(true);
  protected SimpleBooleanProperty highLightProperty = new SimpleBooleanProperty();
  protected SimpleBooleanProperty hoverProperty = new SimpleBooleanProperty();
  protected SimpleObjectProperty<ColorComponent> highLightColor = new SimpleObjectProperty<>();
  
  @Override
  public BooleanProperty visibleProperty() {
    return visibleProperty;
  }
  
  @Override
  public BooleanProperty highLightProperty() {
    return highLightProperty;
  }
  
  @Override
  public BooleanProperty hoverProperty() {
    return hoverProperty;
  }
  
  @Override
  public ObjectProperty<ColorComponent> highLightColorProperty() {
    return highLightColor;
  }
}
