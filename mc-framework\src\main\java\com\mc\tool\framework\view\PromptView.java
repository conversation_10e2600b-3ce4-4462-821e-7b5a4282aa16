package com.mc.tool.framework.view;

import com.mc.tool.framework.interfaces.ApplicationBase;
import com.mc.tool.framework.utility.I18nUtility;
import com.mc.tool.framework.utility.InjectorProvider;
import com.mc.tool.framework.utility.UndecoratedDialog;
import java.io.IOException;
import java.net.URL;
import java.util.ResourceBundle;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.scene.Node;
import javafx.scene.control.ButtonType;
import javafx.scene.control.Label;
import javafx.stage.Modality;
import javafx.stage.Window;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public final class PromptView implements Initializable {
  @FXML
  private Label msgLabel;

  private Node view;

  /**
   * Constructor.
   */
  public PromptView() {
    FXMLLoader loader =
        new FXMLLoader(getClass().getResource("/com/mc/tool/framework/promptview.fxml"));

    try {
      loader.setController(this);
      view = (Node) loader.load();
    } catch (IOException exception) {
      log.warn("Fail to load promptview.fxml!", exception);
    }
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {
    ApplicationBase base = InjectorProvider.getInjector().getInstance(ApplicationBase.class);
    if (base == null) {
      log.warn("ApplicationBase is null!");
      return;
    }

    msgLabel
        .setText(I18nUtility.getI18nBundle("main").getString("framework.menu.config.prompt.msg"));

  }

  public Node getView() {
    return view;
  }

  /**
   * 显示提示页面.
   */
  public static void show(Window owner) {
    UndecoratedDialog<ButtonType> dialog = new UndecoratedDialog<>();
    dialog.initModality(Modality.WINDOW_MODAL);
    dialog.initOwner(owner);
    dialog.setTitle(
        I18nUtility.getI18nBundle("main").getString("framework.menu.config.prompt.title"));
    dialog.getDialogPane().getButtonTypes().add(ButtonType.OK);

    PromptView view = new PromptView();
    dialog.getDialogPane().setContent(view.getView());
    dialog.showAndWait();
  }

}
