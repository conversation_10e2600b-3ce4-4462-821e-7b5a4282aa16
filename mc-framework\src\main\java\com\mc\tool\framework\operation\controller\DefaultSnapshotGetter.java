package com.mc.tool.framework.operation.controller;

import com.mc.common.util.WeakAdapter;
import com.mc.graph.interfaces.CellBindedObject;
import com.mc.tool.framework.operation.interfaces.SnapshotGetter;
import com.mc.tool.framework.operation.videowall.datamodel.interfaces.VideoObject;
import com.mc.tool.framework.systemedit.datamodel.VisualEditModel;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import java.lang.ref.WeakReference;
import javafx.beans.binding.ObjectBinding;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.beans.value.ChangeListener;
import javafx.scene.image.Image;

/**
 * .
 */
public class DefaultSnapshotGetter implements SnapshotGetter {

  private final VisualEditModel model;

  public DefaultSnapshotGetter(VisualEditModel model) {
    this.model = model;
  }

  @Override
  public ObjectProperty<Image> getSnapshot(CellBindedObject object) {
    if (!(object instanceof VideoObject)) {
      return new SimpleObjectProperty<>();
    }

    VideoObject videoData = (VideoObject) object;

    return getSnapshot(videoData.getSource());
  }

  @Override
  public ObjectProperty<Image> getSnapshot(ObjectProperty<VisualEditTerminal> terminal) {
    ObjectBinding<Image> binding = new ImageObjectBinding(terminal, model);
    ObjectProperty<Image> result = new SimpleObjectProperty<>();
    result.bind(binding);
    return result;
  }

  private static class ImageObjectBinding extends ObjectBinding<Image> {
    private final ObjectProperty<VisualEditTerminal> terminal;
    private final WeakReference<VisualEditModel> modelRef;
    private final ChangeListener<VisualEditTerminal> terminalChangeListener;
    private WeakAdapter weakAdapter = new WeakAdapter();

    public ImageObjectBinding(ObjectProperty<VisualEditTerminal> terminal, VisualEditModel model) {
      this.terminal = terminal;
      modelRef = new WeakReference<>(model);
      super.bind(terminal, model.getTerminalSnapshot(terminal.get()));
      terminal
          .addListener(terminalChangeListener = weakAdapter.wrap((observable, oldVal, newVal) -> {
            unbind(terminal, model.getTerminalSnapshot(oldVal));
            bind(terminal, model.getTerminalSnapshot(newVal));
          }));
    }

    @Override
    protected Image computeValue() {
      VisualEditModel model = modelRef.get();
      if (model == null) {
        return null;
      }
      return model.getTerminalSnapshot(terminal.get()).get();
    }

    public void dispose() {
      VisualEditModel model = modelRef.get();
      if (model != null) {
        unbind(terminal, model.getTerminalSnapshot(terminal.get()));
      }
      terminal.removeListener(terminalChangeListener);
    }
  }

}