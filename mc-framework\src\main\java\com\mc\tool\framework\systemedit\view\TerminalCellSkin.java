package com.mc.tool.framework.systemedit.view;

import com.mc.common.control.TextTooltipHelper;
import com.mc.common.control.TextWrapper;
import com.mc.graph.AbstractCellSkin;
import com.mc.graph.interfaces.CellBehavior;
import com.mc.graph.interfaces.CellBindedObject;
import com.mc.graph.interfaces.CellObject;
import com.mc.graph.interfaces.Connector;
import com.mc.graph.interfaces.ConnectorSkin;
import com.mc.graph.interfaces.SkinManager;
import com.mc.tool.framework.systemedit.datamodel.MultimediaInterfaceType;
import com.mc.tool.framework.systemedit.datamodel.SystemEditDefinition;
import com.mc.tool.framework.systemedit.datamodel.TargetDeviceType;
import com.mc.tool.framework.systemedit.datamodel.VisualEditNode;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import com.mc.tool.framework.utility.JavaFxUtility;
import java.io.IOException;
import java.net.URL;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.ResourceBundle;
import javafx.beans.InvalidationListener;
import javafx.beans.binding.Bindings;
import javafx.beans.value.ChangeListener;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.geometry.Side;
import javafx.scene.Parent;
import javafx.scene.image.Image;
import javafx.scene.input.MouseEvent;
import javafx.scene.layout.Background;
import javafx.scene.layout.BackgroundFill;
import javafx.scene.layout.BackgroundImage;
import javafx.scene.layout.BackgroundPosition;
import javafx.scene.layout.BackgroundRepeat;
import javafx.scene.layout.Border;
import javafx.scene.layout.BorderStroke;
import javafx.scene.layout.BorderStrokeStyle;
import javafx.scene.layout.HBox;
import javafx.scene.layout.Pane;
import javafx.scene.layout.Priority;
import javafx.scene.layout.Region;
import javafx.scene.layout.VBox;
import javafx.scene.paint.Color;
import javafx.scene.shape.SVGPath;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class TerminalCellSkin extends AbstractCellSkin implements Initializable {

  public static final int CONNECTOR_INSET = 8;

  protected HBox region;

  @FXML
  protected Pane cellContainer;
  @FXML
  protected VBox connectorContainer;
  @FXML
  protected TextWrapper nameText;

  protected List<ConnectorSkin> connectorSkins;

  protected ColorBackgroundGrid connectedTypes;

  private Boolean leftTerminal;

  private ChangeListener<CellBindedObject> bindObjectChangeListener1;
  private ChangeListener<CellBindedObject> bindObjectChagneListener2;

  protected ChangeListener<MultimediaInterfaceType> multimediaInterfaceTypeChangeListener;
  protected ChangeListener<TargetDeviceType> targetDeviceTypeChangeListener;
  protected ChangeListener<Boolean> targetConnectedChangeListener;

  protected static final int CELL_WIDTH = 168;
  protected static final int CELL_HEIGHT = 35;
  protected static final int IMAGE_WIDTH = CELL_HEIGHT;
  protected static final int CONNECTOR_CONTAINER_WIDTH = 4;
  protected static final int CONNECTED_TYPE_WIDTH = 8;
  protected static final int SEPEARTOR_TOP_INSET = 8;
  protected static final int SEPERATOR_BOTTOM_INSET = 7;
  protected static final int SEPERATOR_WIDTH = 1;

  protected static final int CELL_CONTAINER_WIDTH = CELL_WIDTH - CONNECTOR_CONTAINER_WIDTH;

  protected static final int LABEL_WIDTH =
      CELL_CONTAINER_WIDTH - CONNECTOR_CONTAINER_WIDTH - IMAGE_WIDTH * 2 - SEPERATOR_WIDTH * 2 - 10;

  protected static final BackgroundFill cellBgFill = new BackgroundFill(Color.WHITE, null, null);
  protected static final BackgroundFill leftSeperator1Fill = new BackgroundFill(
      Color.web("#a6a6a6"),
      null, new Insets(SEPEARTOR_TOP_INSET, CELL_CONTAINER_WIDTH - IMAGE_WIDTH - SEPERATOR_WIDTH,
      SEPERATOR_BOTTOM_INSET, IMAGE_WIDTH));
  protected static final BackgroundFill leftSeperator2Fill =
      new BackgroundFill(Color.web("#a6a6a6"), null,
          new Insets(SEPEARTOR_TOP_INSET, IMAGE_WIDTH + CONNECTED_TYPE_WIDTH,
              SEPERATOR_BOTTOM_INSET,
              CELL_CONTAINER_WIDTH - IMAGE_WIDTH - CONNECTED_TYPE_WIDTH - SEPERATOR_WIDTH));
  protected static final BackgroundFill rightSeperator1Fill =
      new BackgroundFill(Color.web("#a6a6a6"), null,
          new Insets(SEPEARTOR_TOP_INSET,
              CELL_CONTAINER_WIDTH - IMAGE_WIDTH - CONNECTED_TYPE_WIDTH - SEPERATOR_WIDTH,
              SEPERATOR_BOTTOM_INSET, CONNECTED_TYPE_WIDTH + IMAGE_WIDTH));
  protected static final BackgroundFill rightSeperator2Fill =
      new BackgroundFill(Color.web("#a6a6a6"), null, new Insets(SEPEARTOR_TOP_INSET, IMAGE_WIDTH,
          SEPERATOR_BOTTOM_INSET, CELL_CONTAINER_WIDTH - IMAGE_WIDTH - SEPERATOR_WIDTH));

  protected static final BackgroundPosition leftImage1Pos =
      new BackgroundPosition(Side.LEFT, 0, false, Side.TOP, 0.5, true);
  protected static final BackgroundPosition leftImage2Pos =
      new BackgroundPosition(Side.RIGHT, CONNECTED_TYPE_WIDTH, false, Side.TOP, 0.5, true);
  protected static final BackgroundPosition rightImage1Pos =
      new BackgroundPosition(Side.LEFT, CONNECTED_TYPE_WIDTH, false, Side.TOP, 0.5, true);
  protected static final BackgroundPosition rightImage2Pos =
      new BackgroundPosition(Side.RIGHT, 0, false, Side.TOP, 0.5, true);


  protected static final SVGPath cellShape = new SVGPath();
  protected static final Border cellNormalBorder =
      new Border(new BorderStroke(Color.web("#b3b3b3"), BorderStrokeStyle.SOLID, null, null));
  protected static final Border cellHighlightBorder =
      new Border(new BorderStroke(SystemEditDefinition.LABEL_HIGHLIGHT_COLOR,
          BorderStrokeStyle.SOLID, null, null));
  private static final TargetDeviceType[] targetDeviceTypes =
      new TargetDeviceType[] {TargetDeviceType.COMPUTER, TargetDeviceType.DVD,
          TargetDeviceType.MONITOR, TargetDeviceType.PROJECTOR,
          TargetDeviceType.GRID_LINE_N, TargetDeviceType.GRID_LINE_T,
          TargetDeviceType.GRID_LINE_R, TargetDeviceType.GRID_LINE_DUAL};

  private static final String[] targetDeviceTypeLogos =
      new String[] {SystemeditConstants.COMPUTER_ONLINE_LOGO, SystemeditConstants.DVD_ONLINE_LOGO,
          SystemeditConstants.MONITOR_ONLINE_LOGO, SystemeditConstants.PROJECTOR_ONLINE_LOGO,
          SystemeditConstants.GRID_LINE_N_ONLINE_LOGO, SystemeditConstants.GRID_LINE_T_ONLINE_LOGO,
          SystemeditConstants.GRID_LINE_R_ONLINE_LOGO,
          SystemeditConstants.GRID_LINE_DUAL_ONLINE_LOGO};

  private static final MultimediaInterfaceType[] multimediaInterfaceTypes =
      new MultimediaInterfaceType[] {MultimediaInterfaceType.DP, MultimediaInterfaceType.HDMI,
          MultimediaInterfaceType.DVI, MultimediaInterfaceType.USB,
          MultimediaInterfaceType.GRID_LINE};
  private static final String[] multimediaInterfaceLogos = new String[] {
      SystemeditConstants.DP_LOGO,
      SystemeditConstants.HDMI_LOGO, SystemeditConstants.DVI_LOGO, SystemeditConstants.USB_LOGO,
      SystemeditConstants.GRIDLINE_LOGO};

  private static final Map<TargetDeviceType, Pair<Image, Image>> targetDeviceTypeBgImgs =
      new HashMap<>();
  private static final Map<MultimediaInterfaceType, Image> multimediaInterfaceBgImgs =
      new HashMap<>();

  static {
    cellShape.setContent("M0.5,0.5h170v36H0.5V0.5z");
    ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
    int count = targetDeviceTypes.length;
    for (int i = 0; i < count; i++) {
      Image onlineImage = new Image(classLoader
          .getResourceAsStream(SystemeditConstants.RESOURCE_PATH_SHORT + targetDeviceTypeLogos[i]));
      Image offlineImage =
          new Image(classLoader.getResourceAsStream(SystemeditConstants.RESOURCE_PATH_SHORT
              + targetDeviceTypeLogos[i].replaceAll("online", "offline")));
      targetDeviceTypeBgImgs.put(targetDeviceTypes[i], new Pair<>(onlineImage, offlineImage));
    }

    count = multimediaInterfaceTypes.length;
    for (int i = 0; i < count; i++) {
      Image logo = new Image(classLoader.getResourceAsStream(
          SystemeditConstants.RESOURCE_PATH_SHORT + multimediaInterfaceLogos[i]));
      multimediaInterfaceBgImgs.put(multimediaInterfaceTypes[i], logo);
    }
  }

  public TerminalCellSkin(CellObject cellobject, Parent parent, Parent container,
                          SkinManager skinManager) {
    super(cellobject, parent, container, skinManager);
    movableProperty().set(false);
  }

  protected boolean isLeftTerminal() {
    if (leftTerminal == null) {
      leftTerminal = true;
    }
    return leftTerminal;
  }

  @Override
  public Region getRegion() {
    return region;
  }

  @Override
  public Collection<ConnectorSkin> getConnectorSkins() {
    return connectorSkins;
  }

  @Override
  public Color getSelectionBorderColor() {
    return SystemeditConstants.SELECTED_COLOR;
  }

  @Override
  public boolean isSelectionEffectEnabled() {
    return false;
  }

  @Override
  public boolean isResizeble() {
    return false;
  }

  @Override
  protected void initInner() {
    connectorSkins = new ArrayList<>();
  }

  @Override
  protected void initRegion() {
    createRegionByCode();
    region.setUserData(this);

    updateLayout();
    cellObject.getBindedObjectProperty().addListener(
        bindObjectChangeListener1 = weakAdapter.wrap((observable, oldValue, newValue) -> updateLayout()));
  }

  protected void createRegionByFxml() {
    try {
      ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
      FXMLLoader loader = new FXMLLoader(classLoader
          .getResource(SystemeditConstants.RESOURCE_PATH_SHORT + "terminal_cell_skin.fxml"));
      loader.setController(this);
      region = loader.load();
    } catch (IOException exception) {
      log.warn("Fail to load terminal_cell_skin.fxml!");
    }
  }

  protected void createRegionByCode() {

    region = new NoCssHBox();
    region.setAlignment(Pos.CENTER);
    region.setMinHeight(CELL_HEIGHT);
    region.setMaxHeight(CELL_HEIGHT);
    region.getStyleClass().add("terminal");
    //
    cellContainer = new NoCssPane();
    cellContainer.setBorder(cellNormalBorder);
    cellContainer.setMinWidth(CELL_CONTAINER_WIDTH);
    cellContainer.setMaxWidth(CELL_CONTAINER_WIDTH);
    HBox.setHgrow(cellContainer, Priority.ALWAYS);
    cellContainer.getStyleClass().add("cell-container");
    region.getChildren().add(cellContainer);

    nameText = new TextWrapper();
    nameText.setMinHeight(CELL_HEIGHT);
    nameText.setMaxHeight(CELL_HEIGHT);
    nameText.setMinWidth(LABEL_WIDTH);
    nameText.setMaxWidth(LABEL_WIDTH);
    nameText.setAlignment(Pos.CENTER);
    nameText.setId("name-text");
    nameText.setLayoutX(computeNameLabelXPos());
    nameText.setLayoutY(0);
    TextTooltipHelper.install(nameText);
    cellContainer.getChildren().add(nameText);
    //
    connectedTypes = new ColorBackgroundGrid();

    //
    connectorContainer = new NoCssVBox();
    connectorContainer.setMinWidth(CONNECTOR_CONTAINER_WIDTH);
    connectorContainer.setMaxWidth(CONNECTOR_CONTAINER_WIDTH);
    connectorContainer.setAlignment(Pos.CENTER);
    region.getChildren().add(connectorContainer);

    initialize(null, null);
  }

  protected void updateLayout() {
    if (cellObject.getBindedObject() instanceof VisualEditTerminal) {
      VisualEditTerminal terminal = (VisualEditTerminal) cellObject.getBindedObject();
      // rx的布局与tx布局相反
      if (!SystemEditDefinition.nodeAtLeft(terminal)) {
        setRight();
      }
    }
  }

  @Override
  protected void onConnectorChange() {
    if (cellObject == null) {
      return;
    }
    // 删除没有用的connectorskin，创建新的
    List<ConnectorSkin> newSkins = new ArrayList<>();
    for (Connector connector : cellObject.getConnectors()) {
      ConnectorSkin cs = null;
      for (ConnectorSkin skin : connectorSkins) {
        if (skin.getConnector() == connector) {
          cs = skin;
          break;
        }
      } // end for
      if (cs == null) {
        cs = new TerminalConnectorSkin(connector, connectorContainer, container);
        skinManager.setConnectorSkin(connector, cs);
        for (CellBehavior cellBehavior : cellBehaviors) {
          cellBehavior.createConnectorBehavior(cs);
        }
        if (getRegion().getParent() != null) {
          cs.add();
        }
      }
      newSkins.add(cs);
    } // end for

    for (ConnectorSkin skin : connectorSkins) {
      if (!newSkins.contains(skin)) {
        skin.remove();
      }
    }

    connectorSkins = newSkins;
    if (connectorSkins.size() > 1) {
      double space = region.getMaxHeight()
          - TerminalConnectorSkin.CONNECTOR_HEIGHT * connectorSkins.size() - CONNECTOR_INSET;
      space = space / (connectorSkins.size() - 1);
      connectorContainer.setSpacing(space);
    }

    updateConnectorDirection();
  }

  @Override
  protected void layoutConnectors() {

  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {
    nameText.addEventHandler(MouseEvent.MOUSE_CLICKED, weakAdapter.wrap(this::onNameLabelMouseClicked));

    multimediaInterfaceTypeChangeListener =
        weakAdapter.wrap((change, oldVal, newVal) -> updateCellBackground());
    targetDeviceTypeChangeListener =
        weakAdapter.wrap((change, oldVal, newVal) -> updateCellBackground());
    targetConnectedChangeListener =
        weakAdapter.wrap((change, oldVal, newVal) -> updateCellBackground());

    updateNameBinding();
    updateCellBackground();

    getCell().getBindedObjectProperty()
        .addListener(bindObjectChagneListener2 = weakAdapter.wrap((change, oldVal, newVal) -> {
          updateListeners(oldVal, false);
          updateListeners(newVal, true);
          updateNameBinding();
          updateCellBackground();
        }));

    connectedTypes.getObservable()
        .addListener(weakAdapter.wrap((InvalidationListener) (change) -> updateCellBackground()));

    cellContainer.borderProperty().bind(Bindings.when(cellObject.highLightProperty())
        .then(cellHighlightBorder).otherwise(cellNormalBorder));
  }

  protected void updateListeners(CellBindedObject object, boolean attach) {
    if (!(object instanceof VisualEditTerminal)) {
      return;
    }
    VisualEditTerminal terminal = (VisualEditTerminal) object;
    if (attach) {
      terminal.multimediaInterfaceTypeProperty().addListener(multimediaInterfaceTypeChangeListener);
      terminal.targetDeviceTypeProperty().addListener(targetDeviceTypeChangeListener);
      terminal.targetDeviceConnectedProperty().addListener(targetConnectedChangeListener);
    } else {
      terminal.multimediaInterfaceTypeProperty()
          .removeListener(multimediaInterfaceTypeChangeListener);
      terminal.targetDeviceTypeProperty().removeListener(targetDeviceTypeChangeListener);
      terminal.targetDeviceConnectedProperty().removeListener(targetConnectedChangeListener);
    }
  }

  protected void updateCellBackground() {
    BackgroundImage[] images = new BackgroundImage[0];
    if (getCell().getBindedObject() instanceof VisualEditTerminal) {
      VisualEditTerminal terminal = (VisualEditTerminal) getCell().getBindedObject();
      Image img1;
      Image img2;
      BackgroundPosition imgPos1;
      BackgroundPosition imgPos2;
      if (isLeftTerminal()) {
        if (terminal.isTargetDeviceConnected()) {
          img1 = targetDeviceTypeBgImgs.get(terminal.getTargetDeviceType()).getKey();
        } else {
          img1 = targetDeviceTypeBgImgs.get(terminal.getTargetDeviceType()).getValue();
        }
        img2 = multimediaInterfaceBgImgs.get(terminal.getMultimediaInterfaceType());
        imgPos1 = leftImage1Pos;
        imgPos2 = leftImage2Pos;
      } else {
        img1 = multimediaInterfaceBgImgs.get(terminal.getMultimediaInterfaceType());
        if (terminal.isTargetDeviceConnected()) {
          img2 = targetDeviceTypeBgImgs.get(terminal.getTargetDeviceType()).getKey();
        } else {
          img2 = targetDeviceTypeBgImgs.get(terminal.getTargetDeviceType()).getValue();
        }
        imgPos1 = rightImage1Pos;
        imgPos2 = rightImage2Pos;
      }

      if (imgPos1 != null && imgPos2 != null && img1 != null && img2 != null) {
        BackgroundPosition newImgPos1 = new BackgroundPosition(imgPos1.getHorizontalSide(),
            imgPos1.getHorizontalPosition() + (IMAGE_WIDTH - img1.getWidth()) / 2,
            imgPos1.isHorizontalAsPercentage(), imgPos1.getVerticalSide(),
            imgPos1.getVerticalPosition(), imgPos1.isVerticalAsPercentage());
        BackgroundPosition newImgPos2 = new BackgroundPosition(imgPos2.getHorizontalSide(),
            imgPos2.getHorizontalPosition() + (IMAGE_WIDTH - img2.getWidth()) / 2,
            imgPos2.isHorizontalAsPercentage(), imgPos2.getVerticalSide(),
            imgPos2.getVerticalPosition(), imgPos2.isVerticalAsPercentage());
        BackgroundImage bgImg1 = new BackgroundImage(img1, BackgroundRepeat.NO_REPEAT,
            BackgroundRepeat.NO_REPEAT, newImgPos1, null);
        BackgroundImage bgImg2 = new BackgroundImage(img2, BackgroundRepeat.NO_REPEAT,
            BackgroundRepeat.NO_REPEAT, newImgPos2, null);
        images = new BackgroundImage[] {bgImg1, bgImg2};
      } else {
        if (imgPos1 == null) {
          log.warn("imgPos1 is null!");
        }
        if (imgPos2 == null) {
          log.warn("imgPos2 is null!");
        }
        if (img1 == null) {
          log.warn("img1 is null!");
        }
        if (img2 == null) {
          log.warn("img2 is null!");
        }
      }
    }
    Background background;

    if (isLeftTerminal()) {
      Collection<BackgroundFill> connectedTypeFills = connectedTypes.createBackgroundFills(1, 1, 1,
          CELL_CONTAINER_WIDTH - CONNECTED_TYPE_WIDTH + 1, CELL_HEIGHT - 2);
      List<BackgroundFill> fills = new ArrayList<>();
      fills.add(cellBgFill);
      fills.add(leftSeperator1Fill);
      fills.add(leftSeperator2Fill);
      fills.addAll(connectedTypeFills);
      background = new Background(fills.toArray(new BackgroundFill[0]), images);
    } else {
      Collection<BackgroundFill> connectedTypeFills = connectedTypes.createBackgroundFills(1,
          CELL_CONTAINER_WIDTH - CONNECTED_TYPE_WIDTH + 1, 1, 1, CELL_HEIGHT - 2);
      List<BackgroundFill> fills = new ArrayList<>();
      fills.add(cellBgFill);
      fills.add(rightSeperator1Fill);
      fills.add(rightSeperator2Fill);
      fills.addAll(connectedTypeFills);
      background = new Background(fills.toArray(new BackgroundFill[0]), images);
    }
    cellContainer.setBackground(background);
  }

  protected Image getTargetDeviceOnlineBgImg(TargetDeviceType type) {
    return targetDeviceTypeBgImgs.get(type).getKey();
  }

  protected Image getTargetDeviceOffsetBgImg(TargetDeviceType type) {
    return targetDeviceTypeBgImgs.get(type).getValue();
  }

  protected Image getMultimediaIntefaceBgImg(MultimediaInterfaceType type) {
    return multimediaInterfaceBgImgs.get(type);
  }

  protected void updateNameBinding() {
    nameText.textProperty().unbind();
    if (getCell().getBindedObject() instanceof VisualEditNode) {
      VisualEditNode node = (VisualEditNode) getCell().getBindedObject();
      nameText.textProperty().bindBidirectional(node.nameProperty());
    }
  }

  protected void onNameLabelMouseClicked(MouseEvent event) {
    if (event.getClickCount() == 2) {
      Optional<String> result =
          ViewUtility.getNameFromDialog(nameText.getScene().getWindow(), nameText.getText());
      result.ifPresent(s -> nameText.setText(s));
    }
  }

  protected void setRight() {
    if (isLeftTerminal()) {
      JavaFxUtility.reversePaneContent(region, false);
      JavaFxUtility.reversePaneContent(cellContainer, false);
      leftTerminal = false;
      nameText.setLayoutX(computeNameLabelXPos());
      updateConnectorDirection();
      updateCellBackground();
    }
  }

  protected void updateConnectorDirection() {
    for (ConnectorSkin skin : connectorSkins) {
      // 当terminal是在matrix的左边时，connector应该连右边的元素
      TerminalConnectorSkin terminalConnectorSkin = (TerminalConnectorSkin) skin;
      terminalConnectorSkin.setConnectAtLeft(!isLeftTerminal());
    }
  }

  @SuppressWarnings("checkstyle:AbbreviationAsWordInName")
  protected double computeNameLabelXPos() {
    if (isLeftTerminal()) {
      return (double) (CELL_CONTAINER_WIDTH - CONNECTED_TYPE_WIDTH - LABEL_WIDTH) / 2;
    } else {
      return (double) (CELL_CONTAINER_WIDTH - CONNECTED_TYPE_WIDTH - LABEL_WIDTH) / 2 + CONNECTED_TYPE_WIDTH;
    }
  }

  @Override
  public void destroy() {
    if (getCell() != null && getCell().getBindedObjectProperty() != null) {
      getCell().getBindedObjectProperty().removeListener(bindObjectChangeListener1);
      getCell().getBindedObjectProperty().removeListener(bindObjectChagneListener2);
      updateListeners(getCell().getBindedObject(), false);
    }

    for (ConnectorSkin skin : getConnectorSkins()) {
      skin.getContainerXposProperty().unbind();
      skin.getContainerYposProperty().unbind();
    }

    nameText.textProperty().unbind();
    cellContainer.borderProperty().unbind();
    connectedTypes.destroy();
    TextTooltipHelper.uninstall(nameText);
    super.destroy();
  }

}
