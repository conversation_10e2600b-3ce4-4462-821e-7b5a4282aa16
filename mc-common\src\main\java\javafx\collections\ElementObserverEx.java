/*
 * Copyright (c) 2010, 2013, Oracle and/or its affiliates. All rights reserved. ORACLE
 * PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */

package javafx.collections;

import javafx.beans.InvalidationListener;
import javafx.beans.Observable;
import javafx.beans.WeakInvalidationListener;
import javafx.util.Callback;

import java.util.IdentityHashMap;

public class ElementObserverEx<E> {

  public static class ElementsMapElement {
    InvalidationListener listener;
    InvalidationListener weakListener;
    int counter;

    protected ElementsMapElement(InvalidationListener listener, InvalidationListener weakListener) {
      this.listener = listener;
      this.weakListener = weakListener;
      this.counter = 1;
    }

    public void increment() {
      counter++;
    }

    public int decrement() {
      return --counter;
    }

    public InvalidationListener getListener() {
      return listener;
    }
    
    public InvalidationListener getWeakListener() {
      return weakListener;
    }
    
  }

  private Callback<E, Observable[]> extractor;
  private final Callback<E, InvalidationListener> listenerGenerator;
  private IdentityHashMap<E, ElementObserverEx.ElementsMapElement> elementsMap =
      new IdentityHashMap<E, ElementObserverEx.ElementsMapElement>();

  public ElementObserverEx(Callback<E, Observable[]> extractor,
      Callback<E, InvalidationListener> listenerGenerator) {
    this.extractor = extractor;
    this.listenerGenerator = listenerGenerator;
  }

  /**
   * 添加监听器.
   * @param element 要添加监听器的元素
   */
  public void attachListener(final E element) {
    if (elementsMap != null && element != null) {
      if (elementsMap.containsKey(element)) {
        elementsMap.get(element).increment();
      } else {
        InvalidationListener listener = listenerGenerator.call(element);
        InvalidationListener weakListener = new WeakInvalidationListener(listener);
        for (Observable o : extractor.call(element)) {
          o.addListener(weakListener);
        }
        elementsMap.put(element, new ElementObserverEx.ElementsMapElement(listener, weakListener));
      }
    }
  }

  /**
   * 删除监听器.
   * @param element 要删除监听器的元素
   */
  public void detachListener(E element) {
    if (elementsMap != null && element != null) {
      ElementObserverEx.ElementsMapElement el = elementsMap.get(element);
      for (Observable o : extractor.call(element)) {
        o.removeListener(el.getWeakListener());
      }
      if (el.decrement() == 0) {
        elementsMap.remove(element);
      }
    }
  }

}
