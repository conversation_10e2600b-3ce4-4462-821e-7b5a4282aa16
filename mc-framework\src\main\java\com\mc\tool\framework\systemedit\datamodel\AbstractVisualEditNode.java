package com.mc.tool.framework.systemedit.datamodel;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.JsonAdapter;
import com.mc.common.collections.ObservableListWrapperEx;
import com.mc.graph.interfaces.CellObject;
import com.mc.graph.interfaces.ExtraActionInfo;
import com.mc.tool.framework.utility.I18nUtility;
import com.mc.tool.framework.utility.ObservableListWrapperExTypeAdapterFactory;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.function.Predicate;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.DoubleProperty;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.beans.property.SimpleDoubleProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.StringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import lombok.extern.slf4j.Slf4j;
import org.controlsfx.control.PropertySheet;
import org.controlsfx.control.PropertySheet.Item;
import org.controlsfx.control.StringPropertyItem;

/**
 * .
 */
@Slf4j
public abstract class AbstractVisualEditNode implements VisualEditNode {
  @Expose
  protected StringProperty nameProperty = new SimpleStringProperty(null);
  @Expose
  protected ObjectProperty<VisualEditNode> parentProperty = new SimpleObjectProperty<>();
  @Expose
  protected DoubleProperty xposProperty = new SimpleDoubleProperty();
  @Expose
  protected DoubleProperty yposProperty = new SimpleDoubleProperty();
  @Expose
  protected SimpleBooleanProperty collapseProperty = new SimpleBooleanProperty();
  @Expose
  @JsonAdapter(ObservableListWrapperExTypeAdapterFactory.class)
  protected ObservableListWrapperEx<VisualEditNode> children =
      new ObservableListWrapperEx<>(new ArrayList<>());
  protected ObservableList<VisualEditNode> readOnlyChildren = null;
  protected CellObject cellObject;

  @Expose
  private String guid = UUID.randomUUID().toString();

  protected ObservableList<PropertySheet.Item> properties = FXCollections.observableArrayList();

  @Override
  public String getGuid() {
    return guid;
  }

  protected void addNameProperty() {
    StringPropertyItem item = new StringPropertyItem(nameProperty);
    item.setName(I18nUtility.getI18nBundle("systemedit").getString("property.name"));
    properties.add(item);
  }

  @Override
  public VisualEditNode findChildByGuid(String guid, boolean recursive) {
    for (VisualEditNode child : getChildren()) {
      if (child.getGuid().equals(guid)) {
        return child;
      } else if (recursive) {
        VisualEditNode recursiveResult = child.findChildByGuid(guid, recursive);
        if (recursiveResult != null) {
          return recursiveResult;
        }
      }
    }
    return null;
  }

  @Override
  public VisualEditNode findChildByName(String name, boolean recursive) {
    for (VisualEditNode child : getChildren()) {
      if (child.getName().equals(name)) {
        return child;
      } else if (recursive) {
        VisualEditNode recursiveResult = child.findChildByName(name, recursive);
        if (recursiveResult != null) {
          return recursiveResult;
        }
      }
    }
    return null;
  }

  @Override
  public VisualEditNode findChild(Predicate<VisualEditNode> predicate, boolean recursive) {
    for (VisualEditNode child : getChildren()) {
      if (predicate.test(child)) {
        return child;
      } else if (recursive) {
        VisualEditNode recursiveResult = child.findChild(predicate, recursive);
        if (recursiveResult != null) {
          return recursiveResult;
        }
      }
    }
    return null;
  }

  @Override
  public Collection<VisualEditTerminal> getAllOnlineTerminalChild() {
    return getAllTerminalChild().filtered(VisualEditTerminal::isOnline);
  }

  @Override
  public Collection<String> getSupportLinkNodeType() {
    return Collections.emptyList();
  }

  @Override
  public Collection<String> getSupportConnectNodeType() {
    return Collections.emptyList();
  }

  @Override
  public Collection<String> getSupportConnectionType() {
    return Collections.emptyList();
  }

  @Override
  public void setName(String name) {
    nameProperty.set(name);
  }

  @Override
  public StringProperty nameProperty() {
    return nameProperty;
  }

  @Override
  public String getName() {
    return nameProperty.get();
  }

  @Override
  public VisualEditNode getParent() {
    return parentProperty.get();
  }

  @Override
  public void setParent(VisualEditNode node) {
    parentProperty.set(node);
  }

  @Override
  public Collection<VisualEditNode> getChildren() {
    return new ArrayList<>(children);
  }

  @Override
  public ObservableList<VisualEditNode> getObservableChildren() {
    if (readOnlyChildren == null) {
      readOnlyChildren = FXCollections.unmodifiableObservableList(children);
    }
    return readOnlyChildren;
  }

  @Override
  public Collection<VisualEditNode> getRxChildren() {
    if (isRx()) {
      return getChildren();
    } else {
      return Collections.emptyList();
    }
  }

  @Override
  public Collection<VisualEditNode> getTxChildren() {
    if (isTx()) {
      return getChildren();
    } else {
      return Collections.emptyList();
    }
  }

  @Override
  public void addChildren(int index, VisualEditNode... nodes) {
    Collection<VisualEditNode> list = addPreprocessing(nodes);
    for (VisualEditNode item : list) {
      item.setParent(this);
    }
    if (index >= 0) {
      children.addAll(index, list);
    } else {
      children.addAll(list);
    }

  }

  @Override
  public void addChildren(VisualEditNode... node) {
    Collection<VisualEditNode> list = addPreprocessing(node);
    for (VisualEditNode item : list) {
      item.setParent(this);
    }
    children.addAll(list);
  }

  /**
   * 添加前的预处理.
   *
   * @param nodes 要添加的节点.
   * @return 可以添加的节点的列表.
   */
  protected Collection<VisualEditNode> addPreprocessing(VisualEditNode... nodes) {
    List<VisualEditNode> list = new ArrayList<>();
    for (VisualEditNode item : nodes) {
      if (item == this || item.hasChild(this)) {
        log.error("Recursive Add!");
        continue;
      }
      list.add(item);
    }
    return list;
  }

  @Override
  public int indexOfChild(VisualEditNode node) {
    return children.indexOf(node);
  }

  @Override
  public void removeChildren(boolean recursive, VisualEditNode... node) {
    children.removeAll(Arrays.asList(node));
    for (VisualEditNode item : node) {
      if (recursive) {
        for (VisualEditNode child : children) {
          if (child.hasChild(item)) {
            child.removeChildren(recursive, item);
            break;
          }
        }
      }
    }
  }

  @Override
  public void removeChildren(boolean recursive, boolean permanent, VisualEditNode... node) {
    removeChildren(recursive, node);
  }

  @Override
  public void moveChild(VisualEditNode node, int index) {
    if (!children.contains(node)) {
      log.warn("Do not have such child.");
      return;
    }

    children.move(node, index);
  }

  @Override
  public void removeAndAdd(Collection<VisualEditNode> removeItems,
                           Collection<VisualEditNode> addItems, int addIndex) {
    for (VisualEditNode node : addItems) {
      node.setParent(this);
    }
    children.removeAndAdd(removeItems, addItems, addIndex);
  }

  @Override
  public DoubleProperty locationXposProperty() {
    return xposProperty;
  }

  @Override
  public DoubleProperty locationYposProperty() {
    return yposProperty;
  }

  @Override
  public boolean isEmpty() {
    return children.isEmpty();
  }

  @Override
  public boolean hasChild(VisualEditNode node) {
    for (VisualEditNode child : getChildren()) {
      if (child == node) {
        return true;
      }
      if (child.hasChild(node)) {
        return true;
      }
    }
    return false;
  }

  @Override
  public boolean isRx() {
    return false;
  }

  @Override
  public boolean isTx() {
    return false;
  }

  @Override
  public boolean isFixToMatrix(VisualEditMatrix matrix) {
    return true;
  }

  @Override
  public boolean isChildIndexChangable() {
    return true;
  }

  @Override
  public void setCellObject(CellObject cellObject) {
    if (this.cellObject != null) {
      this.cellObject.getXProperty().unbindBidirectional(this.locationXposProperty());
      this.cellObject.getYProperty().unbindBidirectional(this.locationYposProperty());
    }
    this.cellObject = cellObject;
    if (cellObject != null) {
      cellObject.getXProperty().bindBidirectional(this.locationXposProperty());
      cellObject.getYProperty().bindBidirectional(this.locationYposProperty());
    }
  }

  @Override
  public CellObject getCellObject() {
    return this.cellObject;
  }

  @Override
  public Collection<ExtraActionInfo> getExtraActions() {
    return Collections.emptyList();
  }

  @Override
  public BooleanProperty collapseProperty() {
    return collapseProperty;
  }


  @Override
  public void recursiveInit() {
    for (VisualEditNode node : getChildren()) {
      node.recursiveInit();
      node.setParent(this);
    }
    init();
  }

  @Override
  public ObservableList<Item> getProperties() {
    return properties;
  }

  /**
   * 递归获取所有符合条件的child.
   *
   * @param predicate 判断条件
   * @param allChild  所有符合条件的child
   * @param tclass    类型
   * @param <T>       类型
   */
  public <T> void recursiveAllChild(Predicate<T> predicate, List<T> allChild, Class<T> tclass) {
    Collection<VisualEditNode> childs = getChildren();
    for (VisualEditNode child : childs) {
      if (tclass.isInstance(child) && predicate.test(tclass.cast(child))) {
        allChild.add(tclass.cast(child));
      }
    }
    for (VisualEditNode node : childs) {
      node.recursiveAllChild(predicate, allChild, tclass);
    }
  }

}
