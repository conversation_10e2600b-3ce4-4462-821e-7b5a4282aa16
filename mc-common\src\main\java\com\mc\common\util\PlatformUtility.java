package com.mc.common.util;

import javafx.application.Platform;

public class PlatformUtility {
  
  /**
   * 运行runnable在javafx线程.
   * @param runnable runnable
   */
  public static void runInFxThread(Runnable runnable) {
    if (System.getProperty("no-fx-mode") != null || Platform.isFxApplicationThread()) {
      runnable.run();
    } else {
      Platform.runLater(runnable);
    }
  }

  /**
   * 迟点在javafx线程运行.
   * @param runnable runnable
   */
  public static void runInFxThreadLater(Runnable runnable) {
    if (System.getProperty("no-fx-mode") != null) {
      runnable.run();
    } else {
      Platform.runLater(runnable);
    }
  }

  /**
   * 检查当前线程名称的前缀.
   * @param name 前缀
   */
  public static void checkThreadPrefix(String name) {
    if (!Thread.currentThread().getName().startsWith(name)
        && System.getProperty("generator-mode") == null) {
      throw new IllegalThreadStateException("Not in thread " + name);
    }
  }
}
