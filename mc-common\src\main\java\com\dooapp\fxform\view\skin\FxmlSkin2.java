package com.dooapp.fxform.view.skin;

import com.dooapp.fxform.FXForm;
import com.dooapp.fxform.view.NodeCreationException;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.scene.Node;

import java.io.IOException;
import java.net.URL;
import java.util.ResourceBundle;

public class FxmlSkin2 extends NodeSkin {

  private final URL url;

  private FXMLLoader fxmlLoader;

  private Initializable controller;

  private ResourceBundle extraResourceBundle;

  public FxmlSkin2(FXForm<?> fxForm, URL url) {
    this(fxForm, url, null, null);
  }

  /**
   * Constructor.
   */
  public FxmlSkin2(FXForm<?> fxForm, URL url, ResourceBundle extraResourceBundle) {
    this(fxForm, url, null, extraResourceBundle);
  }

  /**
   * Constructor.
   */
  public FxmlSkin2(FXForm<?> fxForm, URL url, Initializable controller) {
    this(fxForm, url, controller, null);
  }

  /**
   * Constructor.
   */
  public FxmlSkin2(FXForm<?> fxForm, URL url, Initializable controller,
      ResourceBundle extraResourceBundle) {
    super(fxForm);
    this.url = url;
    this.controller = controller;
    this.extraResourceBundle = extraResourceBundle;
    setOnCreateNode(this::loadFxml);
    buildNode();
  }

  protected Node loadFxml() throws NodeCreationException {
    fxmlLoader = new FXMLLoader();
    fxmlLoader.setLocation(url);
    fxmlLoader.setController(controller);
    fxmlLoader.setResources(extraResourceBundle);
    try {
      return fxmlLoader.load();
    } catch (IOException ex) {
      throw new NodeCreationException(ex.getMessage(), ex);
    }
  }

}
