package com.mc.tool.framework.utility;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import java.util.Arrays;
import lombok.Getter;
import lombok.Setter;

/**
 * .
 */
@SuppressFBWarnings(value = {"EI_EXPOSE_REP", "EI_EXPOSE_REP2"})
public class TypeWrapper {
  @Getter
  @Setter
  private String name;
  @Getter
  @Setter
  private String type;
  @Getter
  @Setter
  private String logoUrl;
  @Getter
  @Setter
  private String[] params;

  /**
   * Constructor.
   *
   * @param name    name
   * @param type    type
   * @param logoUrl logo url
   */
  public TypeWrapper(String name, String type, String logoUrl, String... params) {
    this.name = name;
    this.type = type;
    this.logoUrl = logoUrl;
    this.params = Arrays.copyOf(params, params.length);
  }


}
