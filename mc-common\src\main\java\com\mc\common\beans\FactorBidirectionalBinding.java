package com.mc.common.beans;

import javafx.beans.WeakListener;
import javafx.beans.property.DoubleProperty;
import javafx.beans.property.Property;
import javafx.beans.value.ChangeListener;
import javafx.beans.value.ObservableValue;

import java.lang.ref.WeakReference;

public abstract class FactorBidirectionalBinding implements ChangeListener<Number>, WeakListener {

  private final int cachedHashCode;
  private final FactorType type;

  private FactorBidirectionalBinding(Object property1, Object property2, Object factorProperty,
      FactorType type) {
    cachedHashCode = property1.hashCode() * property2.hashCode() * factorProperty.hashCode();
    this.type = type;
  }

  protected abstract Object getProperty1();

  protected abstract Object getProperty2();

  protected abstract Object getFactoryProperty();

  protected FactorType getFactorType() {
    return type;
  }

  @Override
  public int hashCode() {
    return cachedHashCode;
  }

  public boolean wasGarbageCollected() {
    return getProperty1() == null || getProperty2() == null || getFactoryProperty() == null;
  }

  @Override
  public boolean equals(Object obj) {
    if (this == obj) {
      return true;
    }

    final Object propertyA1 = getProperty1();
    final Object propertyA2 = getProperty2();
    if (propertyA1 == null || propertyA2 == null) {
      return false;
    }

    if (obj instanceof FactorBidirectionalBinding) {
      final FactorBidirectionalBinding otherBinding = (FactorBidirectionalBinding) obj;
      final Object propertyB1 = otherBinding.getProperty1();
      final Object propertyB2 = otherBinding.getProperty2();
      if (propertyB1 == null || propertyB2 == null) {
        return false;
      }

      if (propertyA1 == propertyB1 && propertyA2 == propertyB2) {
        return true;
      }
      if (propertyA1 == propertyB2 && propertyA2 == propertyB1) {
        return true;
      }
    }
    return false;
  }

  private static void checkParameters(Object property1, Object property2) {
    if (property1 == null || property2 == null) {
      throw new NullPointerException("Both properties must be specified.");
    }
    if (property1 == property2) {
      throw new IllegalArgumentException("Cannot bind property to itself");
    }
  }

  /**
   * 绑定值，如果property1改变，property2相应的改，如果property2改，property1就改.
   * 如果factorProperty改，property1改，值的计算方式有factor type决定.
   * @param property1 property1
   * @param property2 properyt2
   * @param factorProperty factor property
   * @param type 计算类型.
   * @return bingding
   */
  public static FactorBidirectionalBinding bind(Property<Number> property1,
      Property<Number> property2, Property<Number> factorProperty, FactorType type) {
    checkParameters(property1, property2);
    final FactorBidirectionalBinding binding =
        (property1 instanceof DoubleProperty && property2 instanceof DoubleProperty)
            && (factorProperty instanceof DoubleProperty)
                ? new FactorBidirectionalDoubleBinding((DoubleProperty) property1,
                    (DoubleProperty) property2, (DoubleProperty) factorProperty, type)
                : null;
    property1.setValue(type.getNewValue(property2.getValue(), factorProperty.getValue()));
    property1.addListener(binding);
    property2.addListener(binding);
    factorProperty.addListener(binding);

    return binding;
  }

  /**
   * 取消绑定.
   * @param property1 property1
   * @param property2 property2
   * @param factorProperty factor property
   */
  public static <T> void unbind(Property<Number> property1, Property<Number> property2,
      Property<Number> factorProperty) {
    checkParameters(property1, property2);
    final FactorBidirectionalBinding binding =
        new UntypedGenericBidirectionalBinding(property1, property2, factorProperty);
    property1.removeListener(binding);
    property2.removeListener(binding);
    factorProperty.removeListener(binding);
  }

  private static class FactorBidirectionalDoubleBinding extends FactorBidirectionalBinding {
    private final WeakReference<DoubleProperty> propertyRef1;
    private final WeakReference<DoubleProperty> propertyRef2;
    private final WeakReference<DoubleProperty> factorPropertyRef;
    private boolean updating = false;

    private FactorBidirectionalDoubleBinding(DoubleProperty property1, DoubleProperty property2,
        DoubleProperty factorProperty, FactorType type) {
      super(property1, property2, factorProperty, type);
      propertyRef1 = new WeakReference<DoubleProperty>(property1);
      propertyRef2 = new WeakReference<DoubleProperty>(property2);
      factorPropertyRef = new WeakReference<DoubleProperty>(factorProperty);
    }

    @Override
    protected Property<Number> getProperty1() {
      return propertyRef1.get();
    }

    @Override
    protected Property<Number> getProperty2() {
      return propertyRef2.get();
    }

    @Override
    protected Object getFactoryProperty() {
      return factorPropertyRef.get();
    }

    public void changed(ObservableValue<? extends Number> sourceProperty, Number oldValue,
        Number newValue) {
      if (!updating) {
        final DoubleProperty property1 = propertyRef1.get();
        final DoubleProperty property2 = propertyRef2.get();
        final DoubleProperty factorProperty = factorPropertyRef.get();
        if (property1 == null || property2 == null || factorProperty == null) {
          if (property1 != null) {
            property1.removeListener(this);
          }
          if (property2 != null) {
            property2.removeListener(this);
          }
          if (factorProperty != null) {
            factorProperty.removeListener(this);
          }
        } else {
          try {
            updating = true;
            if (property1 == sourceProperty) {
              property2.set(getFactorType()
                  .getRevertNewValue(newValue.doubleValue(), factorProperty.get()).doubleValue());
            } else if (property2 == sourceProperty) {
              property1.set(getFactorType()
                  .getNewValue(newValue.doubleValue(), factorProperty.get()).doubleValue());
            } else if (factorProperty == sourceProperty) {
              property1.set(getFactorType().getNewValue(property2.get(), newValue.doubleValue())
                  .doubleValue());
            }
          } catch (RuntimeException exception) {
            try {
              if (property1 == sourceProperty) {
                property1.set(oldValue.doubleValue());
              } else if (property2 == sourceProperty) {
                property2.set(oldValue.doubleValue());
              } else if (factorProperty == sourceProperty) {
                factorProperty.set(oldValue.doubleValue());
              }
            } catch (Exception exception2) {
              exception2.addSuppressed(exception);
              unbind(property1, property2, factorProperty);
              throw new RuntimeException("Bidirectional binding failed together with an attempt"
                  + " to restore the source property to the previous value."
                  + " Removing the bidirectional binding from properties " + property1 + " and "
                  + property2, exception2);
            }
            throw new RuntimeException(
                "Bidirectional binding failed, setting to the previous value", exception);
          } finally {
            updating = false;
          }
        }
      }
    }
  }

  private static class UntypedGenericBidirectionalBinding extends FactorBidirectionalBinding {

    private final Object property1;
    private final Object property2;

    public UntypedGenericBidirectionalBinding(Object property1, Object property2, 
        Object factorProperty) {
      super(property1, property2, factorProperty, null);
      this.property1 = property1;
      this.property2 = property2;
    }

    @Override
    protected Object getProperty1() {
      return property1;
    }

    @Override
    protected Object getProperty2() {
      return property2;
    }

    public void changed(ObservableValue<? extends Number> sourceProperty, Number oldValue,
        Number newValue) {
      throw new RuntimeException("Should not reach here");
    }

    @Override
    protected Object getFactoryProperty() {
      return null;
    }
  }

  public enum FactorType {
    ADD {
      @Override
      public Number getNewValue(Number source, Number factor) {
        return add(source, factor);
      }

      @Override
      public Number getRevertNewValue(Number source, Number factor) {
        return sub(source, factor);
      }
    },
    SUB {
      @Override
      public Number getNewValue(Number source, Number factor) {
        return sub(source, factor);
      }

      @Override
      public Number getRevertNewValue(Number source, Number factor) {
        return add(source, factor);
      }
    },
    MULTIPLY {
      @Override
      public Number getNewValue(Number source, Number factor) {
        return multiply(source, factor);
      }

      @Override
      public Number getRevertNewValue(Number source, Number factor) {
        return divide(source, factor);
      }
    },
    DIVIDE {
      @Override
      public Number getNewValue(Number source, Number factor) {
        return divide(source, factor);
      }

      @Override
      public Number getRevertNewValue(Number source, Number factor) {
        return multiply(source, factor);
      }
    };

    public Number getNewValue(Number source, Number factor) {
      return 0;
    }

    public Number getRevertNewValue(Number source, Number factor) {
      return 0;
    }

    protected Number add(Number source, Number factor) {
      return source.doubleValue() + factor.doubleValue();
    }

    protected Number sub(Number source, Number factor) {
      return source.doubleValue() - factor.doubleValue();
    }

    protected Number multiply(Number source, Number factor) {
      return source.doubleValue() * factor.doubleValue();
    }

    protected Number divide(Number source, Number factor) {
      return source.doubleValue() / factor.doubleValue();
    }


  }
}
