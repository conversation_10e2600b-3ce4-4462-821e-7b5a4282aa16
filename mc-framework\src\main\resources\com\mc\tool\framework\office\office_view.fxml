<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.Label?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.Region?>
<?import javafx.scene.layout.StackPane?>
<?import javafx.scene.layout.VBox?>
<fx:root type="javafx.scene.layout.VBox" xmlns="http://javafx.com/javafx/8"
         xmlns:fx="http://javafx.com/fxml/1" stylesheets="@office_view.css" prefHeight="720" prefWidth="1280" id="root">
  <VBox id="body-pane" VBox.Vgrow="ALWAYS">
    <VBox id="function-content" VBox.Vgrow="ALWAYS">
      <HBox VBox.Vgrow="ALWAYS">

        <StackPane HBox.Hgrow="ALWAYS" fx:id="functionView">
          <HBox alignment="CENTER_LEFT">
            <Region HBox.Hgrow="ALWAYS"/>
            <VBox alignment="CENTER_RIGHT">
              <VBox id="function-seat" prefHeight="221" prefWidth="114"/>
              <Region prefHeight="5"/>
              <Label styleClass="function-seat-label" prefHeight="30" prefWidth="80" text="首长坐席"/>
            </VBox>
            <Region HBox.Hgrow="ALWAYS"/>
            <VBox alignment="CENTER_RIGHT">
              <VBox id="function-seat" prefHeight="221" prefWidth="114"/>
              <Region prefHeight="5"/>
              <Label styleClass="function-seat-label" prefHeight="30" prefWidth="80" text="普通坐席"/>
            </VBox>
            <Region HBox.Hgrow="ALWAYS"/>
            <VBox alignment="CENTER_RIGHT">
              <VBox id="function-seat" prefHeight="221" prefWidth="114"/>
              <Region prefHeight="5"/>
              <Label styleClass="function-seat-label" prefHeight="30" prefWidth="80" text="普通坐席"/>
            </VBox>
            <Region HBox.Hgrow="ALWAYS"/>
            <VBox alignment="CENTER_RIGHT">
              <VBox id="function-seat" prefHeight="221" prefWidth="114"/>
              <Region prefHeight="5"/>
              <Label styleClass="function-seat-label" prefHeight="30" prefWidth="80" text="普通坐席"/>
            </VBox>
            <Region HBox.Hgrow="ALWAYS"/>
            <VBox id="video-wall" prefWidth="261" prefHeight="866"/>
            <Region HBox.Hgrow="ALWAYS"/>
          </HBox>
        </StackPane>

      </HBox>
    </VBox>
  </VBox>


</fx:root>