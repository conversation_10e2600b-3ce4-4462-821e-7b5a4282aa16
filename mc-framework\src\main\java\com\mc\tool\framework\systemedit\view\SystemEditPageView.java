package com.mc.tool.framework.systemedit.view;

import com.mc.tool.framework.systemedit.controller.SystemEditControllable;
import com.mc.tool.framework.systemedit.datamodel.VisualEditModel;
import com.mc.tool.framework.utility.I18nUtility;
import com.mc.tool.framework.utility.InjectorProvider;
import java.io.IOException;
import java.net.URL;
import javafx.fxml.FXMLLoader;
import javafx.scene.layout.AnchorPane;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public final class SystemEditPageView extends AnchorPane {

  @Getter
  private SystemEditControllable controllable;

  /**
   * Contructor.
   */
  public SystemEditPageView(VisualEditModel model) {
    try {
      controllable = InjectorProvider.getInjector().getInstance(
          SystemEditControllable.class);
      controllable.initModel(model);
      URL location = getClass().getResource(
          "/com/mc/tool/framework/systemedit/system_edit_view.fxml");
      FXMLLoader loader = new FXMLLoader(location);
      loader.setController(controllable);
      loader.setResources(I18nUtility.getI18nBundle("systemedit"));
      loader.setRoot(this);
      loader.load();
    } catch (IOException exc) {
      log.warn("Can not load system_edit_view.fxml", exc);
    }
  }
}
