package com.mc.graph;

import com.mc.graph.connector.ConnectorIntegration;
import com.mc.graph.interfaces.CellObject;
import com.mc.graph.interfaces.Connector;
import com.mc.graph.interfaces.ConnectorIdentifier;

class McConnector extends AbstractGraphObject implements Connector {
  private ConnectorIdentifier id;
  private CellObject cell;

  public McConnector(ConnectorIdentifier id, CellObject cell) {
    this.id = id;
    this.cell = cell;
    visibleProperty.bind(cell.visibleProperty());
  }

  @Override
  public ConnectorIdentifier getId() {
    return id;
  }

  @Override
  public CellObject getCell() {
    return cell;
  }

  @Override
  public ConnectorIntegration getConnectorIntegration() {
    return null;
  }

  @Override
  public void destroy() {
  }

}