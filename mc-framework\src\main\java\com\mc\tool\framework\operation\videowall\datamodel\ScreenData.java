package com.mc.tool.framework.operation.videowall.datamodel;

import com.google.gson.annotations.Expose;
import com.mc.tool.framework.operation.videowall.datamodel.interfaces.ScreenObject;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleObjectProperty;
import lombok.Getter;

/**
 * .
 */
public class ScreenData implements ScreenObject {
  @Getter
  @Expose
  private ObjectProperty<VisualEditTerminal> target = new SimpleObjectProperty<>();

  @Override
  public void copyTo(ScreenObject input) {
    if (!(input instanceof ScreenData)) {
      return;
    }
    ScreenData screenData = (ScreenData) input;
    screenData.target.set(target.get());
  }

  @Override
  public boolean isEmpty() {
    return target.get() == null || !target.get().isRx();
  }
}
