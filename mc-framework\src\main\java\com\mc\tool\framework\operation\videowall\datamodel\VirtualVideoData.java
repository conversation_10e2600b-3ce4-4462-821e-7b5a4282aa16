package com.mc.tool.framework.operation.videowall.datamodel;

import com.google.gson.annotations.Expose;
import com.mc.tool.framework.operation.videowall.datamodel.interfaces.VideoObject;
import javafx.beans.property.DoubleProperty;
import javafx.beans.property.SimpleDoubleProperty;
import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.StringProperty;
import lombok.Getter;

/**
 * .
 */
public class VirtualVideoData {
  @Expose
  @Getter
  private StringProperty name = new SimpleStringProperty();
  @Expose
  @Getter
  private DoubleProperty xpos = new SimpleDoubleProperty();
  @Expose
  @Getter
  private DoubleProperty ypos = new SimpleDoubleProperty();
  @Expose
  @Getter
  private DoubleProperty width = new SimpleDoubleProperty();
  @Expose
  @Getter
  private DoubleProperty height = new SimpleDoubleProperty();

  /**
   * 把数据转换成实际的视频数据.
   *
   * @param totalWidth  视频墙的总宽度
   * @param totalHeight 视频墙的总高度
   * @return 实际的视频数据
   */
  public VideoObject toVideoData(VideoObject newVideo, int totalWidth, int totalHeight) {
    VideoObject data = newVideo;
    data.getName().set(name.get());
    data.getXpos().set((int) (xpos.get() * totalWidth));
    data.getYpos().set((int) (ypos.get() * totalHeight));
    data.getWidth().set((int) (width.get() * totalWidth));
    data.getHeight().set((int) (height.get() * totalHeight));
    return data;
  }
}
