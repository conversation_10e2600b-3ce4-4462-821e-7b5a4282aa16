package com.mc.tool.framework.systemedit.behavior;

import com.mc.graph.DefaultNewLinkBehavior;
import com.mc.graph.interfaces.Connector;
import com.mc.graph.interfaces.ConnectorIdentifier;
import com.mc.graph.interfaces.ConnectorSkin;
import com.mc.graph.interfaces.GraphController;
import com.mc.graph.interfaces.NewLinkSkin;
import com.mc.graph.util.NodeUtil;
import com.mc.tool.framework.systemedit.controller.SystemEditControllable;
import com.mc.tool.framework.systemedit.datamodel.VisualEditGroup;
import com.mc.tool.framework.systemedit.datamodel.VisualEditMatrix;
import com.mc.tool.framework.systemedit.datamodel.VisualEditNode;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import com.mc.tool.framework.utility.TypeWrapper;
import java.util.Collection;
import javafx.event.ActionEvent;
import javafx.event.EventHandler;
import javafx.scene.Node;
import javafx.scene.control.ContextMenu;
import javafx.scene.control.Menu;
import javafx.scene.control.MenuItem;
import javafx.scene.input.MouseEvent;
import javafx.util.Pair;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class SystemEditNewLinkBehavior extends DefaultNewLinkBehavior {
  protected SystemEditControllable controllable;
  protected Pair<VisualEditTerminal, ConnectorIdentifier> connectableRx;
  protected Pair<VisualEditTerminal, ConnectorIdentifier> connectableTx;
  protected Connector linkableSenderConnector;
  protected Connector linkableReceiverConnector;
  protected ContextMenu contextMenu = new ContextMenu();

  public SystemEditNewLinkBehavior(SystemEditControllable controllable, GraphController controller,
                                   NewLinkSkin linkSkin) {
    super(controller, linkSkin);
    this.controllable = controllable;
  }

  protected void resetCache() {
    connectableTx = null;
    connectableRx = null;
    linkableSenderConnector = null;
    linkableReceiverConnector = null;
  }

  @Override
  protected void onDragedReceiver(MouseEvent event) {
    resetCache();
    Node selectedConnector = NodeUtil.getNode(linkSkin.getParent(), event.getSceneX(),
        event.getSceneY(), ConnectorSkin.class);

    if (selectedConnector == null || selectedConnector == controller.getSkinManager()
        .getConnectorSkin(linkSkin.getSender()).getNode()) {
      linkSkin.setNoLink();
      return;
    }

    ConnectorSkin receiver = (ConnectorSkin) selectedConnector.getUserData();
    VisualEditNode receiverNode =
        (VisualEditNode) (receiver.getConnector().getCell().getBindedObject());
    Connector receiverConnector = receiver.getConnector();
    VisualEditNode senderNode = (VisualEditNode) linkSkin.getSender().getCell().getBindedObject();
    Connector senderConnector = linkSkin.getSender();
    // group不能connect与link
    if (receiverNode instanceof VisualEditGroup || senderNode instanceof VisualEditGroup) {
      linkSkin.setNoLink();
      return;
    }

    boolean linkable;
    boolean connectable = false;
    // 多种情况可连接 1. rx与tx的端口可connect 2. rx与连接tx的matrix端口可connect，反之亦然 3.连接rx的matrix端口与连接tx的matrix的端口可connect
    // 多种情况可链接 1. 空的rx/tx的端口与空的matrix端口

    Pair<VisualEditTerminal, ConnectorIdentifier> senderTerminalInfo =
        getTerminal(senderNode, senderConnector.getId());
    Pair<VisualEditTerminal, ConnectorIdentifier> receiverTerminalInfo =
        getTerminal(receiverNode, receiverConnector.getId());
    if (senderTerminalInfo.getKey() == null || receiverTerminalInfo.getKey() == null) {
      connectable = false;
    } else if (senderTerminalInfo.getKey().isRx() && receiverTerminalInfo.getKey().isTx()) {
      connectable =
          controllable.isConnectable(senderTerminalInfo.getKey(), senderTerminalInfo.getValue(),
              receiverTerminalInfo.getKey(), receiverTerminalInfo.getValue());
    } else if (senderTerminalInfo.getKey().isTx() && receiverTerminalInfo.getKey().isRx()) {
      connectable =
          controllable.isConnectable(receiverTerminalInfo.getKey(), receiverTerminalInfo.getValue(),
              senderTerminalInfo.getKey(), senderTerminalInfo.getValue());
    }

    linkable = controllable.isLinkable(senderNode, senderConnector.getId(), receiverNode,
        receiverConnector.getId());

    if (connectable) {
      if (senderTerminalInfo.getKey().isTx() && receiverTerminalInfo.getKey().isRx()) {
        connectableTx = senderTerminalInfo;
        connectableRx = receiverTerminalInfo;
      } else if (senderTerminalInfo.getKey().isRx() && receiverTerminalInfo.getKey().isTx()) {
        connectableRx = senderTerminalInfo;
        connectableTx = receiverTerminalInfo;
      }
    }

    if (linkable) {
      linkableSenderConnector = senderConnector;
      linkableReceiverConnector = receiverConnector;
    }

    if (connectable || linkable) {
      linkSkin.setLinkable();
    } else {
      linkSkin.setNoLink();
    }

  }

  private Pair<VisualEditTerminal, ConnectorIdentifier> getTerminal(VisualEditNode node,
                                                                    ConnectorIdentifier connector) {
    if (node instanceof VisualEditTerminal) {
      return new Pair<>((VisualEditTerminal) node, connector);
    } else if (node instanceof VisualEditMatrix) {
      VisualEditMatrix matrix = (VisualEditMatrix) node;
      VisualEditTerminal terminal = matrix.getTerminal(connector);
      ConnectorIdentifier newConnector = matrix.getTermianlPort(connector);
      return new Pair<>(terminal, newConnector);
    } else {
      return new Pair<>(null, null);
    }
  }

  @Override
  protected void onReleasedReceiver(MouseEvent event) {
    if (linkableSenderConnector != null && linkableReceiverConnector != null) {
      controller.connect(linkableSenderConnector, linkableReceiverConnector, true);
    }

    if (connectableRx != null && connectableTx != null) {
      Collection<TypeWrapper> types = controllable.getConnectableType(connectableRx.getKey(),
          connectableRx.getValue(), connectableTx.getKey(), connectableTx.getValue());
      if (types.isEmpty()) {
        return;
      }
      Collection<TypeWrapper> channels =
          controllable.getMultiviewChannel(connectableRx.getKey(), connectableTx.getKey());
      contextMenu.getItems().clear();
      if (channels.isEmpty()) {
        for (TypeWrapper typeWrapper : types) {
          MenuItem item = new MenuItem(typeWrapper.getName());
          item.setOnAction(new ConnectEventHandler(controllable, connectableRx, connectableTx,
              typeWrapper.getType()));
          contextMenu.getItems().add(item);
        }
      } else {
        channels.forEach(channel -> {
          Menu menu = new Menu(channel.getName());
          for (TypeWrapper type : types) {
            MenuItem item = new MenuItem(type.getName());
            item.setOnAction(new ConnectEventHandler(controllable, connectableRx, connectableTx,
                type.getType(), Integer.parseInt(channel.getType()) + 1));
            menu.getItems().add(item);
          }
          contextMenu.getItems().add(menu);
        });
      }
      contextMenu.show(((Node) event.getSource()).getScene().getWindow(), event.getScreenX(),
          event.getScreenY());
    }

    resetCache();
    linkSkin.remove();
  }

  @AllArgsConstructor
  static class ConnectEventHandler implements EventHandler<ActionEvent> {
    private SystemEditControllable controllable;
    private Pair<VisualEditTerminal, ConnectorIdentifier> connectableRx;
    private Pair<VisualEditTerminal, ConnectorIdentifier> connectableTx;
    private String mode;
    private int rxChannel;

    public ConnectEventHandler(SystemEditControllable controllable,
                               Pair<VisualEditTerminal, ConnectorIdentifier> rx,
                               Pair<VisualEditTerminal, ConnectorIdentifier> tx, String mode) {
      this.controllable = controllable;
      this.connectableRx = rx;
      this.connectableTx = tx;
      this.mode = mode;
    }

    @Override
    public void handle(ActionEvent event) {
      controllable.connect(connectableTx.getKey(), connectableTx.getValue(), connectableRx.getKey(),
          connectableRx.getValue(), mode, rxChannel);
    }

  }
}
