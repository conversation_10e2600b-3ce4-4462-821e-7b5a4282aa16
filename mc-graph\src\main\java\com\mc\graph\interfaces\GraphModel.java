package com.mc.graph.interfaces;

import javafx.collections.ObservableList;

public interface GraphModel {
  ObservableList<LinkObject> getObservableLinks();
  
  ObservableList<CellObject> getObservableCells();
  
  void beginUpdate();
  
  void endUpdate();
  
  boolean hasCell(CellObject cellObject);
  
  /**
   * 通过CellBindedObject来找CellObject.
   * @param bindedObject cell binded object
   * @return 如果有找到，返回相应的CellObject，否则返回null
   */
  CellObject findCell(CellBindedObject bindedObject);
}
