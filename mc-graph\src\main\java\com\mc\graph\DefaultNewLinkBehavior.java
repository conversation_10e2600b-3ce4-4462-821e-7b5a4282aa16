package com.mc.graph;

import com.mc.graph.interfaces.ConnectorSkin;
import com.mc.graph.interfaces.GraphController;
import com.mc.graph.interfaces.LinkBehavior;
import com.mc.graph.interfaces.NewLinkSkin;
import com.mc.graph.util.MouseControlUtil;
import com.mc.graph.util.NodeUtil;
import javafx.scene.Node;
import javafx.scene.input.MouseEvent;

public class DefaultNewLinkBehavior implements LinkBehavior {
  protected NewLinkSkin linkSkin;
  protected GraphController controller;

  public DefaultNewLinkBehavior(GraphController controller, NewLinkSkin linkSkin) {
    this.linkSkin = linkSkin;
    this.controller = controller;
  }

  @Override
  public void attach() {
    MouseControlUtil.makeDraggable(linkSkin.getReceiverUi(),
        this::onDragedReceiver,
        event -> {
          linkSkin.getReceiverUi().layoutXProperty().unbind();
          linkSkin.getReceiverUi().layoutYProperty().unbind();
        }, true, null);

    linkSkin.getReceiverUi().onMouseReleasedProperty().set(this::onReleasedReceiver);
  }

  @Override
  public void detach() {
    linkSkin.getReceiverUi().onMousePressedProperty().set(null);
    linkSkin.getReceiverUi().onMouseDraggedProperty().set(null);
    linkSkin.getReceiverUi().onMouseReleasedProperty().set(null);
  }

  protected void onDragedReceiver(MouseEvent event) {
    Node selectedConnector = NodeUtil.getNode(linkSkin.getParent(), event.getSceneX(),
        event.getSceneY(), ConnectorSkin.class);
    if (selectedConnector == null || selectedConnector == controller.getSkinManager()
        .getConnectorSkin(linkSkin.getSender()).getNode()) {
      linkSkin.setNoLink();
    } else {
      linkSkin.setLinkable();
    }
  }

  protected void onReleasedReceiver(MouseEvent event) {
    Node selectedConnector = NodeUtil.getNode(linkSkin.getParent(), event.getSceneX(),
        event.getSceneY(), ConnectorSkin.class);
    if (selectedConnector != null) {
      ConnectorSkin skin = (ConnectorSkin) selectedConnector.getUserData();
      controller.connect(linkSkin.getSender(), skin.getConnector(), true);
    }
    linkSkin.remove();
  }

}
