package com.mc.tool.caesar.vpm;

import com.google.inject.Binder;
import com.google.inject.Module;
import com.mc.tool.caesar.vpm.device.CaesarDemoDataModelFactory;
import com.mc.tool.caesar.vpm.device.CaesarDemoDataModelFactoryImpl;
import com.mc.tool.caesar.vpm.device.FileUserInfoFactory;
import com.mc.tool.caesar.vpm.device.FileUserInfoFactoryImpl;
import com.mc.tool.caesar.vpm.pages.office.controller.CaesarOfficeController;
import com.mc.tool.caesar.vpm.pages.operation.crossscreen.controller.CaesarCrossScreenController;
import com.mc.tool.caesar.vpm.pages.operation.irregularcrossscreen.controller.CaesarIrregularCrossScreenController;
import com.mc.tool.caesar.vpm.pages.operation.irregularcrossscreen.controller.IrregularCrossScreenControllable;
import com.mc.tool.caesar.vpm.pages.operation.seat.controller.CaesarSeatController;
import com.mc.tool.caesar.vpm.pages.operation.videowall.controller.CaesarVideoWallController;
import com.mc.tool.caesar.vpm.pages.systemedit.controller.CaesarSystemEditController;
import com.mc.tool.caesar.vpm.view.CaesarFrameController;
import com.mc.tool.framework.controller.FrameController;
import com.mc.tool.framework.interfaces.ApplicationBase;
import com.mc.tool.framework.office.controller.OfficeControllable;
import com.mc.tool.framework.operation.crossscreen.controller.CrossScreenControllable;
import com.mc.tool.framework.operation.seat.controller.SeatControllable;
import com.mc.tool.framework.operation.videowall.controller.VideoWallControllable;
import com.mc.tool.framework.systemedit.controller.SystemEditControllable;
import com.mc.tool.framework.utility.dialogues.FileDialogueFacotryImpl;
import com.mc.tool.framework.utility.dialogues.FileDialogueFactory;

/**
 * .
 */
public class CaesarModule implements Module {

  @Override
  public void configure(Binder binder) {
    binder.bind(ApplicationBase.class).to(CaesarApplication.class);
    binder.bind(FrameController.class).to(CaesarFrameController.class);
    binder.bind(SystemEditControllable.class).to(CaesarSystemEditController.class);
    binder.bind(VideoWallControllable.class).to(CaesarVideoWallController.class);
    binder.bind(OfficeControllable.class).to(CaesarOfficeController.class);
    binder.bind(SeatControllable.class).to(CaesarSeatController.class);
    binder.bind(CrossScreenControllable.class).to(CaesarCrossScreenController.class);
    binder
        .bind(IrregularCrossScreenControllable.class)
        .to(CaesarIrregularCrossScreenController.class);
    binder.bind(FileDialogueFactory.class).to(FileDialogueFacotryImpl.class);
    binder.bind(CaesarDemoDataModelFactory.class).to(CaesarDemoDataModelFactoryImpl.class);
    binder.bind(FileUserInfoFactory.class).to(FileUserInfoFactoryImpl.class);
  }
}
