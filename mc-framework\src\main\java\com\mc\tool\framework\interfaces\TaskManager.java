package com.mc.tool.framework.interfaces;

import javafx.concurrent.Task;

/**
 * .
 */
public interface TaskManager {
  /**
   * Add a task run in background. If needed, can show progress in status bar.
   *
   * @param task the task to run.
   * @return if add successful, return true.
   */
  boolean addBackgroundTask(Task<?> task);

  /**
   * Add a task run in foreground. The gui can not be operated until the task is finish.
   *
   * @param task the task to run.
   * @return if add successful, return true.
   */
  boolean addForegroundTask(Task<?> task);

  void exit();
}
