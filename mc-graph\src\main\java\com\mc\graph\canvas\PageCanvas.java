package com.mc.graph.canvas;

import com.mc.common.beans.BackgroundColorStyleBinding;
import com.mc.graph.ExtendedScrollPaneSkin;
import com.mc.graph.interfaces.OverviewableGraphCanvas;
import javafx.application.Platform;
import javafx.beans.binding.StringBinding;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.DoubleProperty;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.beans.property.SimpleDoubleProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.beans.property.StringProperty;
import javafx.beans.value.ChangeListener;
import javafx.beans.value.ObservableValue;
import javafx.collections.FXCollections;
import javafx.collections.ListChangeListener;
import javafx.collections.ObservableList;
import javafx.geometry.Bounds;
import javafx.geometry.Point2D;
import javafx.geometry.Pos;
import javafx.geometry.Rectangle2D;
import javafx.scene.Group;
import javafx.scene.Node;
import javafx.scene.Parent;
import javafx.scene.control.Label;
import javafx.scene.control.ScrollPane;
import javafx.scene.control.Skin;
import javafx.scene.control.Tooltip;
import javafx.scene.effect.BlendMode;
import javafx.scene.layout.Pane;
import javafx.scene.layout.StackPane;
import javafx.scene.paint.Color;
import javafx.scene.shape.Rectangle;
import javafx.scene.shape.StrokeType;
import javafx.scene.transform.Scale;
import lombok.Getter;

import java.util.Collection;

/**
 * 显示多页面的Canvas.
 */
public class PageCanvas extends ScrollPane implements OverviewableGraphCanvas {
  private static final int MIN_PADDING = 10;

  private SimpleDoubleProperty scaleProperty = new SimpleDoubleProperty(1);
  private SimpleDoubleProperty nodeScaleProperty = new SimpleDoubleProperty(1);
  private SimpleDoubleProperty horizontalVisibleProperty = new SimpleDoubleProperty(1);
  private SimpleDoubleProperty verticalVisibleProperty = new SimpleDoubleProperty(1);

  private Scale scale = new Scale();

  private ObservableList<Rectangle2D> allAreas = FXCollections.observableArrayList();
  private ObjectProperty<Color> bgColor =
      new SimpleObjectProperty<>(new Color(0x90 / 255.0, 0x99 / 255.0, 0xae / 255.0, 1));
  private ObjectProperty<Color> pageColor = new SimpleObjectProperty<>(Color.WHITE);

  private ObjectProperty<Color> pageStrokeColor = new SimpleObjectProperty<>(Color.BLACK);
  private ObjectProperty<Color> borderColor = new SimpleObjectProperty<>(new Color(1, 0, 0, 1));
  private BooleanProperty graphVisible = new SimpleBooleanProperty(true);
  private ObjectProperty<Color> gridHighLightColor =
      new SimpleObjectProperty<>(new Color(0.9, 0.1, 0.2, 0.2));

  private ObjectProperty<Color> gridSelectedColor =
      new SimpleObjectProperty<>(new Color(0.1, 0.9, 0.2, 0.2));

  private ObservableList<Rectangle2D> highLightAreas = FXCollections.observableArrayList();

  private ObservableList<Rectangle2D> selectedAreas =
      FXCollections.observableArrayList();

  private ObservableList<PageText> pageTexts = FXCollections.observableArrayList();


  private final InnerCanvas innerCanvas;

  /**
   * Constructor.
   */
  public PageCanvas() {

    innerCanvas = createInnerCanvas();
    this.setContent(innerCanvas.getContainer());

    this.viewportBoundsProperty().addListener(new ChangeListener<Bounds>() {

      @Override
      public void changed(ObservableValue<? extends Bounds> observable, Bounds oldValue,
                          Bounds newValue) {
        innerCanvas.getScrollContent().setMinSize(newValue.getWidth(), newValue.getHeight());
        double width = newValue.getWidth();
        double height = newValue.getHeight();
        double nodeScale = nodeScaleProperty.get();

        double maxScale = computeNodeScale(width + 2 * MIN_PADDING, height + 2 * MIN_PADDING);
        double minScale = computeNodeScale(width - 2 * MIN_PADDING, height - 2 * MIN_PADDING);

        if (nodeScale > maxScale || nodeScale < minScale) {
          fitToView();
        }
      }
    });

    innerCanvas.getZoomContent().getTransforms().add(scale);
    scale.xProperty().bindBidirectional(scaleProperty);
    scale.yProperty().bindBidirectional(scaleProperty);
    scale.pivotXProperty().bind(innerCanvas.getScrollContent().widthProperty().divide(2));
    scale.pivotYProperty().bind(innerCanvas.getScrollContent().heightProperty().divide(2));

    styleProperty().bind(new BackgroundColorStyleBinding(bgColor));

  }

  @Override
  public boolean isCellScaleEnable() {
    return true;
  }

  @Override
  public DoubleProperty getCellScaleProperty() {
    return nodeScaleProperty;
  }

  public void setAllPageAreas(Collection<Rectangle2D> areas) {
    allAreas.setAll(areas);
  }

  public void setHighLightAreas(Collection<Rectangle2D> areas) {
    highLightAreas.setAll(areas);
  }

  public void setSelectedAreas(Collection<Rectangle2D> areas) {
    selectedAreas.setAll(areas);
  }

  public void setPageTexts(Collection<PageText> pageTexts) {
    this.pageTexts.setAll(pageTexts);
  }

  public void setGraphVisible(boolean visible) {
    graphVisible.set(visible);
  }

  public boolean isGraphVisible() {
    return graphVisible.get();
  }

  public int getPageWidth() {
    return PageCanvasUtility.getPageWidth(allAreas);
  }

  public int getPageHeight() {
    return PageCanvasUtility.getPageHeight(allAreas);
  }

  /**
   * 根据索引获取页面的未缩放的区域.
   *
   * @param index 索引
   * @return 未缩放的区域
   */
  public Rectangle2D getOriginPageAreaByIndex(int index) {
    if (index < 0 || index >= allAreas.size()) {
      return null;
    } else {
      return allAreas.get(index);
    }
  }

  /**
   * 获取包含location的页面的未缩放区域.
   *
   * @param location 点
   * @return 包含location的所有页的区域
   */
  public Collection<Rectangle2D> getIntersectedPageOriginAreas(Point2D location) {
    double scale = nodeScaleProperty.get();
    return PageCanvasUtility.getIntersectedAreas(allAreas,
        new Point2D(location.getX() / scale, location.getY() / scale));
  }

  /**
   * 获取与rect相交的页面的未缩放区域.
   *
   * @param rect rect
   * @return 与rect相交的所有页的区域
   */
  public Collection<Rectangle2D> getIntersectedPageOriginAreas(Rectangle2D rect) {
    double scale = nodeScaleProperty.get();
    return PageCanvasUtility.getIntersectedAreas(allAreas,
        new Rectangle2D(rect.getMinX() / scale, rect.getMinY() / scale, rect.getWidth() / scale,
            rect.getHeight() / scale));
  }

  /**
   * 获取包含location的页面的索引.
   *
   * @param location 点
   * @return 包好location的所有页面的索引
   */
  public Collection<Integer> getIntersectedPageAreasIndex(Point2D location) {
    double scale = nodeScaleProperty.get();
    return PageCanvasUtility.getIntersectedAreasIndex(allAreas,
        new Point2D(location.getX() / scale, location.getY() / scale));
  }

  /**
   * 获取与rect相交的所有页面的未缩放区域，并合并成一个区域.
   *
   * @param rect rect
   * @return 合并后的区域
   */
  public Rectangle2D getMergedIntersectedPageOriginArea(Rectangle2D rect) {
    double scale = nodeScaleProperty.get();
    return PageCanvasUtility.mergeAreas(PageCanvasUtility.getIntersectedAreas(allAreas,
        new Rectangle2D(rect.getMinX() / scale, rect.getMinY() / scale, rect.getWidth() / scale,
            rect.getHeight() / scale)));
  }

  @Override
  public void setBgColor(Color color) {
    bgColor.set(color);
  }

  @Override
  public Color getBgColor() {
    return bgColor.get();
  }


  public void setPageColor(Color color) {
    pageColor.set(color);
  }

  public void setPageStrokeColor(Color color) {
    pageStrokeColor.set(color);
  }

  public void setBorderColor(Color color) {
    borderColor.set(color);
  }

  /**
   * 缩放page，尽可能显示更多的内容.
   */
  @Override
  public void fitToView() {
    double newScale = computeNodeScale(getViewportBounds().getWidth(),
        getViewportBounds().getHeight());
    nodeScaleProperty.set(newScale);
    setHvalue(0.5);
    setVvalue(0.5);
    Platform.runLater(this::requestLayout);

    // move viewport so that old center remains in the center after the
    // scaling
    // repositionScroller(innerCanvas.getScrollContent(), this, scaleFactor, scrollOffset);
  }

  protected double computeNodeScale(double viewWidth, double viewHeight) {
    viewWidth -= 2 * MIN_PADDING;
    viewHeight -= 2 * MIN_PADDING;
    double widthScale = viewWidth / getPageWidth();
    double heightScale = viewHeight / getPageHeight();

    double newScale = Math.min(widthScale, heightScale);
    newScale = Math.max(Math.max(60.0 / PageCanvasUtility.getMinGridWidth(allAreas),
        60.0 / PageCanvasUtility.getMinGridHeight(allAreas)), newScale);
    return newScale;
  }


  @Override
  public Node getNode() {
    return this;
  }

  @Override
  public Parent getContainer() {
    return innerCanvas.getGraphContainer();
  }

  public Parent getBgCanvas() {
    return innerCanvas.getBgCanvas();
  }

  @Override
  public boolean setScale(double scale) {
    scaleProperty.set(scale);
    return true;
  }

  @Override
  public double getScale() {
    return scaleProperty.get();
  }

  @Override
  public DoubleProperty getScaleProperty() {
    return scaleProperty;
  }

  @Override
  protected Skin<?> createDefaultSkin() {
    ExtendedScrollPaneSkin skin = new ExtendedScrollPaneSkin(this);
    horizontalVisibleProperty
        .bindBidirectional(skin.getHorizontalScrollBar().visibleAmountProperty());
    verticalVisibleProperty.bindBidirectional(skin.getVerticalScrollBar().visibleAmountProperty());
    return skin;
  }

  protected InnerCanvas createInnerCanvas() {
    InnerCanvas canvas = new InnerCanvas(allAreas, highLightAreas, selectedAreas, pageTexts);
    canvas.bgColor.bind(bgColor);
    canvas.borderColor.bind(borderColor);
    canvas.pageColor.bind(pageColor);
    canvas.pageStrokeColor.bind(pageStrokeColor);
    canvas.gridHighLightColor.bind(gridHighLightColor);
    canvas.gridSelectedColor.bind(gridSelectedColor);
    canvas.nodeScaleProperty.bind(nodeScaleProperty);
    canvas.graphVisible.bind(graphVisible);
    return canvas;
  }

  @Override
  public OverviewCanvas createOverviewCanvas() {
    InnerCanvas canvas = createInnerCanvas();
    canvas.getScrollContent().minWidthProperty()
        .bind(innerCanvas.getScrollContent().minWidthProperty());
    canvas.getScrollContent().minHeightProperty()
        .bind(innerCanvas.getScrollContent().minHeightProperty());
    canvas.getScrollContent().maxWidthProperty()
        .bind(innerCanvas.getScrollContent().maxWidthProperty());
    canvas.getScrollContent().maxHeightProperty()
        .bind(innerCanvas.getScrollContent().maxHeightProperty());
    canvas.getScrollContent().prefWidthProperty()
        .bind(innerCanvas.getScrollContent().prefWidthProperty());
    canvas.getScrollContent().prefHeightProperty()
        .bind(innerCanvas.getScrollContent().prefHeightProperty());
    return canvas;
  }

  @Override
  public DoubleProperty getHorizontalPosProperty() {
    return hvalueProperty();
  }

  @Override
  public DoubleProperty getVerticalPosProperty() {
    return vvalueProperty();
  }

  @Override
  public DoubleProperty getHorizontalVisibleProperty() {
    return horizontalVisibleProperty;
  }

  @Override
  public DoubleProperty getVerticalVisibleProperty() {
    return verticalVisibleProperty;
  }

  static class InnerCanvas implements OverviewCanvas {

    @Getter
    private Pane bgCanvas = new Pane();
    @Getter
    private Pane fgCanvas = new Pane();
    @Getter
    private Pane graphContainer = new Pane();
    @Getter
    private StackPane scrollContent = new StackPane();
    @Getter
    private StackPane zoomContent = new StackPane();

    private Group scrollContentWrapper = new Group();

    @Getter
    private DoubleProperty nodeScaleProperty = new SimpleDoubleProperty();
    @Getter
    private ObjectProperty<Color> bgColor =
        new SimpleObjectProperty<>(new Color(0x90 / 255.0, 0x99 / 255.0, 0xae / 255.0, 1));
    @Getter
    private ObjectProperty<Color> pageColor = new SimpleObjectProperty<>(Color.WHITE);

    @Getter
    private ObjectProperty<Color> pageStrokeColor = new SimpleObjectProperty<>(Color.BLACK);
    @Getter
    private ObjectProperty<Color> borderColor = new SimpleObjectProperty<>(new Color(1, 0, 0, 1));

    @Getter
    private ObservableList<Rectangle2D> allAreas;
    @Getter
    private ObservableList<Rectangle2D> highlightAreas;
    @Getter
    private ObservableList<Rectangle2D> selectedAreas;
    @Getter
    private ObjectProperty<Color> gridHighLightColor = new SimpleObjectProperty<>();
    @Getter
    private ObjectProperty<Color> gridSelectedColor = new SimpleObjectProperty<>();
    @Getter
    private BooleanProperty graphVisible = new SimpleBooleanProperty(true);
    @Getter
    private ObservableList<PageText> pageTexts;


    public InnerCanvas(ObservableList<Rectangle2D> allAreas,
                       ObservableList<Rectangle2D> highlightAreas,
                       ObservableList<Rectangle2D> selectedAreas,
                       ObservableList<PageText> pageTexts) {
      this.allAreas = FXCollections.unmodifiableObservableList(allAreas);
      this.highlightAreas = FXCollections.unmodifiableObservableList(highlightAreas);
      this.selectedAreas = FXCollections.unmodifiableObservableList(selectedAreas);
      this.pageTexts = FXCollections.unmodifiableObservableList(pageTexts);

      StringBinding scrollContentStyleBinding = new BackgroundColorStyleBinding(bgColor);
      scrollContent.styleProperty().bind(scrollContentStyleBinding);

      StringBinding bgStyleBinding = new StringBinding() {
        {
          super.bind(borderColor);
        }

        @Override
        protected String computeValue() {
          return String.format("-fx-border-size:1; -fx-border-color:#%s;",
              borderColor.get().toString().substring(2));
        }
      };

      bgCanvas.styleProperty()
          .bind(bgStyleBinding.concat(new BackgroundColorStyleBinding(pageColor)));

      graphContainer.managedProperty().bind(graphContainer.visibleProperty());
      graphContainer.visibleProperty().bind(graphVisible);
      zoomContent.getChildren().add(bgCanvas);
      zoomContent.getChildren().add(graphContainer);
      zoomContent.getChildren().add(fgCanvas);
      scrollContent.getChildren().add(zoomContent);
      scrollContentWrapper.getChildren().add(scrollContent);

      fgCanvas.setPickOnBounds(false);

      allAreas.addListener((ListChangeListener<? super Rectangle2D>) change -> repaintPage());

      this.highlightAreas.addListener((ListChangeListener<Rectangle2D>) change -> repaintPage());

      this.selectedAreas.addListener(
          (ListChangeListener<Rectangle2D>) change -> repaintPage());

      this.pageTexts.addListener((ListChangeListener<PageText>) change -> repaintPage());

      nodeScaleProperty.addListener((observable, oldVal, newVal) -> {
        repaintPage();
      });
    }

    protected void addPageRegion(Rectangle2D rect, Color fillColor, Color strokeColor) {
      double nodeScale = nodeScaleProperty.doubleValue();
      int gridWidthVal = (int) rect.getWidth();
      int gridHeightVal = (int) rect.getHeight();
      int minx = (int) rect.getMinX();
      int miny = (int) rect.getMinY();
      Rectangle rectangle = new Rectangle();
      rectangle.setLayoutX(minx * nodeScale);
      rectangle.setLayoutY(miny * nodeScale);
      rectangle.setWidth(gridWidthVal * nodeScale);
      rectangle.setHeight(gridHeightVal * nodeScale);
      rectangle.setFill(fillColor);
      rectangle.setStroke(strokeColor);
      rectangle.setStrokeType(StrokeType.CENTERED);
      rectangle.setStrokeDashOffset(3);
      rectangle.setBlendMode(BlendMode.DARKEN);
      bgCanvas.getChildren().add(rectangle);
    }

    protected void addFgColorRegion(Rectangle2D rect, Color color) {
      double nodeScale = nodeScaleProperty.doubleValue();
      int gridWidthVal = (int) rect.getWidth();
      int gridHeightVal = (int) rect.getHeight();
      int minx = (int) rect.getMinX();
      int miny = (int) rect.getMinY();
      Rectangle rectangle = new Rectangle();
      rectangle.setLayoutX(minx * nodeScale);
      rectangle.setLayoutY(miny * nodeScale);
      rectangle.setWidth(gridWidthVal * nodeScale);
      rectangle.setHeight(gridHeightVal * nodeScale);
      rectangle.setFill(color);
      rectangle.setMouseTransparent(true);
      fgCanvas.getChildren().add(rectangle);
    }

    protected void addText(PageText text) {
      double nodeScale = nodeScaleProperty.doubleValue();
      Rectangle2D rect = text.getArea();

      int minx = (int) rect.getMinX();
      int miny = (int) rect.getMinY();
      Label textShape = new Label();
      textShape.textProperty().bind(text.getText());
      textShape.setLayoutX(minx * nodeScale + 5);
      textShape.setLayoutY(miny * nodeScale + 5);

      int gridWidthVal = (int) rect.getWidth();
      int gridHeightVal = (int) rect.getHeight();
      double width = Math.max(nodeScale * gridWidthVal - 10, 0);
      double height = Math.max(nodeScale * gridHeightVal - 10, 0);
      textShape.setMinSize(width, height);
      textShape.setMaxSize(width, height);
      textShape.setPrefSize(width, height);
      textShape.setAlignment(text.pos);
      textShape.setTextFill(text.color);
      textShape.setPickOnBounds(false);
      Tooltip tooltip = new Tooltip();
      tooltip.textProperty().bind(text.getText());
      Tooltip.install(textShape, tooltip);
      fgCanvas.getChildren().add(textShape);
    }

    protected void repaintPage() {
      double width = PageCanvasUtility.getPageWidth(allAreas);
      double height = PageCanvasUtility.getPageHeight(allAreas);

      double nodeScale = nodeScaleProperty.doubleValue();

      width = width * nodeScale;
      height = height * nodeScale;

      zoomContent.setMinSize(width, height);
      zoomContent.setMaxSize(width, height);

      graphContainer.setMinSize(width, height);
      graphContainer.setMaxSize(width, height);

      bgCanvas.setMinSize(width, height);
      bgCanvas.setMaxSize(width, height);

      fgCanvas.setMinSize(width, height);
      fgCanvas.setMaxSize(width, height);
      fgCanvas.getChildren().clear();
      bgCanvas.getChildren().clear();

      for (Rectangle2D value : allAreas) {
        addPageRegion(value, pageColor.get(), pageStrokeColor.get());
      }

      for (Rectangle2D value : highlightAreas) {
        addFgColorRegion(value, gridHighLightColor.get());
      }

      for (Rectangle2D value : selectedAreas) {
        addFgColorRegion(value, gridSelectedColor.get());
      }

      for (PageText pageText : pageTexts) {
        addText(pageText);
      }
    }

    @Override
    public Parent getContainer() {
      return scrollContentWrapper;
    }

    @Override
    public void relocateContainer(double xpos, double ypos, double scaleFactor) {
      double minx = 0;
      double miny = 0;
      for (Node node : graphContainer.getChildren()) {
        if (node.getLayoutX() < minx) {
          minx = node.getLayoutX();
        }
        if (node.getLayoutY() < miny) {
          miny = node.getLayoutY();
        }
      } //end for
      getContainer().setLayoutX(xpos - minx * scaleFactor);
      getContainer().setLayoutY(ypos - miny * scaleFactor);
    }
  }

  /**
   * 页面文字.
   */
  public static class PageText {

    @Getter
    private Rectangle2D area;


    @Getter
    private StringProperty text;

    @Getter
    private Color color = Color.BLACK;

    @Getter
    private Pos pos = Pos.TOP_LEFT;

    /**
     * 构建页面文字.
     *
     * @param area  文字显示的区域
     * @param text  文字内容
     * @param color 文字颜色
     * @param pos   文字位置
     */
    public PageText(Rectangle2D area, StringProperty text, Color color, Pos pos) {
      this.area = area;
      this.text = text;
      this.color = color;
      this.pos = pos;
    }
  }

  @Override
  public void centerRegion(Rectangle2D region) {
    throw new IllegalAccessError();
  }


}
