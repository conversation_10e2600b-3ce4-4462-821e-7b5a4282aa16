package com.mc.tool.framework.operation.view;

import com.mc.tool.framework.systemedit.datamodel.VisualEditModel;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import javafx.beans.binding.Bindings;
import javafx.beans.property.ObjectProperty;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.SnapshotParameters;
import javafx.scene.control.Label;
import javafx.scene.image.Image;
import javafx.scene.layout.Background;
import javafx.scene.layout.BackgroundFill;
import javafx.scene.layout.BackgroundImage;
import javafx.scene.layout.BackgroundPosition;
import javafx.scene.layout.BackgroundRepeat;
import javafx.scene.layout.BackgroundSize;
import javafx.scene.layout.HBox;
import javafx.scene.layout.VBox;
import javafx.scene.paint.Color;

/**
 * .
 */
public class VideoSourceListCellWithImage extends VideoSourceListCell {
  private VBox container;
  private VBox image;
  private Label itemText;

  private final VisualEditModel model;

  private static Background noImageBackground;

  static {
    noImageBackground = new Background(new BackgroundFill(Color.web("#7b7a80"), null, null));
  }

  /**
   * Constructor.
   *
   * @param model data model
   */
  public VideoSourceListCellWithImage(VisualEditModel model) {
    this.model = model;
    container = new VBox();
    container.setPadding(new Insets(10, 10, 10, 10));
    container.setSpacing(10);

    image = new VBox();
    image.setMinHeight(90);

    container.getChildren().add(image);

    HBox labelBox = new HBox();
    labelBox.setAlignment(Pos.CENTER);
    itemText = new Label();
    labelBox.getChildren().add(itemText);

    container.getChildren().add(labelBox);
  }

  @Override
  protected void updateItem(VisualEditTerminal item, boolean empty) {
    super.updateItem(item, empty);
    graphicProperty().unbind();
    textProperty().unbind();
    setText(null);
    if (empty) {
      setGraphic(null);
    } else {
      setGraphic(container);
      itemText.textProperty().bind(item.nameProperty());

      ObjectProperty<Image> imageProperty = model.getTerminalSnapshot(item);
      image.backgroundProperty().bind(Bindings.createObjectBinding(() -> {
        if (imageProperty.get() == null) {
          return noImageBackground;
        } else {
          return new Background(new BackgroundImage(imageProperty.get(),
              BackgroundRepeat.NO_REPEAT, BackgroundRepeat.NO_REPEAT, BackgroundPosition.CENTER,
              new BackgroundSize(1, 1, true, true, false, false)));
        }
      }, imageProperty));
    }
  }

  @Override
  protected Image getDragImage() {
    return container.snapshot(new SnapshotParameters(), null);
  }


}
