package com.mc.tool.framework.systemedit.datamodel;

import com.mc.tool.framework.operation.crossscreen.datamodel.MultiScreenObject;
import javafx.collections.ObservableList;
import javafx.util.Pair;

/**
 * .
 */
public abstract class MultiScreenFunc<DataTypeT extends MultiScreenObject> extends VisualEditFunc {

  public abstract DataTypeT getFuncData();

  public abstract void moveScreen(int fromRow, int fromColumn, int toRow, int toColumn);

  public abstract boolean isScreenMovable();

  public abstract Pair<Integer, Integer> posOfScreen(VisualEditTerminal terminal);

  public abstract boolean addScenario(DataTypeT object);

  public abstract boolean removeScenario(DataTypeT object);

  public abstract ObservableList<DataTypeT> getScenarios();

  @Override
  public String getNodeType() {
    return SystemEditDefinition.SEAT_CELL;
  }

  public abstract int getMaxRow();

  public abstract int getMaxColumn();
}
