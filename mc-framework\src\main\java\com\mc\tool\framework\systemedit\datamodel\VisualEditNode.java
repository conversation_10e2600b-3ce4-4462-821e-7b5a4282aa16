package com.mc.tool.framework.systemedit.datamodel;

import com.mc.graph.interfaces.CellBindedObject;
import com.mc.graph.interfaces.CellObject;
import java.util.Collection;
import java.util.List;
import java.util.function.Predicate;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.DoubleProperty;
import javafx.beans.property.StringProperty;
import javafx.collections.ObservableList;
import org.controlsfx.control.PropertySheet;

/**
 * .
 */
public interface VisualEditNode extends CellBindedObject {
  /**
   * 获取node唯一标识.
   *
   * @return guid
   */
  String getGuid();

  /**
   * 通过guid查找子节点.
   *
   * @param guid      子节点的guid.
   * @param recursive 如果为true，那么会递归查找.
   * @return 如果找到，返回相应的node，否则返回null
   */
  VisualEditNode findChildByGuid(String guid, boolean recursive);

  VisualEditNode findChild(Predicate<VisualEditNode> predicate, boolean recursive);

  <T> void recursiveAllChild(Predicate<T> predicate, List<T> allChild, Class<T> tclass);

  /**
   * 通过名称查找子节点.
   *
   * @param name      子节点的名称.
   * @param recursive 如果为true，那么会递归查找.
   * @return 如果找到，返回相应的node，否则返回null
   */
  VisualEditNode findChildByName(String name, boolean recursive);

  String getNodeType();

  default Object getExtraData() {
    return "0";
  }

  Collection<String> getSupportLinkNodeType();

  Collection<String> getSupportConnectNodeType();

  Collection<String> getSupportConnectionType();

  void setName(String name);

  String getName();

  StringProperty nameProperty();

  VisualEditNode getParent();

  void setParent(VisualEditNode node);

  Collection<VisualEditNode> getChildren();

  Collection<VisualEditNode> getRxChildren();

  Collection<VisualEditNode> getTxChildren();

  ObservableList<VisualEditNode> getObservableChildren();

  /**
   * Add a child.
   *
   * @param node child node
   */
  void addChildren(VisualEditNode... node);

  void addChildren(int index, VisualEditNode... nodes);

  int indexOfChild(VisualEditNode node);

  /**
   * 删除节点.
   *
   * @param recursive 是否为递归删除
   * @param node      要删除的节点
   */
  void removeChildren(boolean recursive, VisualEditNode... node);

  void removeChildren(boolean recursive, boolean permanent, VisualEditNode... node);

  void removeAndAdd(Collection<VisualEditNode> removeItems, Collection<VisualEditNode> addItems,
                    int addIndex);

  void moveChild(VisualEditNode node, int index);

  DoubleProperty locationXposProperty();

  DoubleProperty locationYposProperty();

  BooleanProperty collapseProperty();

  int getPortCount();

  boolean isEmpty();

  boolean hasChild(VisualEditNode node);

  boolean isChildIndexChangable();

  boolean isTx();

  boolean isRx();


  /**
   * 检查是否当前节点需要是否与matrix之间的位置固定.
   *
   * @param matrix 与当前节点有连接关系的matrix
   * @return 如果需要位置固定，返回true.
   */
  boolean isFixToMatrix(VisualEditMatrix matrix);

  /**
   * 递归获取所有类型为terminal的子节点.
   *
   * @return terminal节点的集合
   */
  ObservableList<VisualEditTerminal> getAllTerminalChild();

  Collection<VisualEditTerminal> getAllOnlineTerminalChild();

  ObservableList<VisualEditFunc> getAllFunctions();

  void setCellObject(CellObject cellObject);

  CellObject getCellObject();

  /**
   * 初始化，必须在使用前调用.
   */
  void init();

  /**
   * 递归初始化，反序列化时使用.
   */
  void recursiveInit();

  ObservableList<PropertySheet.Item> getProperties();
}
