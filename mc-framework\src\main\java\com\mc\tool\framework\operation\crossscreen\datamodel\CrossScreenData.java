package com.mc.tool.framework.operation.crossscreen.datamodel;

import com.google.gson.annotations.Expose;
import com.mc.tool.framework.operation.seat.datamodel.SeatData.SeatConnection;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import javafx.beans.property.IntegerProperty;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleIntegerProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.StringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableMap;
import lombok.Getter;

/**
 * .
 */
public class CrossScreenData implements CrossScreenObject {

  @Getter
  @Expose
  private StringProperty name = new SimpleStringProperty();

  @Expose
  @Getter
  private IntegerProperty rows = new SimpleIntegerProperty();

  @Expose
  @Getter
  private IntegerProperty columns = new SimpleIntegerProperty();

  @Expose
  @Getter
  private ObjectProperty<VisualEditTerminal> controlSource = new SimpleObjectProperty<>();

  @Expose
  private List<ObjectProperty<VisualEditTerminal>> targets = new ArrayList<>();

  @Getter
  @Expose
  private ObservableMap<VisualEditTerminal, SeatConnection> connections =
      FXCollections.observableHashMap();

  @Override
  public Collection<ObjectProperty<VisualEditTerminal>> getTargets() {
    return targets;
  }

  /**
   * 重置screen的容量.
   *
   * @param size 容量的大小
   */
  public void resetCapacity(int size) {
    if (size > targets.size()) {
      int addLength = size - targets.size();
      for (int i = 0; i < addLength; i++) {
        targets.add(new SimpleObjectProperty<>());
      }
    } else if (size < targets.size()) {
      targets = targets.subList(0, size);
    }
  }

  @Override
  public ObjectProperty<VisualEditTerminal> getTarget(int index) {
    if (index < 0 || index >= targets.size()) {
      return null;
    }
    return targets.get(index);
  }

  @Override
  public int indexOfTarget(VisualEditTerminal target) {
    if (target == null) {
      return -1;
    }
    for (int i = 0; i < targets.size(); i++) {
      if (targets.get(i).get() == target) {
        return i;
      }
    }
    return -1;
  }

  /**
   * 插入一个target.
   *
   * @param target target
   * @param index  插入的索引
   */
  public void insertTarget(VisualEditTerminal target, int index) {
    int stop = index == 0 ? targets.size() - 1 : index - 1;
    while (target != null) {
      VisualEditTerminal temp = targets.get(index).get();
      targets.get(index).set(target);
      target = temp;
      if (index == stop) {
        break;
      }
      index = (index + 1) % targets.size();
    }
  }
}
