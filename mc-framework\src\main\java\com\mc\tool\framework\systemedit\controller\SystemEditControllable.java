package com.mc.tool.framework.systemedit.controller;

import com.mc.graph.McGraph;
import com.mc.graph.interfaces.ConnectorIdentifier;
import com.mc.graph.interfaces.GraphObject;
import com.mc.tool.framework.interfaces.ViewControllable;
import com.mc.tool.framework.systemedit.datamodel.VisualEditGroup;
import com.mc.tool.framework.systemedit.datamodel.VisualEditMatrix;
import com.mc.tool.framework.systemedit.datamodel.VisualEditModel;
import com.mc.tool.framework.systemedit.datamodel.VisualEditNode;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import com.mc.tool.framework.utility.TypeWrapper;
import java.util.Collection;
import javafx.scene.paint.Color;

/**
 * .
 */
public interface SystemEditControllable extends ViewControllable {

  void initModel(VisualEditModel model);

  VisualEditModel getModel();

  McGraph getGraph();

  /**
   * 获取界面上选中的节点的集合.
   *
   * @return 选中的节点的集合.
   */
  Collection<VisualEditNode> getSelectedNodes();

  void updateModelToGraph();

  VisualEditMatrix findMatrix(VisualEditNode node);

  void selectAndCenterNode(VisualEditNode node);

  void setHighLighter(GraphObject... graphObjects);

  /**
   * 高亮与目标节点相关的link.
   *
   * @param nodes 目标节点
   */
  void setHighLightLinks(VisualEditNode... nodes);

  boolean isOnlineEdit();

  boolean isConnectable(VisualEditTerminal rx, Object rxConnector, VisualEditTerminal tx,
                        Object txConnector);

  /**
   * 获取连接的类型.
   *
   * @param rx          rx的终端
   * @param rxConnector rx的connector
   * @param tx          tx的终端
   * @param txConnector tx的connector
   * @return 类型列表
   */
  Collection<TypeWrapper> getConnectableType(VisualEditTerminal rx, Object rxConnector,
                                             VisualEditTerminal tx, Object txConnector);

  Collection<TypeWrapper> getMultiviewChannel(VisualEditTerminal rx, VisualEditTerminal tx);

  Collection<Color> getConnectionModeColor(String mode);

  /**
   * 判断两个节点的两个connector是否能连起来.
   *
   * @param first           第一个节点
   * @param firstConnector  第一个节点的connector
   * @param second          第二个节点
   * @param secondConnector 第二个节点的connector
   * @return 如果能连起来，返回true
   */
  boolean isLinkable(VisualEditNode first, Object firstConnector, VisualEditNode second,
                     Object secondConnector);

  /**
   * 判断目标节点是否能删除.
   *
   * @param node 目标节点
   * @return 如果能删除，返回true
   */
  boolean isDeletable(VisualEditNode node);

  /**
   * 判断节点的设备类型是否能修改.
   *
   * @param node 目标节点
   * @return 如果不能修改，true
   */
  boolean isFixDeviceType(VisualEditNode node);

  /**
   * 连接两个终端.
   *
   * @param tx          tx
   * @param txConnector tx端口
   * @param rx          rx
   * @param rxConnector rx端口
   * @param mode        连接模式
   */
  void connect(VisualEditTerminal tx, ConnectorIdentifier txConnector, VisualEditTerminal rx,
               ConnectorIdentifier rxConnector, String mode, int rxChannel);

  /**
   * 把目标的节点添加为一组.
   *
   * @param groupName  组的名称，如果为null，自动生成一个名称
   * @param groupClazz TODO
   * @param nodes      要组成一组的节点
   * @return TODO
   */
  <T extends VisualEditGroup> T addGroup(String groupName, Class<T> groupClazz,
                                         VisualEditNode... nodes);

  void deleteGroup(VisualEditGroup group, boolean needToUpdate);

  void deleteTerminal(VisualEditTerminal terminal, boolean needToUpdate);

  /**
   * 删除节点.
   *
   * @param nodes 要被删除的节点
   */
  void deleteNodes(VisualEditNode... nodes);

  /**
   * 是否能把一个节点移到另外一个节点作为他的子节点.
   *
   * @param from 子节点
   * @param to   父节点
   * @return 如果能移，返回true
   */
  boolean isMovable(VisualEditNode from, VisualEditNode to);

  /**
   * 把一个节点移移动到另外一个节点作为子节点.
   *
   * @param from 子节点
   * @param to   父节点
   */
  void moveTo(VisualEditNode from, VisualEditNode to);

  /**
   * 节点是否显示.
   *
   * @param node 节点
   * @return 如果显示，返回true
   */
  boolean isNodeVisible(VisualEditNode node);

  NodeSearcher createNodeSearcher();

  /**
   * entity是否状态改变.
   */
  void onEntityActiveChange(boolean active);
}
