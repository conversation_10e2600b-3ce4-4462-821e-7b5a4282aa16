package com.mc.tool.framework.utility;

import com.google.gson.TypeAdapter;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import java.io.IOException;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.scene.paint.Color;

/**
 * .
 */
public class ColorObjectPropertyTypeAdapter extends TypeAdapter<ObjectProperty<Color>> {

  @Override
  public ObjectProperty<Color> read(JsonReader in) throws IOException {
    String value = in.nextString();
    return new SimpleObjectProperty<>(Color.web(value));
  }

  @Override
  public void write(JsonWriter out, ObjectProperty<Color> property) throws IOException {
    Color value = property.get();
    if (value == null) {
      value = Color.TRANSPARENT;
    }
    String str = String.format("#%02x%02x%02x%02x", (int) (value.getRed() * 255),
        (int) (value.getGreen() * 255), (int) (value.getBlue() * 255),
        (int) (value.getOpacity() * 255));
    out.value(str);
  }

}
