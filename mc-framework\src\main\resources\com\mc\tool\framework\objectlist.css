@import "common.css";

.object-list-title {
    -fx-background-color: LEFT_PANEL_COLOR;
    -fx-pref-height: 40;
    -fx-min-height: 40;
    -fx-alignment: center-left;
    -fx-padding: 0 12 0 21;
}

.object-list-name {
    -fx-font-size: 14;
    -fx-font-family: MC_FONT_FAMILY;
    -fx-text-fill: #000000;
}


.object-list-title-blank2 {
    -fx-pref-width: 16;
}

.object-menu {
    -fx-background-image: url("./img/object_menu.png");
    -fx-min-height: 14;
    -fx-min-width: 16;
    -fx-max-height: 14;
    -fx-max-width: 16;
}

.object-menu:hover {
    -fx-background-image: url("./img/object_menu_hover.png");
}

.object-logo {
    -fx-pref-height: 17;
    -fx-pref-width: 18;
}

#listviewContainer {
    -fx-padding: 0 2 0 2;
}

/* Style for list view */
.list-view {
    -fx-background-color: LEFT_PANEL_COLOR;
}

.list-view:focus {
    -fx-border-width: 0;
}

.list-cell {
    -fx-cell-size: 44;
    -fx-background-color: LEFT_PANEL_COLOR, TRANSPARENT;
    -fx-background-insets: 0 0 1 0, 43 0 0 0;
    -fx-alignment: center;
    -fx-text-fill: white;
    -fx-font-size: 14;
}

.list-cell:selected {
    -fx-background-color: #cccccc;
}

.list-cell:hover {
    -fx-background-color: #d9d9d9;
}

.list-cell:empty {
    -fx-opacity: 0;
} 