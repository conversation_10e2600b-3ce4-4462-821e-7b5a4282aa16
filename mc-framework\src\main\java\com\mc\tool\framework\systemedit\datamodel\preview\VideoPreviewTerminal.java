package com.mc.tool.framework.systemedit.datamodel.preview;

import com.google.gson.annotations.Expose;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.StringProperty;
import lombok.Getter;

/**
 * .
 */
public class VideoPreviewTerminal {
  @Expose
  @Getter
  private ObjectProperty<VisualEditTerminal> terminal = new SimpleObjectProperty<>();
  @Expose
  @Getter
  private StringProperty address = new SimpleStringProperty();
}
