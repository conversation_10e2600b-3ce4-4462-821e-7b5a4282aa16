<?xml version="1.0" encoding="UTF-8"?>

<?import com.mc.tool.framework.controller.MainMasterDetailPane?>
<?import com.mc.tool.framework.view.ObjectListView?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.TabPane?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.Region?>
<?import javafx.scene.layout.StackPane?>
<?import javafx.scene.layout.VBox?>
<?import org.controlsfx.control.MaskerPane?>
<?import org.controlsfx.control.StatusBar?>
<VBox maxHeight="-Infinity" maxWidth="-Infinity" minHeight="-Infinity"
      minWidth="-Infinity" prefHeight="720" prefWidth="1315"
      xmlns="http://javafx.com/javafx/8" xmlns:fx="http://javafx.com/fxml/1"
      fx:id="container" stylesheets="@main.css">
  <Region prefHeight="56" minHeight="56" id="header"/>
  <Region prefHeight="1" minHeight="1" id="header-seperator"/>
  <StackPane VBox.vgrow="ALWAYS">
    <MainMasterDetailPane detailSide="LEFT" dividerPosition="0.18" fx:id="masterDetailPane">
      <detailNode>
        <VBox id="leftPanel" prefWidth="200">
          <VBox VBox.Vgrow="ALWAYS">
            <ObjectListView id="device-view" fx:id="deviceView"/>
            <ObjectListView id="monitor-view" fx:id="monitorView"/>
          </VBox>
          <VBox styleClass="application-logo" minHeight="101"/>
        </VBox>
      </detailNode>
      <masterNode>
        <AnchorPane HBox.hgrow="ALWAYS" id="rightPanel">
          <VBox AnchorPane.topAnchor="0" AnchorPane.bottomAnchor="0"
                AnchorPane.leftAnchor="0" AnchorPane.rightAnchor="0">
            <TabPane VBox.vgrow="ALWAYS" tabClosingPolicy="UNAVAILABLE"
                     fx:id="pagesTabPanel" id="pages-tab"/>
            <VBox visible="false" VBox.vgrow="ALWAYS" alignment="CENTER" fx:id="startPage">
              <ImageView id="start-image"/>
              <Region minHeight="40"/>
              <Label text="%framework.start.text"/>
              <Region minHeight="22"/>
              <HBox fx:id="startButtonContainer" alignment="CENTER" id="start-button-container"/>
            </VBox>
            <Region minHeight="1" styleClass="seperator"/>
            <StatusBar fx:id="statusBar" text="" minHeight="39" maxHeight="39"/>
          </VBox>
          <VBox AnchorPane.topAnchor="0" AnchorPane.bottomAnchor="0"
                AnchorPane.leftAnchor="0" minWidth="10" alignment="center">
            <Button styleClass="image-button" id="hide-show-menu-btn" fx:id="hideShowMenuBtn"/>
          </VBox>
        </AnchorPane>
      </masterNode>
    </MainMasterDetailPane>
    <MaskerPane visible="false" fx:id="maskerPane"/>
  </StackPane>
</VBox>
