package com.mc.tool.framework.office.datamodel;

import com.google.gson.annotations.Expose;
import com.mc.graph.interfaces.CellBindedObject;
import com.mc.graph.interfaces.ConnectorIdentifier;
import com.mc.graph.interfaces.ExtraActionInfo;
import com.mc.tool.framework.systemedit.datamodel.VisualEditFunc;
import java.util.Collection;
import java.util.Collections;
import javafx.beans.property.DoubleProperty;
import javafx.beans.property.SimpleDoubleProperty;
import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.StringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import lombok.Getter;
import lombok.Setter;

/**
 * .
 */
public class OfficeData implements CellBindedObject {

  /**
   * 视频墙或坐席名称.
   */
  @Expose
  @Getter
  @Setter
  private StringProperty name = new SimpleStringProperty();

  /**
   * 视频墙或坐席类型.
   */
  @Expose
  @Getter
  @Setter
  private String type = "";

  /**
   * 视频墙或坐席功能组.
   */
  @Expose
  @Getter
  @Setter
  private VisualEditFunc func = new VisualEditFunc();

  /**
   * 视频墙或坐席位置的横坐标.
   */
  @Expose
  @Getter
  private DoubleProperty xpos = new SimpleDoubleProperty();

  /**
   * 视频墙或坐席位置的纵坐标.
   */
  @Expose
  @Getter
  private DoubleProperty ypos = new SimpleDoubleProperty();

  /**
   * 视频墙或坐席位置的旋转角度.
   */
  @Expose
  protected DoubleProperty angle = new SimpleDoubleProperty();

  public DoubleProperty angleProperty() {
    return angle;
  }

  /**
   * 视频墙或坐席的宽度.
   */
  @Expose
  @Getter
  private DoubleProperty width = new SimpleDoubleProperty();

  /**
   * 视频墙或坐席的高度.
   */
  @Expose
  @Getter
  private DoubleProperty height = new SimpleDoubleProperty();

  @Expose
  @Getter
  private DoubleProperty alpha = new SimpleDoubleProperty();

  @Override
  public ObservableList<ConnectorIdentifier> getConnectorId() {
    return FXCollections.observableArrayList();
  }

  @Override
  public Collection<ExtraActionInfo> getExtraActions() {
    return Collections.emptyList();
  }

}
