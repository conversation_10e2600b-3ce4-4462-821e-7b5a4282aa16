package com.mc.tool.framework.utility;

import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.scene.Node;
import javafx.scene.layout.Pane;

/**
 * .
 */
public class JavaFxUtility {
  /**
   * 反转所有子控件.
   *
   * @param container 需要反转的控件
   * @param recursive 递归反转子控件
   */
  public static void reversePaneContent(Pane container, boolean recursive) {
    if (container == null) {
      return;
    }
    ObservableList<Node> workingCollection = FXCollections.observableArrayList(container.getChildren());
    FXCollections.reverse(workingCollection);
    container.getChildren().setAll(workingCollection);
    if (recursive) {
      for (Node node : container.getChildren()) {
        if (node instanceof Pane) {
          reversePaneContent((Pane) node, recursive);
        }
      }
    }
  }
}
