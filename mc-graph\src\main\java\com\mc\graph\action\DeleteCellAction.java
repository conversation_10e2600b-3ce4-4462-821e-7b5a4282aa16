package com.mc.graph.action;

import com.mc.graph.interfaces.GraphController;
import javafx.event.ActionEvent;
import javafx.event.EventHandler;

public class DeleteCellAction implements EventHandler<ActionEvent> {
  private GraphController controller;
  
  public DeleteCellAction(GraphController controller) {
    this.controller = controller;
  }  
  
  @Override
  public void handle(ActionEvent event) {
    if (controller.isCellDeletable()) {
      controller.removeSelectedCells();
    }
  }
  
}
