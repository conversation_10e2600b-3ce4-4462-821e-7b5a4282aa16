package com.mc.tool.framework.operation.videowall.menu;

import com.mc.tool.framework.operation.videowall.controller.VideoWallControllable;
import com.mc.tool.framework.operation.videowall.datamodel.LayoutData;
import com.mc.tool.framework.operation.videowall.datamodel.VideoDataBean;
import com.mc.tool.framework.operation.videowall.datamodel.interfaces.VideoObject;
import com.mc.tool.framework.utility.FormDialog;
import com.mc.tool.framework.utility.I18nUtility;
import com.mc.tool.framework.utility.SimpleFxFormValidator;
import java.lang.annotation.ElementType;
import javafx.stage.Window;
import javax.validation.Configuration;
import javax.validation.Validation;
import javax.validation.ValidationException;
import javax.validation.ValidatorFactory;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.validator.HibernateValidatorConfiguration;
import org.hibernate.validator.cfg.ConstraintMapping;
import org.hibernate.validator.cfg.defs.MaxDef;
import org.hibernate.validator.cfg.defs.MinDef;

/**
 * .
 */
@Slf4j
public class MenuVideoSetting extends VideoWallMenuBase {

  public MenuVideoSetting(VideoWallControllable controllable) {
    super(controllable);
    setDisable(controllable.getSelectedVideos().size() != 1);
  }

  @Override
  protected void onAction() {
    VideoObject data = controllable.getSelectedVideos().iterator().next();
    onSetting(getParentPopup().getOwnerWindow(), data,
        controllable.getVideoWallFunction().getVideoWallObject().getLayoutData());
  }

  @Override
  protected String getMenuText() {
    return I18nUtility.getI18nBundle("operation").getString("menu.setting");
  }

  /**
   * 弹出视频窗口的设置框.
   *
   * @param owner     owner
   * @param videoData 视频窗口数据
   */
  public static void onSetting(Window owner, VideoObject videoData, LayoutData layoutData) {
    VideoDataBean bean = new VideoDataBean(videoData);
    FormDialog<VideoDataBean> formDialog = new FormDialog<>(bean,
        I18nUtility.getI18nBundle("videobean"), owner);
    formDialog.getFxform().setFxFormValidator(new VideoValidator(layoutData));
    formDialog.getDialogPane().setMinSize(400, 400);
    formDialog.showAndWaitWithCommit();
  }

  static class VideoValidator extends SimpleFxFormValidator {
    private final LayoutData layoutData;

    public VideoValidator(LayoutData layoutData) {
      this.layoutData = layoutData;
    }

    @Override
    protected void init() {
      if (init) {
        return;
      }
      try {
        Configuration<?> configuration = Validation.byDefaultProvider().configure();
        if (configuration instanceof HibernateValidatorConfiguration) {
          HibernateValidatorConfiguration hiConfig = (HibernateValidatorConfiguration) configuration;
          ConstraintMapping mapping = hiConfig.createConstraintMapping();
          mapping.type(VideoDataBean.class).property("xpos", ElementType.METHOD)
              .constraint(new MinDef().value(-layoutData.getTotalWidth()))
              .constraint(new MaxDef().value(layoutData.getTotalWidth()));
          mapping.type(VideoDataBean.class).property("ypos", ElementType.METHOD)
              .constraint(new MinDef().value(-layoutData.getTotalHeight()))
              .constraint(new MaxDef().value(layoutData.getTotalHeight()));
          mapping.type(VideoDataBean.class).property("width", ElementType.METHOD)
              .constraint(new MaxDef().value(2 * layoutData.getTotalWidth()));
          mapping.type(VideoDataBean.class).property("height", ElementType.METHOD)
              .constraint(new MaxDef().value(2 * layoutData.getTotalHeight()));
          hiConfig.addMapping(mapping);
        }
        ValidatorFactory newFactory = configuration.buildValidatorFactory();
        validator = newFactory.getValidator();
        messageInterpolator = newFactory.getMessageInterpolator();
      } catch (ValidationException exception) {
        // validation is not activated, since no implementation has been provided
        log.warn("Validation disabled", exception);
      }
      init = true;
    }
  }
}
