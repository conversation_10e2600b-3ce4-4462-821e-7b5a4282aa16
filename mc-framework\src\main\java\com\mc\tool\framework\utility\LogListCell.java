package com.mc.tool.framework.utility;

import com.mc.tool.framework.utility.ViewLogger.LogInfo;
import javafx.scene.control.ListCell;

/**
 * .
 */
public class LogListCell extends ListCell<LogInfo> {

  @Override
  protected void updateItem(LogInfo item, boolean empty) {
    super.updateItem(item, empty);
    if (!empty) {
      setText(item.getMessage());
      String textFill = "black";
      switch (item.getType()) {
        case INFO:
          textFill = "black";
          break;
        case WARN:
          textFill = "#9F6000";
          break;
        case ERR:
          textFill = "#D8000C";
          break;
        case SUCCESS:
          textFill = "#4F8A10";
          break;
        default:
          break;
      }
      setStyle("-fx-text-fill: " + textFill);
    } else {
      setText(null);
    }
  }
}
