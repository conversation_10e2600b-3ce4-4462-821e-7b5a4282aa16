/*
 * Copyright 2012-2016 <PERSON> <info@micha<PERSON><PERSON>er.de>. All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without modification, are permitted
 * provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this list of conditions
 * and the following disclaimer.
 *
 * 2. Redistributions in binary form must reproduce the above copyright notice, this list of
 * conditions and the following disclaimer in the documentation and/or other materials provided with
 * the distribution.
 *
 * Please cite the following publication(s):
 *
 * <PERSON><PERSON>, C.<PERSON>, G.<PERSON>. Visual Reflection Library - A Framework for Declarative GUI
 * Programming on the Java Platform. Computing and Visualization in Science, 2011, in press.
 *
 * THIS SOFTWARE IS PROVIDED BY <PERSON> <<EMAIL>> "AS IS" AND ANY EXPRESS OR
 * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND
 * FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL <PERSON>
 * <info@micha<PERSON><PERSON>er.de> OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
 * EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE
 * GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON
 * ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY
 * OF SUCH DAMAGE.
 *
 * The views and conclusions contained in the software and documentation are those of the authors
 * and should not be interpreted as representing official policies, either expressed or implied, of
 * Michael Hoffer <<EMAIL>>.
 */

package com.mc.graph.util;

import com.mc.graph.util.GraphSelectionModel.SelectionMode;
import javafx.beans.property.DoubleProperty;
import javafx.beans.property.SimpleDoubleProperty;
import javafx.event.EventHandler;
import javafx.scene.Node;
import javafx.scene.Parent;
import javafx.scene.input.MouseEvent;
import javafx.scene.shape.Rectangle;
import lombok.Getter;

/**
 * This is a utility class that provides methods for mouse gesture control. Currently, it can be
 * used to make nodes draggable.
 *
 * <AUTHOR> Hoffer &lt;<EMAIL>&gt;
 */
public class MouseControlUtil {

  // no instanciation allowed
  private MouseControlUtil() {
    throw new AssertionError(); // not in this class either!
  }

  /**
   * Makes a node draggable via mouse gesture.
   *
   * <p>
   * <b>Note:</b> Existing handlers will be replaced!
   * </p>
   *
   * @param node the node that shall be made draggable
   */
  public static void makeDraggable(final Node node) {

    makeDraggable(node, null, null, false, null);
  }

  public static void makeDraggable(final Node node, DraggableParams params) {
    makeDraggable(node, null, null, false, params);
  }

  /**
   * Makes a node draggable via mouse gesture.
   *
   * <p>
   * <b>Note:</b> Existing handlers will be replaced!
   * </p>
   *
   * @param node the node that shall be made draggable
   */
  public static void makeDraggable(final Node node, boolean centerNode) {

    makeDraggable(node, null, null);
  }

  /**
   * Makes a node draggable via mouse gesture.
   *
   * <p>
   * <b>Note:</b> Existing handlers will be replaced!
   * </p>
   *
   * @param node the node that shall be made draggable
   * @param dragHandler additional drag handler
   * @param pressHandler additional press handler
   */
  public static void makeDraggable(final Node node, EventHandler<MouseEvent> dragHandler,
      EventHandler<MouseEvent> pressHandler) {
    makeDraggable(node, dragHandler, pressHandler, false, null);
  }


  /**
   * Makes a node draggable via mouse gesture.
   * <p>
   * <b>Note:</b> Existing handlers will be replaced!
   * </p>
   * 
   * @param node the node that shall be made draggable
   * @param dragHandler additional drag handler
   * @param pressHandler additional press handler
   */
  public static void makeDraggable(final Node node, EventHandler<MouseEvent> dragHandler,
      EventHandler<MouseEvent> pressHandler, boolean centerNode, DraggableParams params) {

    EventHandlerGroup<MouseEvent> dragHandlerGroup = new EventHandlerGroup<>();
    EventHandlerGroup<MouseEvent> pressHandlerGroup = new EventHandlerGroup<>();

    if (dragHandler != null) {
      dragHandlerGroup.addHandler(dragHandler);
    }

    if (pressHandler != null) {
      pressHandlerGroup.addHandler(pressHandler);
    }

    node.setOnMouseDragged(dragHandlerGroup);
    node.setOnMousePressed(pressHandlerGroup);

    node.layoutXProperty().unbind();
    node.layoutYProperty().unbind();

    makeDraggable_(node, dragHandlerGroup, pressHandlerGroup, centerNode, params);
  }

  /**
   * Adds a selection rectangle gesture to the specified parent node. A rectangle node must be
   * specified that is used to indicate the selection area.
   * <p>
   * <b>Note:</b>
   * </p>
   * To support selection a node must implement the
   * {@link jfxtras.labs.scene.control.window.SelectableNode} interface.
   *
   * @param root parent node
   * @param rect selectionn rectangle
   *
   */
  public static void addSelectionRectangleGesture(final Parent root, final Rectangle rect,
      GraphSelectionModel clipboard) {
    addSelectionRectangleGesture(root, rect, null, null, null, clipboard);
  }

  /**
   * Adds a selection rectangle gesture to the specified parent node. A rectangle node must be
   * specified that is used to indicate the selection area.
   * <p>
   * <b>Note:</b>
   * </p>
   * To support selection a node must implement the
   * {@link jfxtras.labs.scene.control.window.SelectableNode} interface.
   *
   * @param root parent node
   * @param rect selectionn rectangle
   * @param dragHandler additional drag handler (optional, may be <code>null</code>)
   * @param pressHandler additional press handler (optional, may be <code>null</code>)
   * @param releaseHandler additional release handler (optional, may be <code>null</code>)
   *
   */
  public static void addSelectionRectangleGesture(final Parent root, final Rectangle rect,
      EventHandler<MouseEvent> dragHandler, EventHandler<MouseEvent> pressHandler,
      EventHandler<MouseEvent> releaseHandler, GraphSelectionModel clipboard) {

    EventHandlerGroup<MouseEvent> dragHandlerGroup = new EventHandlerGroup<>();
    EventHandlerGroup<MouseEvent> pressHandlerGroup = new EventHandlerGroup<>();
    EventHandlerGroup<MouseEvent> releaseHandlerGroup = new EventHandlerGroup<>();

    if (dragHandler != null) {
      dragHandlerGroup.addHandler(dragHandler);
    }

    if (pressHandler != null) {
      pressHandlerGroup.addHandler(pressHandler);
    }

    if (releaseHandler != null) {
      releaseHandlerGroup.addHandler(releaseHandler);
    }

    root.addEventHandler(MouseEvent.MOUSE_DRAGGED, dragHandlerGroup);
    root.addEventHandler(MouseEvent.MOUSE_PRESSED, pressHandlerGroup);
    root.addEventHandler(MouseEvent.MOUSE_RELEASED, releaseHandlerGroup);
    
    root.addEventFilter(MouseEvent.MOUSE_DRAGGED, (event) -> {
      if (clipboard.getSelectionMode().get() != SelectionMode.AREA) {
        return;
      }
      dragHandlerGroup.handle(event);
    });
    root.addEventFilter(MouseEvent.MOUSE_PRESSED, (event) -> {
      if (clipboard.getSelectionMode().get() != SelectionMode.AREA) {
        return;
      }
      pressHandlerGroup.handle(event);
    });
    root.addEventFilter(MouseEvent.MOUSE_RELEASED, (event) -> {
      if (clipboard.getSelectionMode().get() != SelectionMode.AREA) {
        return;
      }
      releaseHandlerGroup.handle(event);
    });

    RectangleSelectionControllerImpl selectionHandler =
        new RectangleSelectionControllerImpl(clipboard);

    selectionHandler.apply(root, rect, dragHandlerGroup, pressHandlerGroup, releaseHandlerGroup);
  }

  private static void makeDraggable_(final Node node, EventHandlerGroup<MouseEvent> dragHandler,
      EventHandlerGroup<MouseEvent> pressHandler, boolean centerNode, DraggableParams params) {

    DraggingControllerImpl draggingController = new DraggingControllerImpl();
    draggingController.apply(node, dragHandler, pressHandler, centerNode, params);
  }

  public static class DraggableParams {
    @Getter
    private DoubleProperty minXposProperty = new SimpleDoubleProperty();
    @Getter
    private DoubleProperty minYposProperty = new SimpleDoubleProperty();
    @Getter
    private DoubleProperty maxXposProperty = new SimpleDoubleProperty();
    @Getter
    private DoubleProperty maxYposProperty = new SimpleDoubleProperty();
  }
}
