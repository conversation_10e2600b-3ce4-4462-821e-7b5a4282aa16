package com.mc.common.form;

import com.sun.javafx.binding.ExpressionHelper;
import javafx.beans.InvalidationListener;
import javafx.beans.Observable;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.Property;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.beans.value.ChangeListener;
import javafx.beans.value.ObservableValue;

import java.lang.ref.WeakReference;

public class ObjectFormProperty<T> extends ObjectProperty<T> implements FormProperty<T> {

  protected ObjectProperty<T> value = null;
  protected Property<T> observable = null;
  protected InvalidationListener listener = null;
  protected boolean valid = true;
  protected ExpressionHelper<T> helper = null;

  protected BooleanProperty changed = new SimpleBooleanProperty(false);

  /**
   * 创建一个属性，必须绑定一个值.
   * 
   * @param observable 绑定的值
   */
  public ObjectFormProperty(Property<T> observable) {
    bindImpl(observable);
  }

  @Override
  public Object getBean() {
    return null;
  }

  @Override
  public String getName() {
    return "";
  }

  @Override
  public void addListener(InvalidationListener listener) {
    helper = ExpressionHelper.addListener(helper, this, listener);
  }

  @Override
  public void addListener(ChangeListener<? super T> listener) {
    helper = ExpressionHelper.addListener(helper, this, listener);
  }

  @Override
  public void removeListener(InvalidationListener listener) {
    helper = ExpressionHelper.removeListener(helper, listener);
  }

  @Override
  public void removeListener(ChangeListener<? super T> listener) {
    helper = ExpressionHelper.removeListener(helper, listener);
  }

  /**
   * Sends notifications to all attached {@link javafx.beans.InvalidationListener
   * InvalidationListeners} and {@link javafx.beans.value.ChangeListener ChangeListeners}. This
   * method is called when the value is changed, either manually by calling {@link #set} or in case
   * of a bound property, if the binding becomes invalid.
   */
  protected void fireValueChangedEvent() {
    ExpressionHelper.fireValueChangedEvent(helper);
  }

  private void markInvalid() {
    if (valid) {
      valid = false;
      invalidated();
      fireValueChangedEvent();
    }
  }

  /**
   * The method {@code invalidated()} can be overridden to receive invalidation notifications. This
   * is the preferred option in {@code Objects} defining the property, because it requires less
   * memory. The default implementation is empty.
   */
  protected void invalidated() {}

  @Override
  public T get() {
    valid = true;
    if (value != null) {
      return value.get();
    } else if (observable != null) {
      return observable.getValue();
    } else {
      return null;
    }
  }

  @Override
  public void set(T newValue) {
    if (value != null) {
      if (observable.getValue() == newValue) {
        value = null;
        changed.set(false);
        markInvalid();
      } else if (value.get() != newValue) {
        value.set(newValue);
        markInvalid();
      }
    } else if (observable.getValue() != newValue) {
      value = new SimpleObjectProperty<>(newValue);
      changed.set(true);
      markInvalid();
    }
  }

  @Override
  public boolean isBound() {
    return observable != null;
  }

  @Override
  public void bind(final ObservableValue<? extends T> newObservable) {
    /**
     * 不能重新bind，只能在构造时bind.
     */
    throw new UnsupportedOperationException();
  }

  protected void bindImpl(final Property<T> newObservable) {
    if (newObservable == null) {
      throw new NullPointerException("Cannot bind to null");
    }

    if (!newObservable.equals(this.observable)) {
      unbind();
      observable = newObservable;
      if (listener == null) {
        listener = new Listener(this);
      }
      observable.addListener(listener);
      markInvalid();
    }
  }

  @Override
  public void unbind() {
    /**
     * 不能解除绑定
     */
  }

  /**
   * Returns a string representation of this {@code ObjectPropertyBase} object.
   * 
   * @return a string representation of this {@code ObjectPropertyBase} object.
   */
  @Override
  public String toString() {
    final Object bean = getBean();
    final String name = getName();
    final StringBuilder result = new StringBuilder("ObjectProperty [");
    if (bean != null) {
      result.append("bean: ").append(bean).append(", ");
    }
    if (!name.equals("")) {
      result.append("name: ").append(name).append(", ");
    }
    if (isBound()) {
      result.append("bound, ");
      if (valid) {
        result.append("value: ").append(get());
      } else {
        result.append("invalid");
      }
    } else {
      result.append("value: ").append(get());
    }
    result.append("]");
    return result.toString();
  }

  private static class Listener implements InvalidationListener {

    private final WeakReference<ObjectFormProperty<?>> wref;

    public Listener(ObjectFormProperty<?> ref) {
      this.wref = new WeakReference<ObjectFormProperty<?>>(ref);
    }

    @Override
    public void invalidated(Observable observable) {
      ObjectFormProperty<?> ref = wref.get();
      if (ref == null) {
        observable.removeListener(this);
      } else {
        ref.markInvalid();
      }
    }
  }

  @Override
  public boolean isChanged() {
    return changed.get();
  }

  @Override
  public void setToBinding() {
    if (value != null) {
      observable.setValue(value.get());
      value = null;
      changed.set(false);
      markInvalid();
    }
  }

  @Override
  public void reset() {
    if (value != null) {
      value = null;
      changed.set(false);
      markInvalid();
    }
  }

  @Override
  public BooleanProperty changedProperty() {
    return changed;
  }
}
