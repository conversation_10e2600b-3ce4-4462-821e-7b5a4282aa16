package com.mc.tool.framework;

import com.mc.tool.framework.interfaces.Page;
import com.mc.tool.framework.systemedit.datamodel.VisualEditModel;
import com.mc.tool.framework.systemedit.view.SystemEditPageView;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.scene.layout.Pane;

/**
 * .
 */
public class SystemEditPage implements Page {

  private final SystemEditPageView view;

  private SimpleBooleanProperty visibilityProperty = new SimpleBooleanProperty(true);

  public SystemEditPage(VisualEditModel model) {
    view = new SystemEditPageView(model);
  }

  @Override
  public String getTitle() {
    return "System Edit";
  }

  @Override
  public String getName() {
    return "system.edit";
  }

  @Override
  public Pane getView() {
    return view;
  }

  @Override
  public BooleanProperty getVisibleProperty() {
    return visibilityProperty;
  }

  @Override
  public void showObject(Object object) {

  }

  @Override
  public String getStyleClass() {
    return null;
  }

}
