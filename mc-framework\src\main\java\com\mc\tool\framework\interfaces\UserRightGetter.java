package com.mc.tool.framework.interfaces;

import com.mc.tool.framework.systemedit.datamodel.VideoWallFunc;

/**
 * .
 */
public interface UserRightGetter {
  boolean isSystemEditRenamable();

  boolean isSystemEditGroupCreateDeletable();

  boolean isVideoWallCreateDeletable();

  boolean isSeatCreateDeletable();

  boolean isDeviceTypeChangable();

  boolean isDeviceItemCreateDeletable();

  boolean isDeviceConnectable();

  boolean isDeviceItemMovable();

  boolean isSystemScenarioEditable();

  boolean isVideoWallLayoutEditable(VideoWallFunc func);

  boolean isVideoWallWindowCreateDeletable(VideoWallFunc func);

  boolean isVideoWallWindowMovablale(VideoWallFunc func);

  boolean isVideoWallWindowResizable(VideoWallFunc func);

  boolean isVideoWallScenarioCreateDeletable(
      VideoWallFunc func);

  boolean isVideoWallScenarioActivatable(
      VideoWallFunc func);

  boolean isVideoWallConnectable(VideoWallFunc func);

  boolean isSeatConnectable();

  boolean isSeatScenarioCreateDeletable();

  boolean isSeatSceanrioActivatable();
}
