package com.mc.tool.framework.operation.view;

import com.mc.tool.framework.operation.controller.OperationPageControllable;
import com.mc.tool.framework.systemedit.datamodel.VisualEditModel;
import com.mc.tool.framework.utility.I18nUtility;
import com.mc.tool.framework.utility.InjectorProvider;
import java.io.IOException;
import java.net.URL;
import javafx.fxml.FXMLLoader;
import javafx.scene.layout.VBox;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public final class OperationPageView extends VBox {
  @Getter
  private final OperationPageControllable controllable;

  /**
   * Constructor.
   */
  public OperationPageView(VisualEditModel model) {
    URL location = getClass().getResource("/com/mc/tool/framework/operation/operation_view.fxml");
    FXMLLoader loader = new FXMLLoader(location);
    controllable =
        InjectorProvider.getInjector().getInstance(OperationPageControllable.class);
    controllable.initModel(model);
    loader.setController(controllable);
    loader.setResources(I18nUtility.getI18nBundle("operation"));
    loader.setRoot(this);
    try {
      loader.load();
    } catch (IOException exception) {
      log.warn("Fail to load operation_view.fxml", exception);
    }
  }
}
