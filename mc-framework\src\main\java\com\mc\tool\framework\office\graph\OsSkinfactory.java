package com.mc.tool.framework.office.graph;

import com.mc.graph.DefaultSkinFactory;
import com.mc.graph.interfaces.CellObject;
import com.mc.graph.interfaces.CellSkin;
import com.mc.graph.interfaces.SkinManager;
import com.mc.tool.framework.office.view.SeatCellSkin;
import com.mc.tool.framework.office.view.VideoWallCellSkin;
import com.mc.tool.framework.systemedit.datamodel.SystemEditDefinition;
import javafx.scene.Parent;

/**
 * .
 */
public class OsSkinfactory extends DefaultSkinFactory {

  private CellSkin skin = null;

  @Override
  public CellSkin createCellSkin(CellObject cellObject, Parent parent, Parent container, SkinManager skinManager) {
    if (cellObject.getType().equals(SystemEditDefinition.VIDEO_WALL_CELL)) {
      skin = new VideoWallCellSkin(cellObject, parent, container, skinManager);
    } else if (cellObject.getType().equals(SystemEditDefinition.SEAT_CELL)) {
      skin = new SeatCell<PERSON>kin(cellObject, parent, container, skinManager);
    }
    if (skin != null) {
      skin.getRegion().layoutXProperty().bindBidirectional(cellObject.getXProperty());
      skin.getRegion().layoutYProperty().bindBidirectional(cellObject.getYProperty());
      skin.getRegion().prefWidthProperty().bindBidirectional(cellObject.getWidthProperty());
      skin.getRegion().prefHeightProperty().bindBidirectional(cellObject.getHeightProperty());
      skinManager.setCellSkin(cellObject, skin);
    }
    return skin;
  }

  public CellSkin getCellSkin() {
    return skin;
  }
}
