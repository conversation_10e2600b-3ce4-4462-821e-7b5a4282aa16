package com.mc.tool.framework.operation.videowall.scenario;

import com.mc.tool.framework.operation.videowall.controller.VideoWallConstants;
import com.mc.tool.framework.operation.videowall.controller.VideoWallControllable;
import com.mc.tool.framework.operation.videowall.datamodel.interfaces.VideoObject;
import com.mc.tool.framework.operation.videowall.datamodel.interfaces.VideoWallObject;
import com.mc.tool.framework.utility.I18nUtility;
import java.io.IOException;
import java.net.URL;
import java.util.ResourceBundle;
import javafx.beans.binding.Bindings;
import javafx.beans.property.ReadOnlyStringWrapper;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.scene.canvas.Canvas;
import javafx.scene.canvas.GraphicsContext;
import javafx.scene.control.ContextMenu;
import javafx.scene.control.Label;
import javafx.scene.control.ListCell;
import javafx.scene.control.MenuItem;
import javafx.scene.input.MouseButton;
import javafx.scene.input.MouseEvent;
import javafx.scene.layout.HBox;
import javafx.scene.layout.VBox;
import javafx.scene.paint.Color;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public final class ScenarioCell extends ListCell<VideoWallObject> implements Initializable {
  @FXML
  private Label scenarioNameLabel;
  @FXML
  private VBox root;
  @FXML
  private Canvas canvas;
  @FXML
  private HBox nameBox;

  private VideoWallControllable controllable;

  private ContextMenu menu = new ContextMenu();

  /**
   * Constructor.
   *
   * @param controllable controllable
   */
  public ScenarioCell(VideoWallControllable controllable) {
    this.controllable = controllable;

    FXMLLoader loader = new FXMLLoader(getClass()
        .getResource("/com/mc/tool/framework/operation/videowall/scenario/scenario_cell.fxml"));
    loader.setController(this);
    try {
      loader.load();
    } catch (IOException exception) {
      log.warn("Fail to load scenario_cell.fxml!", exception);
    }
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {
    root.addEventHandler(MouseEvent.MOUSE_CLICKED, (event) -> {
      if (event.getButton() == MouseButton.PRIMARY && event.getClickCount() == 2) {
        boolean activatable = controllable.getDeviceController() == null
            || controllable.getDeviceController().getUserRight()
            .isVideoWallScenarioActivatable(controllable.getVideoWallFunction());
        if (activatable) {
          controllable.activeScenario(getItem());
        }
      } else if (event.getButton() == MouseButton.SECONDARY && event.getClickCount() == 1) {
        if (menu.isShowing()) {
          menu.hide();
        } else {
          menu.getItems().clear();

          boolean deletable = controllable.getDeviceController() == null || controllable
              .getDeviceController().getUserRight()
              .isVideoWallScenarioCreateDeletable(controllable.getVideoWallFunction());
          if (deletable) {
            MenuItem item = new MenuItem();
            item.setText(I18nUtility.getI18nBundle("operation").getString("menu.delete"));
            item.setDisable(!controllable.hasScenario(getItem()));
            item.setOnAction((menuEvent) -> controllable.deleteScenario(getItem()));
            menu.getItems().add(item);
          }
          menu.show(root.getScene().getWindow(), event.getScreenX(), event.getScreenY());
        }
      }
    });
  }

  @Override
  protected void updateItem(VideoWallObject item, boolean empty) {
    super.updateItem(item, empty);
    if (empty) {
      setGraphic(null);
    } else {
      setGraphic(root);
      if (item.hasIndex()) {
        scenarioNameLabel.textProperty()
            .bind(new ReadOnlyStringWrapper(String.format("[%02d]", item.getIndex()))
                .concat(item.getName()));
      } else {
        scenarioNameLabel.textProperty().bind(item.getName());
      }
      paintScenario(canvas, getItem());
      canvas.boundsInParentProperty().addListener((change) -> {
        paintScenario(canvas, getItem());
      });
      root.styleProperty()
          .bind(Bindings.when(this.selectedProperty())
              .then("-fx-border-size:2;-fx-border-color:#f08519;")
              .otherwise("-fx-border-size:1;-fx-border-color:#e6e6e6;"));
      nameBox.styleProperty()
          .bind(Bindings.when(Bindings.equal(controllable.currentScenarioProperty(), getItem()))
              .then("-fx-background-color:#f08619;").otherwise("-fx-background-color:#44464a;"));
    }
  }

  /**
   * 绘制预案.
   *
   * @param canvas 绘制的canvas
   * @param data   预案用的视频墙数据.
   */
  public static void paintScenario(Canvas canvas, VideoWallObject data) {
    double width = canvas.getWidth();
    double height = canvas.getHeight();

    int totalWidth = data.getLayoutData().getTotalWidth();
    int totalHeight = data.getLayoutData().getTotalHeight();

    double scaleFactor = Math.min((width - 6) / totalWidth, (height - 6) / totalHeight);
    double scaleWidth = totalWidth * scaleFactor;
    double scaleHeight = totalHeight * scaleFactor;
    double xoffset = (width - scaleWidth) / 2;
    double yoffset = (height - scaleHeight) / 2;
    //
    GraphicsContext gc = canvas.getGraphicsContext2D();
    gc.clearRect(0, 0, width, height);
    gc.setFill(Color.valueOf(VideoWallConstants.VIDEO_WALL_BG_COLOR));
    gc.fillRect(xoffset, yoffset, scaleWidth, scaleHeight);

    for (VideoObject videoData : data.getVideos()) {
      double xpos = xoffset + videoData.getXpos().get() * scaleFactor;
      double ypos = yoffset + videoData.getYpos().get() * scaleFactor;
      double videoWidth = videoData.getWidth().get() * scaleFactor;
      double videoHeight = videoData.getHeight().get() * scaleFactor;
      if (videoData.getSource().get() != null) {
        gc.setFill(Color.valueOf(VideoWallConstants.NORMAL_VIDEO_COLOR));
      } else {
        gc.setFill(Color.valueOf(VideoWallConstants.EMPTY_VIDEO_COLOR));
      }
      gc.fillRect(xpos, ypos, videoWidth, videoHeight);
      gc.setStroke(Color.valueOf(VideoWallConstants.VIDEO_BORER_COLOR));
      gc.strokeRect(xpos, ypos, videoWidth, videoHeight);
    }
  }
}
