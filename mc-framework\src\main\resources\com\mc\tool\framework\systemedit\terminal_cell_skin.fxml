<?import javafx.scene.control.Label?>
<?import javafx.scene.layout.GridPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.VBox?>
<HBox xmlns="http://javafx.com/javafx/8" xmlns:fx="http://javafx.com/fxml/1"
      minWidth="169" maxWidth="169" minHeight="35" maxHeight="35"
      stylesheets="@terminal_cell_skin.css">
  <HBox HBox.Hgrow="ALWAYS" fx:id="cellContainer" styleClass="cell-container">
    <Label fx:id="targetTypeLogo" minWidth="35" maxWidth="35" minHeight="35" maxHeight="35" styleClass="target-type-logo"/>
    <Label styleClass="cell-seperator" minHeight="35" maxHeight="35"/>
    <HBox HBox.Hgrow="ALWAYS" alignment="center">
      <Label id="name-text" fx:id="nameText" HBox.Hgrow="ALWAYS" minHeight="35" maxHeight="35" textAlignment="center"/>
    </HBox>
    <Label styleClass="cell-seperator" minHeight="35" maxHeight="35"/>
    <Label fx:id="interfaceTypeLogo" minWidth="35" maxWidth="35" minHeight="35" maxHeight="35" styleClass="interface-type-logo"/>
    <GridPane prefWidth="8" styleClass="connected-type" fx:id="connectedType"/>
  </HBox>
  <VBox prefWidth="4" minWidth="4" maxWidth="4" fx:id="connectorContainer" alignment="center">
  </VBox>
</HBox>