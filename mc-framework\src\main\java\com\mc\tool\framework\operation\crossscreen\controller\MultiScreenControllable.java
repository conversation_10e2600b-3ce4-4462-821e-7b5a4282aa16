package com.mc.tool.framework.operation.crossscreen.controller;

import com.mc.tool.framework.operation.crossscreen.datamodel.MultiScreenObject;
import com.mc.tool.framework.operation.interfaces.OperationControllable;
import com.mc.tool.framework.systemedit.datamodel.MultiScreenFunc;
import com.mc.tool.framework.systemedit.datamodel.VisualEditConnection;
import com.mc.tool.framework.systemedit.datamodel.VisualEditModel;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import com.mc.tool.framework.utility.TypeWrapper;
import java.util.Collection;
import javafx.beans.property.ObjectProperty;

/**
 * .
 */
public interface MultiScreenControllable<DataTypeT extends MultiScreenObject,
    FuncTypeT extends MultiScreenFunc<DataTypeT>> extends OperationControllable {
  void init(VisualEditModel model, FuncTypeT currentFunction);

  boolean isConnetable();

  void connectScreen(VisualEditTerminal tx, VisualEditTerminal rx,
                     String mode);

  void connectScreen(Collection<VisualEditConnection> connections);

  void updateConnection(VisualEditTerminal rx, VisualEditConnection connection);

  void beginUpdate();

  void endUpdate();

  /**
   * 激活预案.
   *
   * @param scenario 预案数据.
   */
  void activeScenario(DataTypeT scenario);

  ObjectProperty<DataTypeT> currentScenarioProperty();

  /**
   * 是否有此预案.
   *
   * @param scenario 预案
   * @return 如果有，返回true
   */
  boolean hasScenario(DataTypeT scenario);

  /**
   * 删除预案.
   *
   * @param scenario 预案
   */
  void deleteScenario(DataTypeT scenario);

  Collection<TypeWrapper> getMultiviewChannel(VisualEditTerminal rx);

  void connectMultiview(VisualEditTerminal tx, VisualEditTerminal rx, int mode, int channel);
}
