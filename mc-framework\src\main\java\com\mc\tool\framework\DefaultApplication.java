package com.mc.tool.framework;

import com.google.gson.Gson;
import com.google.gson.JsonIOException;
import com.google.gson.JsonSyntaxException;
import com.google.inject.Singleton;
import com.mc.tool.framework.beans.ConfigBean;
import com.mc.tool.framework.interfaces.ApplicationBase;
import com.mc.tool.framework.interfaces.Entity;
import com.mc.tool.framework.interfaces.EntityFactory;
import com.mc.tool.framework.interfaces.EntityManager;
import com.mc.tool.framework.interfaces.TaskManager;
import com.mc.tool.framework.interfaces.ViewManager;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Locale;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.scene.image.Image;
import javafx.stage.Window;
import lombok.extern.slf4j.Slf4j;
import org.hildan.fxgson.FxGson;

/**
 * .
 */
@Singleton
@Slf4j
@SuppressFBWarnings("UI_INHERITANCE_UNSAFE_GETRESOURCE")
public class DefaultApplication implements ApplicationBase {
  private final EntityFactory entityFactory;
  private final TaskManager taskManager;
  private final ViewManager viewManager;
  private final EntityManager entityManager;
  private OemInfo oemInfo = new OemInfo();
  protected Window mainWindow = null;

  private final ObjectProperty<Locale> language = new SimpleObjectProperty<>();
  private ConfigBean configBean;

  /**
   * Contructor.
   */
  public DefaultApplication() {
    entityFactory = createEntityFactory();
    taskManager = createTaskManager();
    viewManager = createViewManager();
    entityManager = createEntityManager();
    readConfigFile();

    if (configBean == null) {
      configBean = createConfigBean();
    }
    language.set(configBean.language.get());
    if (!language.getValue().equals(Locale.getDefault())) {
      Locale.setDefault(language.get());
    }

    //oem
    InputStream stream =
        Thread.currentThread().getContextClassLoader().getResourceAsStream("oeminfo.json");
    if (stream != null) {
      InputStreamReader reader = null;
      try {
        reader = new InputStreamReader(stream, StandardCharsets.UTF_8);
        oemInfo = new Gson().fromJson(reader, OemInfo.class);
      } catch (JsonSyntaxException | JsonIOException exception) {
        log.warn("Fail to load oeminfo.json!", exception);
      } finally {
        try {
          if (reader != null) {
            reader.close();
          }
          stream.close();
        } catch (IOException exception2) {
          log.warn("Fail to close the reader!", exception2);
        }

      }
    }
  }

  protected Class<? extends ConfigBean> getConfigBeanClass() {
    return ConfigBean.class;
  }

  protected ConfigBean createConfigBean() {
    return new ConfigBean();
  }

  protected void readConfigFile() {
    String configFile = getConfigFilePath();
    InputStreamReader reader = null;
    try {
      reader = new InputStreamReader(new FileInputStream(configFile), StandardCharsets.UTF_8);
      configBean = FxGson.create().fromJson(reader, getConfigBeanClass());
    } catch (FileNotFoundException | RuntimeException exception) {
      log.warn("Fail to load config.json!");
    } finally {
      if (reader != null) {
        try {
          reader.close();
        } catch (IOException exception2) {
          log.warn("Fail to close the reader");
        }
      }
    }
  }

  protected void writeConfigFile() {
    String configFile = getConfigFilePath();
    File file = new File(configFile);
    if (!file.exists()) {
      if (!file.getParentFile().mkdirs()) {
        log.warn("Fail to make dirs!");
      }
      try {
        if (!file.createNewFile()) {
          log.warn("Fail to create file!");
        }
      } catch (IOException ex) {
        log.warn("Fail to create file!", ex);
      }
    }
    try (FileOutputStream fos = new FileOutputStream(file)) {
      String value = FxGson.create().toJson(configBean);
      fos.write(value.getBytes(StandardCharsets.UTF_8));
    } catch (IOException exception) {
      log.warn("Fail to write config.json!", exception);
    }
  }

  protected String getConfigFilePath() {
    return System.getProperty("user.home") + "/." + getAppName() + "/config.json";
  }

  protected EntityFactory createEntityFactory() {
    return new DefaultEntityFactory();
  }

  protected TaskManager createTaskManager() {
    DefaultTaskManager taskManager = new DefaultTaskManager();
    taskManager.init();
    return taskManager;
  }

  protected EntityManager createEntityManager() {
    return new DefaultEntityManager();
  }

  protected ViewManager createViewManager() {
    return new DefaultViewManager();
  }

  public Locale getLanguage() {
    return language.get();
  }

  public String getDefaultImportPath() {
    return null;
  }

  public String getDefaultFirmwarePath() {
    return null;
  }


  public TaskManager getTaskManager() {
    return taskManager;
  }

  public ViewManager getViewManager() {
    return viewManager;
  }

  @Override
  public EntityFactory getEntityFactory() {
    return entityFactory;
  }

  @Override
  public void exit() {
    taskManager.exit();
    try {
      for (Entity entity : entityManager.getAllEntity()) {
        entityFactory.closeEntity(entity);
      }
      entityManager.removeEntity(entityManager.getAllEntity().toArray(new Entity[0]));
    } catch (RuntimeException exception) {
      log.warn("Fail to close entity!", exception);
    }

    writeConfigFile();
  }

  @Override
  public EntityManager getEntityMananger() {
    return entityManager;
  }

  @Override
  public String getAppName() {
    return "mc-framework";
  }

  @Override
  public String getAppTitle() {
    return "MC Framework";
  }

  @Override
  public String getFullVersion() {
    return "0.0.0";
  }

  @Override
  public int getMainVersion() {
    return 0;
  }

  @Override
  public int getSubVersion() {
    return 0;
  }

  @Override
  public int getModifyVersion() {
    return 0;
  }

  @Override
  public ConfigBean getConfigBean() {
    return configBean;
  }


  @Override
  public Collection<String> getAppAdditionalStyleSheet() {
    String style = getClass()
        .getResource("/com/mc/tool/framework/stagelogo.css").toExternalForm();
    return new ArrayList<>(Collections.singletonList(style));
  }

  @Override
  public Image getIcon() {
    return null;
  }

  @Override
  public Window getMainWindow() {
    return mainWindow;
  }

  @Override
  public void setMainWindow(Window mainWindow) {
    this.mainWindow = mainWindow;
  }

  @Override
  public void importFile(File file) {
  }

  @Override
  public String getExtraVersionInfo() {
    return "";
  }

  @Override
  public OemInfo getOemInfo() {
    return oemInfo;
  }

}
