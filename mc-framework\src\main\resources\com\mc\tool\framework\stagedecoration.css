/* Style for decoration. */

#decorationRoot {
    -fx-pref-height: 56;
}

.decoration-button-close {
    -fx-pref-width: 13;
    -fx-pref-height: 13;
    -fx-background-image: url("./img/close.png");
}

.decoration-button-close:hover {
    -fx-background-image: url("./img/close_hover.png");
}

.decoration-button-maximize {
    -fx-pref-width: 13;
    -fx-pref-height: 13;
    -fx-background-image: url("./img/maxmum.png");
}

.decoration-button-maximize:hover {

    -fx-background-image: url("./img/maxmum_hover.png");
}

.decoration-button-restore {
    -fx-pref-width: 13;
    -fx-pref-height: 13;
    -fx-background-image: url("./img/restore.png");
}

.decoration-button-restore:hover {
    -fx-background-image: url("./img/restore_hover.png");
}

.decoration-button-minimize {
    -fx-pref-width: 13;
    -fx-pref-height: 13;
    -fx-background-image: url("./img/minimum.png");
}

.decoration-button-minimize:hover {
    -fx-background-image: url("./img/minimum_hover.png");
}

.decorator-sub-container-left {
    -fx-padding: 6 0 0 25;
    -fx-pref-height: 56;
    -fx-min-height: 56;
}

.decorator-sub-container-right {
    -fx-spacing: 28;
    -fx-padding: 10 0 0 0;
    -fx-pref-height: 56;
    -fx-min-height: 56;
}

