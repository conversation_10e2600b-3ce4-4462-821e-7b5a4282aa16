package com.mc.tool.framework.utility;

import com.google.gson.Gson;
import com.google.gson.TypeAdapter;
import com.google.gson.TypeAdapterFactory;
import com.google.gson.reflect.TypeToken;
import java.lang.reflect.ParameterizedType;

/**
 * .
 */
public class ObservableListWrapperExTypeAdapterFactory implements TypeAdapterFactory {
  @Override
  public <T> TypeAdapter<T> create(Gson gson, TypeToken<T> type) {
    ParameterizedType parameterizedType = (ParameterizedType) type.getType();
    return new ObservableListWrapperExTypeAdapter(
        gson.getAdapter(TypeToken.get(parameterizedType.getActualTypeArguments()[0])));
  }
}
