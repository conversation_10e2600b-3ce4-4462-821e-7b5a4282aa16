package com.mc.tool.framework.operation.videowall.datamodel;

import com.google.gson.annotations.Expose;
import javafx.beans.property.IntegerProperty;
import javafx.beans.property.SimpleIntegerProperty;
import lombok.Getter;

/**
 * .
 */
public class VirtualLayoutData {
  @Expose
  @Getter
  private IntegerProperty rows = new SimpleIntegerProperty();
  @Expose
  @Getter
  private IntegerProperty columns = new SimpleIntegerProperty();
}
