<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.canvas.Canvas?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.VBox?>
<fx:root type="javafx.scene.layout.VBox" xmlns="http://javafx.com/javafx/8"
         xmlns:fx="http://javafx.com/fxml/1" stylesheets="@screenitem.css" id="container">
  <AnchorPane prefWidth="200" prefHeight="200">
    <Label AnchorPane.topAnchor="0" AnchorPane.bottomAnchor="0"
           AnchorPane.leftAnchor="0" AnchorPane.rightAnchor="0" id="screen-image"
           fx:id="screenImage"/>
    <Canvas fx:id="canvas" AnchorPane.topAnchor="45" AnchorPane.leftAnchor="30" width="140" height="76"/>
  </AnchorPane>
  <Label id="screen-text" fx:id="screenText" minWidth="50"
         textAlignment="center" alignment="center"/>
</fx:root>