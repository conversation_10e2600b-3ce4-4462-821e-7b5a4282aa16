package com.mc.graph.interfaces;

import com.mc.graph.connector.IntegerConnectorIdentifier;
import com.mc.graph.connector.StringConnectorIdentifier;
import javafx.beans.property.StringProperty;

public interface ConnectorIdentifier {

  StringProperty textProperty();

  /**
   * 获取id.
   * 
   * @param value 转换为id的object
   * @return id
   */
  static ConnectorIdentifier getIdentifier(Object value) {
    if (value instanceof Integer) {
      return new IntegerConnectorIdentifier((Integer) value);
    } else if (value instanceof String) {
      return new StringConnectorIdentifier((String) value);
    } else if (value instanceof ConnectorIdentifier) {
      return (ConnectorIdentifier) value;
    } else {
      throw new IllegalArgumentException();
    }
  }
}
