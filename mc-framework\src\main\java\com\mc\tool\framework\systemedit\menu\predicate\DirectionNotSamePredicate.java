package com.mc.tool.framework.systemedit.menu.predicate;

import com.mc.tool.framework.systemedit.datamodel.VisualEditNode;
import java.util.Collection;
import java.util.function.Predicate;

/**
 * .
 */
public class DirectionNotSamePredicate implements Predicate<Collection<VisualEditNode>> {

  @Override
  public boolean test(Collection<VisualEditNode> nodes) {
    if (nodes.isEmpty()) {
      return false;
    }
    boolean isRx = nodes.iterator().next().isRx();
    for (VisualEditNode node : nodes) {
      if (node.isRx() != isRx) {
        return true;
      }
    }
    return false;
  }

}
