<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.Button?>
<?import javafx.scene.control.TabPane?>
<?import javafx.scene.control.ToggleButton?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.Region?>
<?import javafx.scene.layout.VBox?>
<fx:root type="javafx.scene.layout.VBox" xmlns="http://javafx.com/javafx/8"
         xmlns:fx="http://javafx.com/fxml/1" stylesheets="@office_dynamic_view.css" id="rootPane">
  <TabPane tabClosingPolicy="UNAVAILABLE" fx:id="officesTabPanel"
           minHeight="40"/>
  <HBox id="mainContainer" VBox.Vgrow="ALWAYS">
    <VBox minWidth="200" fx:id="vboxTitledPane"/>
    <Region minWidth="1" id="seperator"/>
    <VBox id="rightPanel" HBox.Hgrow="ALWAYS" fx:id="rightPanel">
      <VBox fx:id="graphContainer" VBox.Vgrow="ALWAYS"/>
      <HBox alignment="center" minHeight="38" prefHeight="38"
            id="graph-bottom-toolbar">
        <ToggleButton id="preview-btn" styleClass="image-button"
                      fx:id="previewButton"/>
        <Button id="zoomin-btn" fx:id="zoominBtn" styleClass="image-button" onAction="#onZoomin"/>
        <Button id="zoomout-btn" fx:id="zoomoutBtn" styleClass="image-button" onAction="#onZoomout"/>
        <Button id="restore-btn" styleClass="image-button" onAction="#onRestore"/>
      </HBox>
    </VBox>
  </HBox>

</fx:root>