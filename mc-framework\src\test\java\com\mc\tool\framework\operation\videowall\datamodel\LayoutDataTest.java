package com.mc.tool.framework.operation.videowall.datamodel;

import com.google.common.collect.Lists;
import java.util.Arrays;
import java.util.List;
import javafx.geometry.Rectangle2D;
import org.junit.Assert;
import org.junit.Test;

/**
 * .
 */
public class LayoutDataTest {

  @Test
  public void testGetRows() {
    LayoutData data = new LayoutData();
    int row = 10;
    data.getRowsProperty().set(row);
    Assert.assertEquals(row, data.getRows());
  }

  @Test
  public void testGetColumns() {
    LayoutData data = new LayoutData();
    int columns = 10;
    data.getColumnsProperty().set(columns);
    Assert.assertEquals(columns, data.getColumns());
  }

  @Test
  public void testCopyTo() {
    LayoutData data = new LayoutData();
    data.getColumnsProperty().set(2);
    data.getRowsProperty().set(3);

    LayoutData newData = new LayoutData();
    data.copyTo(newData);

    Assert.assertEquals(data.getColumns(), newData.getColumns());
    Assert.assertEquals(data.getRows(), newData.getRows());
  }

  @Test
  public void testGetMultiResWithMarginAreas() {
    LayoutData data = new LayoutData();
    data.getRowsProperty().set(2);
    data.getColumnsProperty().set(2);
    data.getMultiRes().set(true);
    Integer widths[] = {100, 200};
    Integer heights[] = {300, 400};
    Integer horzMargins[] = {-10};
    Integer vertMargins[] = {-20};
    data.updateMultiResWidths(Arrays.asList(widths));
    data.updateMultiResHeights(Arrays.asList(heights));
    data.updateHorzMargins(Arrays.asList(horzMargins));
    data.updateVertMargins(Arrays.asList(vertMargins));
    List<Rectangle2D> areas = Lists.newArrayList(data.getScreenAreas());
    Assert.assertEquals(4, areas.size());
    Assert.assertEquals(widths[0] + widths[1] + horzMargins[0], data.getTotalWidth());
    Assert.assertEquals(heights[0] + heights[1] + vertMargins[0], data.getTotalHeight());

    int[] rectWidths = {widths[0], widths[1], widths[0], widths[1]};
    int[] rectHeights = {heights[0], heights[0], heights[1], heights[1]};
    int[] rectX = {0, widths[0] + horzMargins[0], 0, widths[0] + horzMargins[0]};
    int[] rectY = {0, 0, heights[0] + vertMargins[0], heights[0] + vertMargins[0]};

    for (int i = 0; i < 4; i++) {
      Assert.assertEquals(rectX[i], (int) areas.get(i).getMinX());
      Assert.assertEquals(rectY[i], (int) areas.get(i).getMinY());
      Assert.assertEquals(rectWidths[i], (int) areas.get(i).getWidth());
      Assert.assertEquals(rectHeights[i], (int) areas.get(i).getHeight());
    }
  }
}
