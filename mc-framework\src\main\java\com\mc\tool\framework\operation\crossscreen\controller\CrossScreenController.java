package com.mc.tool.framework.operation.crossscreen.controller;

import com.google.common.eventbus.Subscribe;
import com.mc.tool.framework.event.VisualEditFuncBeginUpdateEvent;
import com.mc.tool.framework.event.VisualEditFuncEndUpdateEvent;
import com.mc.tool.framework.interfaces.DeviceControllable;
import com.mc.tool.framework.operation.crossscreen.datamodel.CrossScreenObject;
import com.mc.tool.framework.systemedit.datamodel.CrossScreenFunc;
import com.mc.tool.framework.systemedit.datamodel.VisualEditFunc;
import com.mc.tool.framework.systemedit.datamodel.VisualEditModel;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import com.mc.tool.framework.utility.EventBusProvider;
import com.mc.tool.framework.utility.TypeWrapper;
import java.net.URL;
import java.util.Collection;
import java.util.Collections;
import java.util.ResourceBundle;
import java.util.concurrent.atomic.AtomicInteger;
import javafx.beans.property.ObjectProperty;
import javafx.event.ActionEvent;
import javafx.event.Event;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.Group;
import javafx.scene.control.Label;
import javafx.scene.control.ListView;
import javafx.scene.control.ScrollPane;
import javafx.scene.input.KeyCode;
import javafx.scene.input.KeyEvent;
import javafx.scene.input.MouseEvent;
import javafx.scene.layout.GridPane;
import javafx.scene.layout.HBox;
import javafx.scene.layout.StackPane;

/**
 * .
 */
public class CrossScreenController implements CrossScreenControllable, Initializable {
  protected VisualEditModel model = null;
  protected CrossScreenFunc func = null;
  protected ScreenItem[][] screenItems = null;

  public static final double MIN_SCALE = 0.5;
  public static final double MAX_SCALE = 2;
  public static final double SCALE_FACTOR = 1.1;

  private AtomicInteger updateCount = new AtomicInteger(0);

  @FXML
  protected ListView<CrossScreenObject> scenarioList;

  @FXML
  protected GridPane screenList;

  @FXML
  protected Group screenGroup;

  @FXML
  protected StackPane screenStack;

  @FXML
  protected ScrollPane screenScroller;

  @FXML
  protected HBox scenarioListContainer;

  @FXML
  protected Label zoominBtn;

  @FXML
  protected Label zoomoutBtn;

  @Override
  public void beginUpdate() {
    updateCount.incrementAndGet();
  }

  @Override
  public void endUpdate() {
    updateCount.decrementAndGet();
  }

  @Override
  public Collection<TypeWrapper> getMultiviewChannel(VisualEditTerminal rx) {
    return Collections.emptyList();
  }

  @Override
  public void connectMultiview(VisualEditTerminal tx, VisualEditTerminal rx, int mode,
                               int channel) {
    // Nothing to do
  }

  public boolean isUpdating() {
    return updateCount.get() != 0;
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {
    screenList.setGridLinesVisible(true);
    resetScreenItems();
    func.getResetObservable().addListener(change -> resetScreenItems());

    scenarioListContainer.managedProperty().bind(scenarioListContainer.visibleProperty());
    if (!canSaveScenario()) {
      scenarioListContainer.setVisible(false);
    }

    screenScroller.viewportBoundsProperty().addListener(
        (observable, oldValue, newValue) -> screenStack.setMinSize(newValue.getWidth(), newValue.getHeight()));

    zoominBtn.disableProperty().bind(screenGroup.scaleXProperty().greaterThanOrEqualTo(MAX_SCALE));
    zoomoutBtn.disableProperty().bind(screenGroup.scaleXProperty().lessThanOrEqualTo(MIN_SCALE));
  }

  private void resetScreenItems() {
    screenItems = new ScreenItem[func.getMaxRow()][];
    for (int i = 0; i < func.getMaxRow(); i++) {
      screenItems[i] = new ScreenItem[func.getMaxColumn()];
      for (int j = 0; j < func.getMaxColumn(); j++) {
        int index = i * func.getMaxColumn() + j;
        ScreenItem item = createScreenItem(i, j, func.getCrossScreenData().getTarget(index));
        screenList.add(item, j, i);
        screenItems[i][j] = item;
      }
    }
  }

  protected ScreenItem createScreenItem(int row, int column,
                                        ObjectProperty<VisualEditTerminal> target) {
    return new ScreenItem(row, column, target, model, func, this);
  }

  @Override
  public void onConfig() {

  }

  @Override
  public void saveScenario() {

  }

  @Override
  public void saveAsScenario() {

  }

  @Override
  public boolean isConfigable() {
    return false;
  }

  @Override
  public boolean canSaveScenario() {
    return false;
  }

  @Override
  public void setDeviceController(DeviceControllable deviceController) {

  }

  @Override
  public DeviceControllable getDeviceController() {
    return null;
  }

  @Override
  public void init(VisualEditModel model, VisualEditFunc currentFunction) {
    this.model = model;
    EventBusProvider.getEventBus().register(this);
    if (currentFunction instanceof CrossScreenFunc) {
      this.func = (CrossScreenFunc) currentFunction;
    }
  }

  @Override
  public boolean isConnetable(VisualEditTerminal tx, VisualEditTerminal rx) {
    return true;
  }

  @FXML
  protected void onZoomin(MouseEvent event) {
    screenGroup.setScaleX(screenGroup.getScaleX() * SCALE_FACTOR);
    screenGroup.setScaleY(screenGroup.getScaleY() * SCALE_FACTOR);
  }

  @FXML
  protected void onZoomout(MouseEvent event) {
    screenGroup.setScaleX(screenGroup.getScaleX() / SCALE_FACTOR);
    screenGroup.setScaleY(screenGroup.getScaleY() / SCALE_FACTOR);
  }

  @FXML
  protected void onRestore(MouseEvent event) {
    screenGroup.setScaleX(1);
    screenGroup.setScaleY(1);
  }

  @FXML
  protected void onScenarioToLeft(ActionEvent event) {
    Event.fireEvent(scenarioList,
        new KeyEvent(KeyEvent.KEY_PRESSED, "", "", KeyCode.PAGE_UP, false, false, false, false));
  }

  @FXML
  protected void onScenarioToRight(ActionEvent event) {
    Event.fireEvent(scenarioList,
        new KeyEvent(KeyEvent.KEY_PRESSED, "", "", KeyCode.PAGE_DOWN, false, false, false, false));
  }

  @Override
  public void connectForCrossScreen(VisualEditTerminal tx, VisualEditTerminal rx) {
  }

  @Subscribe
  protected void onFuncBeginUpdate(VisualEditFuncBeginUpdateEvent event) {
    if (event.getFunc() == func) {
      beginUpdate();
    }
  }

  @Subscribe
  protected void onFuncEndUpdate(VisualEditFuncEndUpdateEvent event) {
    if (event.getFunc() == func) {
      endUpdate();
    }
  }

  @Override
  public void close() {
    EventBusProvider.getEventBus().unregister(this);
  }
}
