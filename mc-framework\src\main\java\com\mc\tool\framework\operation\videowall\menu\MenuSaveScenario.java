package com.mc.tool.framework.operation.videowall.menu;

import com.mc.tool.framework.operation.videowall.controller.VideoWallControllable;
import com.mc.tool.framework.operation.videowall.datamodel.interfaces.VideoWallObject;
import com.mc.tool.framework.systemedit.datamodel.VideoWallFunc;
import com.mc.tool.framework.systemedit.view.ViewUtility;
import com.mc.tool.framework.utility.I18nUtility;
import java.util.Optional;
import javafx.scene.control.MenuItem;

/**
 * .
 */
public class MenuSaveScenario extends MenuItem {
  private VideoWallControllable controllable;

  /**
   * Constructor.
   *
   * @param controllable controllable
   */
  public MenuSaveScenario(VideoWallControllable controllable) {
    this.controllable = controllable;
    setText(getMenuText());
    setOnAction((event) -> onAction());
  }

  protected void onAction() {
    VideoWallFunc func = controllable.getVideoWallFunction();
    Optional<String> result = ViewUtility.getNameFromDialog(getParentPopup().getOwnerWindow(), "");
    if (result.isPresent()) {
      String name = result.get();
      VideoWallObject videoWallData = func.createVideoWallObject();
      func.getVideoWallObject().copyTo(videoWallData, false);
      videoWallData.getName().set(name);
      func.addScenario(videoWallData);
    }
  }

  protected String getMenuText() {
    return I18nUtility.getI18nBundle("operation").getString("menu.save_scenario");
  }

}
