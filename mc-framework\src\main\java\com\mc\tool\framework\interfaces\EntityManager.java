package com.mc.tool.framework.interfaces;

import javafx.collections.ObservableList;

/**
 * .
 */
public interface EntityManager {
  /**
   * 添加实体.
   *
   * @param entities 要添加的实体.
   */
  void addEntity(Entity... entities);

  void removeEntity(Entity... entities);

  /**
   * 获取实体列表.
   *
   * @param type 实体的类型.
   * @return 实体列表
   */
  ObservableList<Entity> getEntityList(String type);

  ObservableList<Entity> getAllEntity();
}
