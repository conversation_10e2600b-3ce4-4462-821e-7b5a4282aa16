package com.mc.tool.framework.systemedit.view;

import com.mc.graph.interfaces.CellBehavior;
import com.mc.graph.interfaces.CellBindedObject;
import com.mc.graph.interfaces.CellObject;
import com.mc.graph.interfaces.ConnectorSkin;
import com.mc.graph.interfaces.SkinManager;
import com.mc.tool.framework.systemedit.datamodel.SystemEditDefinition;
import com.mc.tool.framework.systemedit.datamodel.VisualEditGroup;
import com.mc.tool.framework.utility.JavaFxUtility;
import java.io.IOException;
import java.net.URL;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.ResourceBundle;
import javafx.application.Platform;
import javafx.beans.binding.Bindings;
import javafx.beans.binding.ObjectBinding;
import javafx.event.EventHandler;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.geometry.Pos;
import javafx.scene.Node;
import javafx.scene.Parent;
import javafx.scene.control.Label;
import javafx.scene.image.Image;
import javafx.scene.input.MouseButton;
import javafx.scene.input.MouseEvent;
import javafx.scene.layout.Background;
import javafx.scene.layout.BackgroundFill;
import javafx.scene.layout.BackgroundImage;
import javafx.scene.layout.BackgroundPosition;
import javafx.scene.layout.BackgroundRepeat;
import javafx.scene.layout.Border;
import javafx.scene.layout.BorderStroke;
import javafx.scene.layout.BorderStrokeStyle;
import javafx.scene.layout.HBox;
import javafx.scene.layout.Priority;
import javafx.scene.layout.Region;
import javafx.scene.paint.Color;
import javafx.scene.shape.SVGPath;
import javafx.scene.text.TextAlignment;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public final class GroupCellSkin extends AbstractCellSkinEx implements Initializable {
  private HBox region;
  @FXML
  private HBox mainContainer;
  @FXML
  private Region parentConnector;
  @FXML
  private Region childrenConnector;
  @FXML
  private Label nameLabel;
  @FXML
  private Label icon;
  @FXML
  private HBox nameLabelContainer;

  private Map<Object, ConnectorSkin> connectorSkins;

  private Boolean leftGroup;

  private static SVGPath childrenConnectorShape = new SVGPath();
  private static Background childrenConnectorExpandBg;
  private static Background childrenConnectorCollapseBg;
  private static Border groupNormalBorder;
  private static Border groupHighlightBorder;

  private static SVGPath mainContainerShape = new SVGPath();
  private static Background mainContainerBg;

  static {
    childrenConnectorShape.setContent("M8,.5A7.5,7.5,0,1,1,.5,8,7.5,7.5,0,0,1,8,.5Z");
    BackgroundFill childrenConnectorBgColor = new BackgroundFill(Color.web("#f5f5f5"), null, null);
    ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
    Image expandImage = new Image(
        classLoader.getResourceAsStream("com/mc/tool/framework/systemedit/group_expand.png"));
    Image collapseImage = new Image(
        classLoader.getResourceAsStream("com/mc/tool/framework/systemedit/group_collapse.png"));
    BackgroundImage expandBgImage = new BackgroundImage(expandImage, BackgroundRepeat.NO_REPEAT,
        BackgroundRepeat.NO_REPEAT, BackgroundPosition.CENTER, null);
    BackgroundImage collapseBgImage = new BackgroundImage(collapseImage, BackgroundRepeat.NO_REPEAT,
        BackgroundRepeat.NO_REPEAT, BackgroundPosition.CENTER, null);
    childrenConnectorExpandBg =
        new Background(Arrays.asList(childrenConnectorBgColor), Arrays.asList(expandBgImage));
    childrenConnectorCollapseBg =
        new Background(Arrays.asList(childrenConnectorBgColor), Arrays.asList(collapseBgImage));

    groupNormalBorder =
        new Border(new BorderStroke(Color.web("#969899"), BorderStrokeStyle.SOLID, null, null));
    groupHighlightBorder = new Border(new BorderStroke(SystemEditDefinition.LABEL_HIGHLIGHT_COLOR,
        BorderStrokeStyle.SOLID, null, null));

    mainContainerShape.setContent(
        "M5.5.5h90a5,5,0,0,1,5,5v25a5,5,0,0,1-5,5H5.5a5,5,0,0,1-5-5V5.5A5,5,0,0,1,5.5.5Z");
    mainContainerBg = new Background(new BackgroundFill(Color.web("#f5f5f5"), null, null));
  }

  public GroupCellSkin(CellObject cellobject, Parent parent, Parent container,
                       SkinManager skinManager) {
    super(cellobject, parent, container, skinManager);
    movableProperty().set(false);
  }

  private boolean isLeftGroup() {
    if (leftGroup == null) {
      leftGroup = true;
    }
    return leftGroup;
  }


  @Override
  public Region getRegion() {
    return region;
  }

  protected Map<Object, ConnectorSkin> getConnectorSkinsMap() {
    if (connectorSkins == null) {
      connectorSkins = new HashMap<>();
    }
    return connectorSkins;
  }

  @Override
  public Collection<ConnectorSkin> getConnectorSkins() {
    return getConnectorSkinsMap().values();
  }

  @Override
  public Color getSelectionBorderColor() {
    return SystemeditConstants.SELECTED_COLOR;
  }

  @Override
  public boolean isSelectionEffectEnabled() {
    return false;
  }

  @Override
  public boolean isResizeble() {
    return false;
  }

  @Override
  protected void initInner() {

  }

  /**
   * 设置group的图标.
   *
   * @param image 图标的图片
   */
  public void setIcon(Image image) {
    icon.setBackground(new Background(new BackgroundImage(image, BackgroundRepeat.NO_REPEAT,
        BackgroundRepeat.NO_REPEAT, BackgroundPosition.CENTER, null)));
    icon.setMinWidth(image.getWidth());
    icon.setMinHeight(image.getHeight());
  }

  @Override
  protected void initRegion() {
    //createRegionByFxml();
    createRegionByCode();
    region.setUserData(this);

    childrenConnector.addEventHandler(MouseEvent.MOUSE_CLICKED,
        weakAdapter.wrap((EventHandler<MouseEvent>) (event) -> {
          if (event.getButton() == MouseButton.PRIMARY
              && cellObject.getBindedObject() instanceof VisualEditGroup) {
            // 左键单击修改collapse的状态
            VisualEditGroup group = (VisualEditGroup) cellObject.getBindedObject();
            group.collapseProperty().set(!group.collapseProperty().get());
          }
        }));

    updateLayout(cellObject.getBindedObject());
    cellObject.getBindedObjectProperty()
        .addListener(weakAdapter.wrap((observable, oldValue, newValue) -> updateLayout(newValue)));
  }

  protected void createRegionByFxml() {
    try {
      FXMLLoader loader = new FXMLLoader(
          getClass().getResource("/com/mc/tool/framework/systemedit/group_cell_skin.fxml"));
      loader.setController(this);
      region = loader.load();
    } catch (IOException exc) {
      log.warn("Can not load group_cell_skin.fxml", exc);
    }
  }

  private void createRegionByCode() {
    region = new HBox();
    region.setMinSize(116, 35);
    region.setMaxSize(116, 35);
    region.setAlignment(Pos.CENTER);
    region.getStyleClass().add("group");
    region.getStylesheets().add(SystemeditConstants.RESOURCE_PATH + "group_cell_skin.css");

    childrenConnector = new Region();
    childrenConnector.setMinSize(16, 16);
    childrenConnector.setMaxSize(16, 16);
    childrenConnector.getStyleClass().add("parent-connector");
    region.getChildren().add(childrenConnector);

    mainContainer = new HBox();
    HBox.setHgrow(mainContainer, Priority.ALWAYS);
    mainContainer.setAlignment(Pos.CENTER);
    region.getChildren().add(mainContainer);

    Region reserved = new Region();
    reserved.setMinWidth(5);
    reserved.setMaxWidth(5);
    mainContainer.getChildren().add(reserved);

    nameLabelContainer = new HBox();
    HBox.setHgrow(nameLabelContainer, Priority.ALWAYS);
    nameLabelContainer.setAlignment(Pos.CENTER);
    mainContainer.getChildren().add(nameLabelContainer);

    nameLabel = new NoCssLabel();
    nameLabel.setTextAlignment(TextAlignment.CENTER);
    nameLabelContainer.getChildren().add(nameLabel);

    icon = new Label();
    mainContainer.getChildren().add(icon);

    parentConnector = new Region();
    parentConnector.setMinWidth(5);
    parentConnector.setMaxWidth(5);
    mainContainer.getChildren().add(parentConnector);

    // css，必须是滞后设置css，不然会导致连接线出问题.
    Platform.runLater(() -> {
      childrenConnector.setShape(childrenConnectorShape);
      mainContainer.setShape(mainContainerShape);
      mainContainer.setBackground(mainContainerBg);
    });

    initialize(null, null);
  }

  @Override
  protected void onConnectorChange() {
    if (getCell().getBindedObject() == null) {
      return;
    }
    VisualEditGroup group = (VisualEditGroup) getCell().getBindedObject();
    for (Object id : group.getConnectorId()) {
      if (getConnectorSkinsMap().containsKey(id)
          && getConnectorSkinsMap().get(id).getConnector() == getCell().getConnector(id)) {
        continue;
      }
      if (getCell().getConnector(id) == null) {
        continue;
      }
      // connecotr的父节点，由于两个connector的父节点是不一样，要区别开来.
      Parent parent;
      Node connector;
      int heightOffset = 17;
      if (id.equals(VisualEditGroup.GROUP_PARENT_CONNECTOR)) {
        connector = parentConnector;
        parent = mainContainer;
      } else if (id.equals(VisualEditGroup.GROUP_CHILD_CONNECTOR)) {
        connector = childrenConnector;
        parent = region;
        heightOffset = 7;
      } else {
        log.error("Unexpected connector id : {}.", id);
        continue;
      }
      SimpleConnectorSkin skin = new SimpleConnectorSkin(getCell().getConnector(id), parent, region,
          container, connector, heightOffset);
      skinManager.setConnectorSkin(getCell().getConnector(id), skin);
      for (CellBehavior cellBehavior : cellBehaviors) {
        cellBehavior.createConnectorBehavior(skin);
      }
      connectorSkins.put(id, skin);
    }
    updateConnectorDirection();
  }

  @Override
  protected void layoutConnectors() {

  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {
    init();

    ObjectBinding<Border> borderBinding = Bindings.when(cellObject.highLightProperty())
        .then(groupHighlightBorder).otherwise(groupNormalBorder);
    mainContainer.borderProperty().bind(borderBinding);
    childrenConnector.borderProperty().bind(borderBinding);
  }

  private void updateLayout(CellBindedObject newValue) {
    if (newValue instanceof VisualEditGroup) {
      VisualEditGroup group = (VisualEditGroup) newValue;
      // rx的布局与tx布局相反
      if (!SystemEditDefinition.nodeAtLeft(group)) {
        setRight();
      }
      childrenConnector.backgroundProperty().bind(Bindings.createObjectBinding(() -> {
        if (group.collapseProperty().get()) {
          return childrenConnectorCollapseBg;
        } else {
          return childrenConnectorExpandBg;
        }
      }, group.collapseProperty()));
    }
  }

  private void setRight() {
    if (isLeftGroup()) {
      JavaFxUtility.reversePaneContent(region, true);
      leftGroup = false;
      updateConnectorDirection();
    }
  }

  private void updateConnectorDirection() {
    // 如果group在matrix的左侧，那么parent的连接点在右侧，与child的连接点在左侧
    SimpleConnectorSkin skin =
        (SimpleConnectorSkin) getConnectorSkinsMap().get(VisualEditGroup.GROUP_PARENT_CONNECTOR);
    if (skin != null) {
      skin.setConnectAtLeft(!isLeftGroup());
    }

    skin = (SimpleConnectorSkin) getConnectorSkinsMap().get(VisualEditGroup.GROUP_CHILD_CONNECTOR);
    if (skin != null) {
      skin.setConnectAtLeft(isLeftGroup());
    }

  }

  @Override
  protected Label getNameLabel() {
    return nameLabel;
  }

  @Override
  protected Node[] getNameLabelChangeSource() {
    return new Node[] {nameLabel, nameLabelContainer};
  }


}
