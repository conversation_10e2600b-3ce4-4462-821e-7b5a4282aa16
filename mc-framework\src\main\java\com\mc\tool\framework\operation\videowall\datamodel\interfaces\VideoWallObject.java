package com.mc.tool.framework.operation.videowall.datamodel.interfaces;

import com.mc.tool.framework.operation.videowall.datamodel.IVideoWallLayout;
import com.mc.tool.framework.operation.videowall.datamodel.LayoutData;
import com.mc.tool.framework.operation.videowall.datamodel.LogicLayoutData;
import java.util.Collection;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.StringProperty;
import javafx.collections.ObservableList;
import javafx.util.Pair;

/**
 * .
 */
public interface VideoWallObject {
  default IVideoWallLayout getUserLayout() {
    return getLayoutData();
  }

  LayoutData getLayoutData();

  int getScreenCount();

  ObservableList<? extends VideoObject> getVideos();

  boolean addVideo(VideoObject video);

  boolean addVideo(int index, VideoObject video);

  boolean addAll(int index, Collection<VideoObject> videos);

  boolean setVideo(int index, VideoObject video);

  ObservableList<? extends ScreenObject> getScreens();

  Pair<Integer, Integer> getScreenLocation(ScreenObject screenData);

  int getScreenIndex(ScreenObject screenData);
  
  void copyTo(VideoWallObject copy, boolean copyScreen);

  StringProperty getName();

  default boolean hasIndex() {
    return false;
  }

  default int getIndex() {
    throw new RuntimeException();
  }

  LogicLayoutData getLogicLayoutData();

  BooleanProperty getUseLogicLayout();

  void setType(short type);
}
