package org.controlsfx.control;

import javafx.application.Platform;
import javafx.beans.property.BooleanProperty;
import javafx.beans.value.ObservableValue;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.Node;
import javafx.scene.control.Button;
import javafx.scene.control.CheckBox;
import javafx.scene.control.ColorPicker;
import javafx.scene.control.ColorPickerEx;
import javafx.scene.control.ComboBox;
import javafx.scene.control.Label;
import javafx.scene.control.ListView;
import javafx.scene.control.Spinner;
import javafx.scene.control.TextInputControl;
import javafx.scene.control.Tooltip;
import javafx.scene.layout.GridPane;
import javafx.scene.layout.HBox;
import javafx.scene.layout.Priority;
import javafx.scene.paint.Color;
import javafx.scene.paint.Paint;
import javafx.scene.text.TextAlignment;
import lombok.Getter;
import org.controlsfx.control.PropertySheet.Item;
import org.controlsfx.property.editor.AbstractPropertyEditor;
import org.controlsfx.property.editor.DefaultPropertyEditorFactory;
import org.controlsfx.property.editor.NumericField;
import org.controlsfx.property.editor.PropertyEditor;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

public class ExPropertyEditorFactory extends DefaultPropertyEditorFactory {
  @SuppressWarnings("unchecked")
  @Override
  public PropertyEditor<?> call(Item item) {
    if (item instanceof EnumPropertyItem) {
      return createChoiceEditor((EnumPropertyItem) item,
          Arrays.<Object>asList(item.getType().getEnumConstants()));
    } else if (item instanceof ChoicesPropertyItem<?>) {
      return createChoiceEditor((ChoicesPropertyItem<?>) item);
    } else if (item instanceof TextBlockPropertyItem) {
      return createTextBlockEditor((TextBlockPropertyItem) item);
    } else if (item.getType() == Boolean.class) {
      return createCheckEditor(item);
    } else if (isNumber(item.getType())) {
      return createNumericEditor(item);
    } else if (item.getType() == Color.class || item.getType() == Paint.class) {
      return createColorEditor(item);
    } else if (item instanceof ListPropertyItem) {
      return createListEditor((ListPropertyItem) item);
    }
    return super.call(item);
  }

  static class TextBlockControl extends HBox {
    @Getter
    private final Label label;
    @Getter
    private final Button button;

    public TextBlockControl() {
      label = new Label();
      label.setTextAlignment(TextAlignment.LEFT);
      Tooltip tip = new Tooltip();
      tip.textProperty().bind(label.textProperty());
      tip.setWrapText(true);
      tip.setMaxWidth(200);
      label.setTooltip(tip);
      button = new Button();
      button.setMinWidth(13);
      button.setMaxWidth(13);
      button.getStyleClass().add("image-button");
      button.setStyle("-fx-background-color:#e6e6e6;"
          + "-fx-background-image:url(\"/com/mc/common/control/ellipsis.png\");"
          + "-fx-background-position:center;" + "-fx-background-repeat:no-repeat");
      HBox labelContainer = new HBox(label);
      getChildren().add(labelContainer);
      getChildren().add(button);
      
      labelContainer.setAlignment(Pos.CENTER_LEFT);
      HBox.setHgrow(labelContainer, Priority.ALWAYS);
      setAlignment(Pos.CENTER_LEFT);
    }

  }
  
  /**
   * 创建颜色编辑器.
   * @param property property
   * @return 颜色编辑器.
   */
  public static final PropertyEditor<?> createColorEditor(Item property) {
    ColorPicker picker = new ColorPickerEx();
    picker.getStyleClass().add("property-color-picker");
    return new AbstractPropertyEditor<Color, ColorPicker>(property, picker) {

      @Override
      protected ObservableValue<Color> getObservableValue() {
        return getEditor().valueProperty();
      }

      @Override
      public void setValue(Color value) {
        getEditor().setValue((Color) value);
      }
    };
  }

  /**
   * 创建一个textblock的编辑器.
   * @param property 属性
   * @return 编辑器
   */
  public static final PropertyEditor<?> createTextBlockEditor(TextBlockPropertyItem property) {

    TextBlockControl control = new TextBlockControl();
    control.getButton().setOnAction(property.getTextAction());
    if (!property.getId().isEmpty()) {
      control.setId(property.getId());
    }
    return new AbstractPropertyEditor<String, TextBlockControl>(property, control) {

      @Override
      public void setValue(String value) {
        getEditor().getLabel().setText(value);
      }

      @Override
      protected ObservableValue<String> getObservableValue() {
        return getEditor().getLabel().textProperty();
      }
    };
  }

  /**
   * 创建一个checkbox的编辑器.
   * @param property 属性
   * @return 编辑器
   */
  public static final PropertyEditor<?> createCheckEditor(Item property) {
    CheckBox checkBox = new CheckBox();
    GridPane.setMargin(checkBox, new Insets(0, 0, 0, 10));
    return new AbstractPropertyEditor<Boolean, CheckBox>(property, checkBox) {

      @Override
      protected BooleanProperty getObservableValue() {
        return getEditor().selectedProperty();
      }

      @Override
      public void setValue(Boolean value) {
        if (value == null) {
          getEditor().setSelected(false);
        } else {
          getEditor().setSelected((Boolean) value);
        }
      }
    };

  }

  /**
   * 创建多项选择的编辑器.
   * 
   * @param property 属性
   * @return 编辑器
   */
  public static final <T> PropertyEditor<?> createChoiceEditor(ChoicesPropertyItem<T> property) {
    ComboBox<T> combo = new ComboBox<>();
    combo.getStyleClass().add("property-combo");
    if (property.getConverter() != null) {
      combo.setConverter(property.getConverter());
    }
    if (!property.getId().isEmpty()) {
      combo.setId(property.getId());
    }
    return new AbstractPropertyEditor<T, ComboBox<T>>(property, combo) {

      {
        getEditor().setItems(property.getChoices().filtered(property.getFilter()));
      }

      @Override
      protected ObservableValue<T> getObservableValue() {
        return getEditor().getSelectionModel().selectedItemProperty();
      }

      @Override
      public void setValue(T value) {
        getEditor().getSelectionModel().select(value);
      }
    };
  }

  /**
   * 创建多项选择的编辑器.
   * 
   * @param property 属性
   * @param choices 选项
   * @return 编辑器
   */
  public static final <T> PropertyEditor<?> createChoiceEditor(EnumPropertyItem<T> property,
      final Collection<T> choices) {
    ComboBox<T> combo = new ComboBox<>();
    combo.getStyleClass().add("property-combo");
    if (property.getConverter() != null) {
      combo.setConverter(property.getConverter());
    }
    if (!property.getId().isEmpty()) {
      combo.setId(property.getId());
    }
    List<T> filteredChoices = new ArrayList<>(choices);
    if (property.getFilter() != null) {
      filteredChoices.clear();
      filteredChoices
          .addAll(choices.stream().filter(property.getFilter()).collect(Collectors.toList()));
    }
    return new AbstractPropertyEditor<T, ComboBox<T>>(property, combo) {

      {
        getEditor().setItems(FXCollections.observableArrayList(filteredChoices));
      }

      @Override
      protected ObservableValue<T> getObservableValue() {
        return getEditor().getSelectionModel().selectedItemProperty();
      }

      @Override
      public void setValue(T value) {
        getEditor().getSelectionModel().select(value);
      }
    };
  }
  
  /**
   * 创建列表编辑器.
   */
  public static final <T> PropertyEditor<?> createListEditor(ListPropertyItem property) {
    ListView<T> listView = new ListView<T>() {
      private boolean rendered = false;
      @Override
      protected void layoutChildren() {
        super.layoutChildren();
        if (!rendered) {
          rendered = true;
          String[] selectors = new String[] {
            ".increment-button", ".increment-arrow", 
            ".decrement-button", ".decrement-arrow"
          };
          for (Node node : lookupAll(".scroll-bar")) {
            node.setStyle("-fx-padding:0");
            for (String selector : selectors) {
              Node subNode = node.lookup(selector);
              if (subNode != null) {
                subNode.setStyle("-fx-padding:0");
              }
            }
          }
        }
      }
    };
    
    return new AbstractPropertyEditor<T, ListView<T>>(property, listView) {
      {
        getEditor().setItems((ObservableList)property.getValue());
        getEditor().setPrefHeight(getEditor().getItems().size() * 25);
      }

      @Override
      public void setValue(T value) {
        // TODO Auto-generated method stub
        
      }

      @Override
      protected ObservableValue<T> getObservableValue() {
        return null;
      }
      
    };

  }
  
  /**
   * 创建数字编辑器.
   */
  public static final PropertyEditor<?> createNumericEditor(Item property) {
    if (!(property instanceof NumberPropertyItem)) {
      return null;
    }
    if (property instanceof SpinnerPropertyItem) {
      SpinnerPropertyItem item = (SpinnerPropertyItem)property;
      Spinner<Number> spinner = new Spinner<>(item.getMinValue().intValue(),
          item.getMaxValue().intValue(), item.getMinValue().intValue());
      spinner.getStyleClass().add("property-spinner");
      spinner.setId(((SpinnerPropertyItem) property).getId());
      return new AbstractPropertyEditor<Number, Spinner<Number>>(property, spinner) {

        @Override
        public void setValue(Number value) {
          Number currentValue = getEditor().getValue();
          if (currentValue.intValue() > value.intValue()) {
            getEditor().decrement(currentValue.intValue() - value.intValue());
          } else {
            getEditor().increment(value.intValue() - currentValue.intValue());
          }
        }

        @Override
        protected ObservableValue<Number> getObservableValue() {
          return getEditor().valueProperty();
        }

      };
    } else {
      NumericField field = new NumericField((Class<? extends Number>) property.getType(),
          ((NumberPropertyItem) property).getMaxValue(),
          ((NumberPropertyItem) property).getMinValue(),
          ((NumberPropertyItem) property).getPrecision());
      field.setId(((NumberPropertyItem) property).getId());
      return new AbstractPropertyEditor<Number, NumericField>(property, field) {
        {
          enableAutoSelectAll(getEditor());
        }
  
        @Override
        protected ObservableValue<Number> getObservableValue() {
          return getEditor().valueProperty();
        }
  
        @Override
        public Number getValue() {
          return getEditor().valueProperty().getValue();
        }
  
        @Override
        public void setValue(Number value) {
          getEditor().valueProperty().setValue(value);
        }
  
      };
    }

  }

  private static void enableAutoSelectAll(final TextInputControl control) {
    control.focusedProperty().addListener(
        (ObservableValue<? extends Boolean> ob, Boolean oldValue, Boolean newValue) -> {
          if (newValue) {
            Platform.runLater(() -> {
              control.selectAll();
            });
          }
        });
  }

  private static boolean isNumber(Class<?> type) {
    if (type == null) {
      return false;
    }
    for (Class<?> cls : numericTypes) {
      if (type == cls) {
        return true;
      }
    }
    return false;
  }

  private static Class<?>[] numericTypes = new Class[] {byte.class, Byte.class, short.class,
      Short.class, int.class, Integer.class, long.class, Long.class, float.class, Float.class,
      double.class, Double.class, BigInteger.class, BigDecimal.class};
}
