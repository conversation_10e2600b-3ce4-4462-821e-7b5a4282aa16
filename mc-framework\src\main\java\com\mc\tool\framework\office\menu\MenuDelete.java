package com.mc.tool.framework.office.menu;

import com.mc.tool.framework.office.controller.OfficeControllable;
import com.mc.tool.framework.utility.I18nUtility;
import javafx.scene.control.MenuItem;

/**
 * .
 */
public class MenuDelete extends MenuItem {
  private OfficeControllable controllable;

  /**
   * Constructor.
   *
   * @param controllable controllable
   */
  public MenuDelete(OfficeControllable controllable) {
    this.controllable = controllable;
    this.setOnAction((event) -> {
      onAction();
    });
    this.setText(I18nUtility.getI18nBundle("office").getString("menu.delete"));
  }

  private void onAction() {
    controllable.deleteSelectedFuncs();
  }
}
