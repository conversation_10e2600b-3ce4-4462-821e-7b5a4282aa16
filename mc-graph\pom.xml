<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<groupId>com.mc</groupId>
	<artifactId>mc-graph</artifactId>
	<version>0.0.1-SNAPSHOT</version>
	<packaging>jar</packaging>

	<name>mc-graph</name>
	<url>http://maven.apache.org</url>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<maven.jxr.version>3.1.1</maven.jxr.version>
		<maven.checkstyle.version>3.0.0</maven.checkstyle.version>
		<maven.pmd.version>3.9.0</maven.pmd.version>
		<maven.findbugs.version>3.0.5</maven.findbugs.version>
		<mc.common.version>0.0.1-SNAPSHOT</mc.common.version>
		<controlsfx.version>8.40.14</controlsfx.version>
		<lombok.version>1.18.26</lombok.version>
		<slf4j.version>1.7.36</slf4j.version>
		<maven.compiler.version>3.9.0</maven.compiler.version>
		<gson.version>2.10.1</gson.version>
		<junit.version>4.13.2</junit.version>
		<guava.version>32.1.1-jre</guava.version>
		<log4j.version>2.20.0</log4j.version>
	</properties>
	<reporting>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jxr-plugin</artifactId>
				<version>${maven.jxr.version}</version>
			</plugin>
		</plugins>
	</reporting>
	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>${maven.compiler.version}</version>
				<configuration>
					<source>1.8</source>
					<target>1.8</target>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-checkstyle-plugin</artifactId>
				<version>${maven.checkstyle.version}</version>
				<configuration>
					<configLocation>google_checks.xml</configLocation>
					<encoding>UTF-8</encoding>
					<failsOnError>true</failsOnError>
					<failOnViolation>true</failOnViolation>
					<violationSeverity>info</violationSeverity>
				</configuration>
				<executions>
					<execution>
						<id>checkstyle</id>
						<phase>validate</phase>
						<goals>
							<goal>check</goal>
						</goals>
						<configuration>
							<failOnViolation>true</failOnViolation>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-pmd-plugin</artifactId>
				<version>${maven.pmd.version}</version>
				<configuration>
					<analysisCache>true</analysisCache>
					<linkXRef>true</linkXRef>
				</configuration>
				<executions>
					<execution>
						<id>pmd</id>
						<phase>compile</phase>
						<goals>
							<goal>check</goal>
							<goal>cpd-check</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>findbugs-maven-plugin</artifactId>
				<version>${maven.findbugs.version}</version>
				<configuration>
					<effort>Max</effort>
					<threshold>Low</threshold>
					<xmlOutput>true</xmlOutput>
				</configuration>
				<executions>
					<execution>
						<phase>compile</phase>
						<goals>
							<goal>check</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
	<dependencies>
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<version>${junit.version}</version>
			<scope>test</scope>
		</dependency>
		<dependency>
            <groupId>com.mc</groupId>
            <artifactId>mc-common</artifactId>
            <version>${mc.common.version}</version>
        </dependency>
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<version>${lombok.version}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-core</artifactId>
			<version>${log4j.version}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-slf4j-impl</artifactId>
			<version>${log4j.version}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-api</artifactId>
			<version>${log4j.version}</version>
		</dependency>
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-api</artifactId>
			<version>${slf4j.version}</version>
		</dependency>
		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>guava</artifactId>
			<version>${guava.version}</version>
		</dependency>
		<dependency>
			<groupId>com.google.code.gson</groupId>
			<artifactId>gson</artifactId>
			<version>${gson.version}</version>
		</dependency>
	</dependencies>
</project>
