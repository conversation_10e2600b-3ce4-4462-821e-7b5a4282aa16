package com.mc.tool.framework.systemedit.datamodel;

import javafx.scene.paint.Color;

/**
 * .
 */
public class SystemEditDefinition {
  public static final String MATRIX_CELL = "matrix.cell";
  public static final String TERMINAL_CELL = "terminal.cell";
  public static final String EMPTY_TERMINAL_CELL = "terminal.cell";
  public static final String SUB_TERMINAL_CELL = "subterminal.cell";
  public static final String GROUP_CELL = "GROUP.CELL";
  public static final String AUDIO_GROUP_CELL = "audio.group.cell";
  public static final String VIDEO_WALL_CELL = "video.wall.cell";
  public static final String SEAT_CELL = "seat.cell";
  public static final String PREVIEW_CELL = "preview.cell";
  public static final String SNAPSHOT_CELL = "snapshot.cell";
  public static final double CELL_X_OFFSET = 100;
  public static final double CELL_Y_OFFSET = 17;
  public static final boolean TX_AT_LEFT = true;

  /**
   * 由于所有的emptyterminal是一样的，所以共用guid.
   */
  public static final String EMPTY_TERMINAL_GUID = "empty.terminal.guid";

  public static final Color LINK_NORMAL_COLOR = Color.rgb(193, 193, 193);
  public static final Color LINK_HIGHLIGHT_COLOR = Color.rgb(255, 177, 7);
  public static final Color LABEL_NORMAL_COLOR = Color.rgb(0x33, 0x33, 0x33);
  public static final Color LABEL_HIGHLIGHT_COLOR = LINK_HIGHLIGHT_COLOR;

  public static final String CELL_VISIBILITY_UPDATER = "cell_visibility_updater";
  public static final String CELL_XPOS_UPDATER = "cell_xpos_updater";
  public static final String CELL_YPOS_UPDATER = "cell_ypos_updater";

  public static final String ACTION_REBOOT = "reboot";
  public static final String ACTION_RESET = "reset";
  public static final String ACTION_RIGHT = "right";
  public static final String ACTION_SCAN = "scan";
  public static final String ACTION_MACRO = "macro";
  public static final String ACTION_SWITCH_MATRIX = "switch.matrix";

  public static boolean nodeAtLeft(VisualEditNode node) {
    return node.isTx() || !TX_AT_LEFT;
  }
}
