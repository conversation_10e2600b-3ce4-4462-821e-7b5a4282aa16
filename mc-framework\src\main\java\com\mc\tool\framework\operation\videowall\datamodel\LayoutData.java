package com.mc.tool.framework.operation.videowall.datamodel;

import com.google.gson.annotations.Expose;
import com.mc.common.beans.SimpleObservable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.IntegerProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.beans.property.SimpleIntegerProperty;
import javafx.geometry.Rectangle2D;
import javafx.util.Pair;
import lombok.Getter;

/**
 * 布局数据.
 */
public class LayoutData implements IVideoWallLayout {

  /**
   * 重叠后最小的剩余的宽度.
   */
  public static final int MIN_OVERLAP_REMAIN = 10;

  /**
   * 视频墙的屏幕的行数.
   */
  @Expose
  private IntegerProperty rows;

  /**
   * 视频墙的屏幕的列数.
   */
  @Expose
  private IntegerProperty columns;

  /**
   * 屏幕输出分辨率的高度.
   */
  @Getter
  @Expose
  private IntegerProperty resHeight;

  /**
   * 屏幕输出分辨率的宽度.
   */
  @Getter
  @Expose
  private IntegerProperty resWidth;

  /**
   * 屏幕输出的帧率.
   */
  @Getter
  @Expose
  private IntegerProperty fps;

  @Expose
  private List<Integer> heights;

  @Expose
  private List<Integer> widths;

  @Getter
  @Expose
  private BooleanProperty multiRes;

  @Expose
  List<Integer> horzMargins;

  @Expose
  List<Integer> vertMargins;

  @Getter
  private SimpleObservable multiResObservable = new SimpleObservable();

  @Getter
  @Expose
  private IntegerProperty compensationScaleThreshold = new SimpleIntegerProperty();

  @Getter
  @Expose
  private IntegerProperty leftCompensation = new SimpleIntegerProperty();

  @Getter
  @Expose
  private IntegerProperty rightCompensation = new SimpleIntegerProperty();

  @Getter
  @Expose
  private IntegerProperty topCompensation = new SimpleIntegerProperty();

  @Getter
  @Expose
  private IntegerProperty bottomCompensation = new SimpleIntegerProperty();

  /**
   * Constructor.
   */
  public LayoutData() {
    rows = new SimpleIntegerProperty(2);
    columns = new SimpleIntegerProperty(2);
    resHeight = new SimpleIntegerProperty(1080);
    resWidth = new SimpleIntegerProperty(1920);
    fps = new SimpleIntegerProperty(30);
    heights = new ArrayList<>();
    widths = new ArrayList<>();
    horzMargins = new ArrayList<>();
    vertMargins = new ArrayList<>();
    multiRes = new SimpleBooleanProperty(false);
  }

  @Override
  public int getRows() {
    return rows.get();
  }

  @Override
  public int getColumns() {
    return columns.get();
  }

  @Override
  public IntegerProperty getRowsProperty() {
    return rows;
  }

  @Override
  public IntegerProperty getColumnsProperty() {
    return columns;
  }

  /**
   * 复制数据.
   *
   * @param layoutData 要复制到的布局.
   */
  public void copyTo(LayoutData layoutData) {
    layoutData.rows.set(rows.get());
    layoutData.columns.set(columns.get());
    layoutData.resHeight.set(resHeight.get());
    layoutData.resWidth.set(resWidth.get());
    layoutData.fps.set(fps.get());
    layoutData.heights.clear();
    layoutData.heights.addAll(heights);
    layoutData.widths.clear();
    layoutData.widths.addAll(widths);
    layoutData.multiRes.set(multiRes.get());

    layoutData.compensationScaleThreshold.set(compensationScaleThreshold.get());
    layoutData.leftCompensation.set(leftCompensation.get());
    layoutData.rightCompensation.set(rightCompensation.get());
    layoutData.topCompensation.set(topCompensation.get());
    layoutData.bottomCompensation.set(bottomCompensation.get());
    layoutData.horzMargins.clear();
    layoutData.horzMargins.addAll(horzMargins);
    layoutData.vertMargins.clear();
    layoutData.vertMargins.addAll(vertMargins);
  }

  /**
   * 获取所有列的宽度.
   *
   * @return 列的宽度的集合
   */
  @Override
  public List<Integer> getWidths() {
    if (multiRes.get()) {
      int cols = columns.get();
      if (widths.size() != cols) {
        if (widths.size() < cols) {
          int addcount = cols - widths.size();
          for (int i = 0; i < addcount; i++) {
            widths.add(1920);
          }
        } else {
          widths.subList(cols, widths.size()).clear();
        }
      }
      return Collections.unmodifiableList(widths);
    } else {
      return Collections.nCopies(columns.get(), resWidth.get());
    }
  }

  /**
   * 更新多分辨率宽度.
   *
   * @param widths 宽度
   */
  public void updateMultiResWidths(Collection<Integer> widths) {
    if (multiRes.get()) {
      this.widths.clear();
      this.widths.addAll(widths);
    }
  }

  /**
   * 获取所有行的高度.
   *
   * @return 行的高度的集合
   */
  @Override
  public List<Integer> getHeights() {
    if (multiRes.get()) {
      int rowCount = rows.get();
      if (heights.size() != rowCount) {
        if (heights.size() < rowCount) {
          int addcount = rowCount - heights.size();
          for (int i = 0; i < addcount; i++) {
            heights.add(1080);
          }
        } else {
          heights.subList(rowCount, heights.size()).clear();
        }
      }
      return heights;
    } else {
      return Collections.nCopies(rows.get(), resHeight.get());
    }
  }

  /**
   * 更新多分辨的高度.
   *
   * @param heights 高度
   */
  public void updateMultiResHeights(Collection<Integer> heights) {
    if (multiRes.get()) {
      this.heights.clear();
      this.heights.addAll(heights);
    }
  }

  /**
   * 获取margin的宽度.
   *
   * @return margin的宽度
   */
  public List<Integer> getHorzMargins() {
    int cnt = columns.get() - 1;
    if (cnt < 0) {
      return Collections.emptyList();
    }
    if (multiRes.get()) {
      if (horzMargins.size() < cnt) {
        int addcount = cnt - horzMargins.size();
        for (int i = 0; i < addcount; i++) {
          horzMargins.add(0);
        }
      } else if (horzMargins.size() > cnt) {
        horzMargins.subList(cnt, horzMargins.size()).clear();
      }
      return Collections.unmodifiableList(horzMargins);
    } else {
      return Collections.nCopies(cnt, 0);
    }
  }

  /**
   * 更新margin的宽度.
   *
   * @param horzMargins margin的宽度
   */
  public void updateHorzMargins(Collection<Integer> horzMargins) {
    if (multiRes.get()) {
      this.horzMargins.clear();
      this.horzMargins.addAll(horzMargins);
    }
  }

  /**
   * 获取margin的高度.
   *
   * @return margin的高度
   */
  public List<Integer> getVertMargins() {
    int cnt = rows.get() - 1;
    if (cnt < 0) {
      return Collections.emptyList();
    }
    if (multiRes.get()) {
      if (vertMargins.size() < cnt) {
        int addcount = cnt - vertMargins.size();
        for (int i = 0; i < addcount; i++) {
          vertMargins.add(0);
        }
      } else if (vertMargins.size() > cnt) {
        vertMargins.subList(cnt, vertMargins.size()).clear();
      }
      return Collections.unmodifiableList(vertMargins);
    } else {
      return Collections.nCopies(cnt, 0);
    }
  }

  /**
   * 更新margin的高度.
   *
   * @param vertMargins margin的高度
   */
  public void updateVertMargins(Collection<Integer> vertMargins) {
    if (multiRes.get()) {
      this.vertMargins.clear();
      this.vertMargins.addAll(vertMargins);
    }
  }

  /**
   * 获取整体宽度.
   *
   * @return 宽度
   */
  @Override
  public int getTotalWidth() {
    int result = 0;
    int cnt = columns.get();
    List<Integer> resWidths = getWidths();
    List<Integer> margins = getHorzMargins();
    for (int i = 0; i < cnt; i++) {
      result += resWidths.get(i);
      if (i > 0) {
        result += margins.get(i - 1);
      }
    }
    return Math.max(0, result);
  }

  /**
   * 获取整体高度.
   *
   * @return 高度
   */
  @Override
  public int getTotalHeight() {
    int result = 0;
    int cnt = rows.get();
    List<Integer> resHeights = getHeights();
    List<Integer> margins = getVertMargins();
    for (int i = 0; i < cnt; i++) {
      result += resHeights.get(i);
      if (i > 0) {
        result += margins.get(i - 1);
      }
    }
    return Math.max(0, result);
  }

  /**
   * 获取根据分辨率限制调整后的边距.
   *
   * @return 调整后的边距
   */
  public List<Integer> getAdaptiveVertMargins() {
    List<Integer> vertMargins = getVertMargins();
    List<Pair<Integer, Integer>> ranges = getVertMarginRanges();
    List<Integer> result = new ArrayList<>();
    for (int i = 0; i < getRows() - 1; i++) {
      Pair<Integer, Integer> range = ranges.get(i);
      int margin = Math.min(vertMargins.get(i), range.getValue());
      margin = Math.max(margin, range.getKey());
      result.add(margin);
    }
    return result;
  }

  /**
   * 获取根据分辨率限制调整后的边距.
   *
   * @return 调整后的边距
   */
  public List<Integer> getAdaptiveHorzMargins() {
    List<Integer> horzMargins = getHorzMargins();
    List<Pair<Integer, Integer>> ranges = getHorzMarginRanges();
    List<Integer> result = new ArrayList<>();
    for (int i = 0; i < getColumns() - 1; i++) {
      Pair<Integer, Integer> range = ranges.get(i);
      int margin = Math.min(horzMargins.get(i), range.getValue());
      margin = Math.max(margin, range.getKey());
      result.add(margin);
    }
    return result;
  }

  private List<Pair<Integer, Integer>> getHorzMarginRanges() {
    List<Integer> widths = getWidths();
    List<Pair<Integer, Integer>> result = new ArrayList<>();
    for (int i = 0; i < getColumns() - 1; i++) {
      int minMargin =
          MIN_OVERLAP_REMAIN - Math.min(widths.get(i), widths.get(i + 1));
      int maxMargin = -minMargin;
      result.add(new Pair<>(minMargin, maxMargin));
    }
    return result;
  }

  private List<Pair<Integer, Integer>> getVertMarginRanges() {
    List<Integer> heights = getHeights();
    List<Pair<Integer, Integer>> result = new ArrayList<>();
    for (int i = 0; i < getRows() - 1; i++) {
      int minMargin =
          MIN_OVERLAP_REMAIN - Math.min(heights.get(i), heights.get(i + 1));
      int maxMargin = -minMargin;
      result.add(new Pair<>(minMargin, maxMargin));
    }
    return result;
  }

  @Override
  public Collection<Rectangle2D> getScreenAreas() {
    List<Rectangle2D> areas = new ArrayList<>();
    List<Integer> widths = getWidths();
    List<Integer> heights = getHeights();
    List<Integer> horzMargins = getAdaptiveHorzMargins();
    List<Integer> vertMargins = getAdaptiveVertMargins();
    int ypos = 0;
    for (int j = 0; j < heights.size(); j++) {
      if (j > 0) {
        ypos += heights.get(j - 1);
        ypos += vertMargins.get(j - 1);
      }
      int xpos = 0;
      for (int i = 0; i < widths.size(); i++) {
        if (i > 0) {
          xpos += widths.get(i - 1);
          xpos += horzMargins.get(i - 1);
        }
        areas.add(new Rectangle2D(xpos, ypos, widths.get(i), heights.get(j)));
      }
    }
    return areas;
  }
}