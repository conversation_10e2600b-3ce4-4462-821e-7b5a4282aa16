package org.controlsfx.control;

import javafx.beans.property.ObjectProperty;

public class BooleanPropertyItem extends ObjectPropertyItem<Boolean> {

  public BooleanPropertyItem(ObjectProperty<Boolean> value, Class<Boolean> clazz) {
    super(value, clazz);
  }

  @Override
  public void setValue(Object value) {
    if (clazz.isInstance(value)) {
      this.value.set(clazz.cast(value));
    }
  }
}
