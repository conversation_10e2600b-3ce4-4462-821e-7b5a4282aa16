package com.mc.graph.interfaces;

import com.mc.graph.util.SelectableNode;
import javafx.beans.property.BooleanProperty;
import javafx.scene.layout.BorderStroke;
import javafx.scene.layout.Region;
import javafx.scene.paint.Color;

import java.util.Collection;

public interface CellSkin extends SelectableNode, Skin {
  Region getRegion();
  
  void setCell(CellObject cell);
  
  CellObject getCell();
  
  void addCellBehavior(CellBehavior cellBehavior);
  
  Collection<ConnectorSkin> getConnectorSkins();
  
  Color getSelectionBorderColor();
  
  boolean isSelectionEffectEnabled();
  
  double getResizableBorderWidth();
  
  boolean isMovable();
  
  BooleanProperty movableProperty();
  
  boolean isResizeble();
  
  boolean isRotatable();
  
  BooleanProperty rotatableProperty();
  
  BorderStroke getSelectedBorderStroke();
}
