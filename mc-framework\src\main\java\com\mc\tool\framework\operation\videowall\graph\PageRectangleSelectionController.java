package com.mc.tool.framework.operation.videowall.graph;

import com.mc.graph.canvas.PageCanvas;
import com.mc.graph.util.EventHandlerGroup;
import java.util.Collections;
import java.util.function.Consumer;
import javafx.event.EventHandler;
import javafx.geometry.Rectangle2D;
import javafx.scene.Parent;
import javafx.scene.input.MouseButton;
import javafx.scene.input.MouseEvent;
import lombok.Setter;

/**
 * .
 */
public class PageRectangleSelectionController {
  private final PageCanvas canvas;
  private double firstX;
  private double firstY;
  private double secondX;
  private double secondY;
  private EventHandler<MouseEvent> mouseDraggedEventHandler;
  private EventHandler<MouseEvent> mousePressedHandler;
  private EventHandler<MouseEvent> mouseReleasedHandler;

  private boolean enable = true;

  @Setter
  private Consumer<Rectangle2D> selectionConsumer = null;

  public PageRectangleSelectionController(PageCanvas canvas) {
    this.canvas = canvas;
  }

  public void setEnable(boolean enable) {
    this.enable = enable;
  }

  /**
   * 使框选生效.
   *
   * @param draggedEvtHandler  dragged handler
   * @param pressedEvtHandler  pressed handler
   * @param releasedEvtHandler released handler
   */
  public void apply(EventHandlerGroup<MouseEvent> draggedEvtHandler,
                    EventHandlerGroup<MouseEvent> pressedEvtHandler,
                    EventHandlerGroup<MouseEvent> releasedEvtHandler) {
    init(canvas.getContainer());
    draggedEvtHandler.addHandler(mouseDraggedEventHandler);
    pressedEvtHandler.addHandler(mousePressedHandler);
    releasedEvtHandler.addHandler(mouseReleasedHandler);
  }

  private void init(final Parent root) {

    mouseDraggedEventHandler = new EventHandler<MouseEvent>() {
      @Override
      public void handle(MouseEvent event) {
        if (!enable) {
          return;
        }
        if (event.getButton() != MouseButton.SECONDARY) {
          return;
        }
        performDrag(root, event);
        event.consume();
      }
    };

    mousePressedHandler = new EventHandler<MouseEvent>() {
      @Override
      public void handle(MouseEvent event) {
        if (!enable) {
          return;
        }
        if (event.getButton() != MouseButton.SECONDARY) {
          return;
        }
        performDragBegin(event);
        event.consume();
      }
    };

    mouseReleasedHandler = new EventHandler<MouseEvent>() {
      @Override
      public void handle(MouseEvent event) {
        if (!enable) {
          return;
        }
        if (event.getButton() != MouseButton.SECONDARY) {
          return;
        }
        performDragEnd(root, event);
        event.consume();
      }
    };
  }

  private void performDrag(Parent root, MouseEvent event) {

    Rectangle2D rect = createSelectionRect(root, event);
    canvas.setSelectedAreas(canvas.getIntersectedPageOriginAreas(rect));
  }

  private Rectangle2D createSelectionRect(Parent root, MouseEvent event) {
    final double parentScaleX = root.localToSceneTransformProperty().getValue().getMxx();
    final double parentScaleY = root.localToSceneTransformProperty().getValue().getMyy();

    final double translateX = -root.localToSceneTransformProperty().getValue().getTx();
    final double translateY = -root.localToSceneTransformProperty().getValue().getTy();

    secondX = event.getSceneX();
    secondY = event.getSceneY();

    firstX = Math.max(firstX, 0);
    firstY = Math.max(firstY, 0);

    secondX = Math.max(secondX, 0);
    secondY = Math.max(secondY, 0);

    double xpos = Math.min(firstX, secondX);
    double ypos = Math.min(firstY, secondY);

    double width = Math.abs(secondX - firstX);
    double height = Math.abs(secondY - firstY);

    double rawXpos = xpos / parentScaleX + translateX / parentScaleX;
    double rawYpos = ypos / parentScaleY + translateY / parentScaleY;
    double rawWidth = width / parentScaleX;
    double rawHeight = height / parentScaleX;

    Rectangle2D rect = new Rectangle2D(rawXpos, rawYpos, rawWidth, rawHeight);
    return rect;
  }

  private void performDragBegin(MouseEvent event) {

    // record the current mouse X and Y position on Node
    firstX = event.getSceneX();
    firstY = event.getSceneY();

  }

  private void performDragEnd(Parent root, MouseEvent event) {
    canvas.setSelectedAreas(Collections.emptyList());
    if (selectionConsumer != null) {
      selectionConsumer.accept(
          canvas.getMergedIntersectedPageOriginArea(createSelectionRect(root, event)));
    }
  }

}