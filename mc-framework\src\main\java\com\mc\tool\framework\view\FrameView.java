package com.mc.tool.framework.view;

import com.google.inject.Guice;
import com.google.inject.Injector;
import com.google.inject.Module;
import com.google.inject.util.Modules;
import com.mc.common.util.PlatformUtility;
import com.mc.tool.framework.DefaultModule;
import com.mc.tool.framework.controller.FrameController;
import com.mc.tool.framework.interfaces.ApplicationBase;
import com.mc.tool.framework.interfaces.Entity;
import com.mc.tool.framework.utility.I18nUtility;
import com.mc.tool.framework.utility.InjectorProvider;
import com.mc.tool.framework.utility.TypeWrapper;
import com.mc.tool.framework.utility.UndecoratedAlert;
import com.sun.jna.Pointer;
import com.sun.jna.platform.win32.User32;
import com.sun.jna.platform.win32.WinDef;
import com.sun.jna.platform.win32.WinUser;
import com.sun.management.HotSpotDiagnosticMXBean;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import insidefx.undecorator.Undecorator;
import insidefx.undecorator.UndecoratorScene;
import java.io.InputStream;
import java.lang.management.ManagementFactory;
import java.lang.ref.WeakReference;
import java.net.URL;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Locale;
import java.util.ResourceBundle;
import javafx.application.Application;
import javafx.application.Platform;
import javafx.beans.binding.Bindings;
import javafx.beans.value.ChangeListener;
import javafx.beans.value.ObservableValue;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.scene.Scene;
import javafx.scene.control.AlertEx.AlertExType;
import javafx.scene.control.Menu;
import javafx.scene.control.MenuBar;
import javafx.scene.control.MenuItem;
import javafx.scene.image.Image;
import javafx.scene.layout.Region;
import javafx.scene.text.Font;
import javafx.stage.Modality;
import javafx.stage.Stage;
import javafx.stage.StageStyle;
import javafx.util.Pair;
import javax.management.MBeanServer;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.bridge.SLF4JBridgeHandler;

/**
 * .
 */
@SuppressFBWarnings("SIC_INNER_SHOULD_BE_STATIC_ANON")
@Slf4j
public class FrameView extends Application {
  private static Module module;

  static {
    SLF4JBridgeHandler.removeHandlersForRootLogger();
    SLF4JBridgeHandler.install();
    InputStream is = Thread.currentThread().getContextClassLoader()
        .getResourceAsStream("fonts/SourceHanSansSC-Regular.otf");
    Font.loadFont(is, 12);

    MBeanServer server = ManagementFactory.getPlatformMBeanServer();
    try {
      HotSpotDiagnosticMXBean mxBean = ManagementFactory.newPlatformMXBeanProxy(server,
          "com.sun.management:type=HotSpotDiagnostic", HotSpotDiagnosticMXBean.class);
      mxBean.setVMOption("HeapDumpOnOutOfMemoryError", "true");
    } catch (Exception exception) {
      log.warn("Fail to get mxbean!", exception);
    }

  }

  public static void setModule(Module module) {
    FrameView.module = module;
  }

  private static void handleUnexpectedError(Thread thread, Throwable exception) {
    log.error("", exception);
    final boolean serious = exception instanceof OutOfMemoryError;
    PlatformUtility.runInFxThread(() -> {
      UndecoratedAlert alert = new UndecoratedAlert(AlertExType.ERROR);
      if (InjectorProvider.getInjector() != null) {
        ApplicationBase applicationBase = InjectorProvider.getInjector()
            .getInstance(ApplicationBase.class);
        if (applicationBase != null) {
          alert.initOwner(applicationBase.getMainWindow());
        }
      }
      alert.initModality(Modality.WINDOW_MODAL);
      String seriousMessage = "";
      if (serious) {
        seriousMessage = "Application must be shutdowned for a serious error! ";
      }
      alert.setContentText(seriousMessage + thread.getName() + ":" + exception.getMessage());
      alert.showAndWait();
      if (serious) {
        Platform.exit();
      }
    });

    assert false;
  }

  @Override
  public void start(Stage primaryStage) throws Exception {
    Thread.setDefaultUncaughtExceptionHandler(FrameView::handleUnexpectedError);

    Module module = getGuiceModule();
    if (FrameView.module != null) {
      module = Modules.override(module).with(FrameView.module);
    }
    final Injector injector = Guice.createInjector(module);
    InjectorProvider.setInjector(injector);
    final ApplicationBase applicationBase = injector.getInstance(ApplicationBase.class);
    log.info("VPM started. Name : {}. Version : {}", applicationBase.getAppTitle(),
        applicationBase.getFullVersion());

    applicationBase.setMainWindow(primaryStage);
    ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
    FXMLLoader loader = new FXMLLoader(classLoader.getResource("com/mc/tool/framework/main.fxml"),
        I18nUtility.getI18nBundle("main"));
    FrameController controller = injector.getInstance(FrameController.class);
    loader.setController(controller);
    Region root = (Region) loader.load();

    final UndecoratorScene undecoratorScene = new McUndecoratorScene(primaryStage,
        StageStyle.DECORATED, root, "/com/mc/tool/framework/stagedecoration.fxml",
        I18nUtility.getI18nBundle("main"), controller);

    initScene(undecoratorScene);
    primaryStage.setScene(undecoratorScene);
    primaryStage.sizeToScene();
    primaryStage.toFront();
    primaryStage.setMinWidth(1315);
    primaryStage.setMinHeight(720);

    Undecorator undecorator = undecoratorScene.getUndecorator();
    undecorator.setMaximizeBorder(56);
    undecorator.getStylesheets()
        .add(classLoader.getResource("com/mc/tool/framework/main.css").toExternalForm());
    undecorator.getStylesheets()
        .add(classLoader.getResource("com/mc/tool/framework/stagedecoration.css").toExternalForm());
    for (String style : applicationBase.getAppAdditionalStyleSheet()) {
      undecorator.getStylesheets().add(style);
    }


    primaryStage.setTitle(applicationBase.getAppTitle());
    Image icon = applicationBase.getIcon();
    if (icon != null) {
      primaryStage.getIcons().add(icon);
    }
    primaryStage.setOnHidden(e -> {
      injector.getInstance(ApplicationBase.class).exit();
      controller.onDestroy();
    });
    primaryStage.show();

    // 解决windows下不能点击任务栏图标最小化软件的问题
    if (System.getProperty("os.name").toLowerCase(Locale.ENGLISH).contains("windows")) {
      long lhwnd = com.sun.glass.ui.Window.getWindows().get(0).getNativeWindow();
      Pointer lpVoid = Pointer.createConstant(lhwnd);
      WinDef.HWND hwnd = new WinDef.HWND(lpVoid);
      final User32 user32 = User32.INSTANCE;
      int oldStyle = user32.GetWindowLong(hwnd, WinUser.GWL_STYLE);
      int newStyle = oldStyle | 0x00020000; // WS_MINIMIZEBOX
      user32.SetWindowLong(hwnd, WinUser.GWL_STYLE, newStyle);
    }
  }

  public Module getGuiceModule() {
    return new DefaultModule();
  }

  protected void initScene(Scene scene) {
  }

  static class McUndecoratorScene extends UndecoratorScene {
    public McUndecoratorScene(Stage stage, StageStyle stageStyle, Region root,
                              String stageDecorationFxml, ResourceBundle stageResource, FrameController controller) {
      super(stage, stageStyle, root, stageDecorationFxml, stageResource);

      McUndecorator undecorator = (McUndecorator) this.getUndecorator();
      undecorator.setController(controller);
    }

    @Override
    protected Undecorator createUndecorator(Stage stage, Region root, String stageDecorationFxml,
                                            StageStyle stageStyle, ResourceBundle stageResource) {
      return new McUndecorator(stage, root, stageDecorationFxml, stageStyle, stageResource);
    }
  }

  static class McUndecorator extends Undecorator implements Initializable {
    @FXML
    private Menu exportMenu;
    @FXML
    private MenuBar menuBar;

    private WeakReference<FrameController> controllerRef = new WeakReference<>(null);

    private List<Menu> currentMenus = new ArrayList<>();

    public McUndecorator(Stage stag, Region clientArea, String stageDecorationFxml, StageStyle st,
                         ResourceBundle resource) {
      super(stag, clientArea, stageDecorationFxml, st, resource);
    }

    public void setController(FrameController controller) {
      this.controllerRef = new WeakReference<>(controller);
      // 可调用此函数界面已初始化完毕

      updateExportItems();
      controller.currentEntityProperty().addListener(new ChangeListener<Entity>() {

        @Override
        public void changed(ObservableValue<? extends Entity> observable, Entity oldValue,
                            Entity newValue) {
          updateExportItems();
          updateMenus();
        }
      });
    }

    private void updateMenus() {
      menuBar.getMenus().removeAll(currentMenus);
      currentMenus.clear();
      FrameController controller = controllerRef.get();
      if (controller == null) {
        return;
      }
      Entity entity = controller.getCurrentEntity();
      if (entity == null) {
        return;
      }

      for (Pair<String, Collection<TypeWrapper>> pair : entity.getMenuGroup()) {
        Menu menu = new Menu(pair.getKey());
        for (TypeWrapper typeWrapper : pair.getValue()) {
          MenuItem item = new MenuItem(typeWrapper.getName());
          item.setOnAction((event) -> entity.onMenu(typeWrapper));
          menu.getItems().add(item);
        }
        currentMenus.add(menu);
      }

      menuBar.getMenus().addAll(1, currentMenus);
    }

    private void updateExportItems() {
      exportMenu.getItems().clear();
      FrameController controller = controllerRef.get();
      if (controller == null) {
        return;
      }
      Entity entity = controller.getCurrentEntity();
      if (entity == null) {
        return;
      }

      for (TypeWrapper type : entity.getExportableTypes()) {
        MenuItem item = new MenuItem(type.getName());
        item.setOnAction((event) -> entity.exports(type.getType(), null));
        exportMenu.getItems().add(item);
      }
    }

    @FXML
    protected void onConfig() {
      FrameController controller = controllerRef.get();
      if (controller != null) {
        controller.onConfig();
      }
    }

    @FXML
    protected void onAbout() {
      FrameController controller = controllerRef.get();
      if (controller != null) {
        controller.onAbout();
      }
    }

    @FXML
    protected void onClose() {
      FrameController controller = controllerRef.get();
      if (controller != null) {
        controller.onClose();
      }
    }

    @Override
    public void initialize(URL location, ResourceBundle resources) {
      exportMenu.visibleProperty().bind(Bindings
          .createBooleanBinding(() -> !exportMenu.getItems().isEmpty(), exportMenu.getItems()));
    }
  }
}
