package javafx.scene.control.cell;

import com.sun.javafx.scene.control.skin.TextFieldSkin;
import javafx.scene.Node;
import javafx.scene.control.Cell;
import javafx.scene.control.PasswordField;
import javafx.scene.control.TextField;
import javafx.scene.input.KeyCode;
import javafx.scene.layout.HBox;
import javafx.util.StringConverter;

public class CellUtilsEx {
  static <T> void updateItem(final Cell<T> cell, final StringConverter<T> converter,
      final HBox hbox, final Node graphic, final TextField textField) {
    if (cell.isEmpty()) {
      cell.setText(null);
      cell.setGraphic(null);
    } else {
      if (cell.isEditing()) {
        if (textField != null) {
          textField.setText(getItemText(cell, converter));
        }
        cell.setText(null);

        if (graphic != null) {
          hbox.getChildren().setAll(graphic, textField);
          cell.setGraphic(hbox);
        } else {
          cell.setGraphic(textField);
        }
      } else {
        cell.setText(maskText(getItemText(cell, converter)));
        cell.setGraphic(graphic);
      }
    }
  }
  
  static String maskText(String text) {
    int length = text.length();
    StringBuilder stringBuilder = new StringBuilder();
    for (int i = 0; i < length; i++) {
      stringBuilder.append(TextFieldSkin.BULLET);
    }
    return stringBuilder.toString();
  }

  static <T> void startEdit(final Cell<T> cell, final StringConverter<T> converter, final HBox hbox,
      final Node graphic, final TextField textField) {
    if (textField != null) {
      textField.setText(getItemText(cell, converter));
    }
    cell.setText(null);

    if (graphic != null) {
      hbox.getChildren().setAll(graphic, textField);
      cell.setGraphic(hbox);
    } else {
      cell.setGraphic(textField);
    }

    if (textField != null) {
      textField.selectAll();

      // requesting focus so that key input can immediately go into the
      // TextField (see RT-28132)
      textField.requestFocus();
    }
  }

  static <T> void cancelEdit(Cell<T> cell, final StringConverter<T> converter, Node graphic) {
    cell.setText(getItemText(cell, converter));
    cell.setGraphic(graphic);
  }

  private static <T> String getItemText(Cell<T> cell, StringConverter<T> converter) {
    return converter == null ? cell.getItem() == null ? "" : cell.getItem().toString()
        : converter.toString(cell.getItem());
  }

  static <T> PasswordField createPasswordField(final Cell<T> cell,
      final StringConverter<T> converter) {
    final PasswordField textField = new PasswordField();
    textField.setText(getItemText(cell, converter));

    // Use onAction here rather than onKeyReleased (with check for Enter),
    // as otherwise we encounter RT-34685
    textField.setOnAction(event -> {
      if (converter == null) {
        throw new IllegalStateException(
            "Attempting to convert text input into Object, but provided "
                + "StringConverter is null. Be sure to set a StringConverter "
                + "in your cell factory.");
      }
      cell.commitEdit(converter.fromString(textField.getText()));
      event.consume();
    });
    textField.setOnKeyReleased(t -> {
      if (t.getCode() == KeyCode.ESCAPE) {
        cell.cancelEdit();
        t.consume();
      }
    });
    return textField;
  }
}
