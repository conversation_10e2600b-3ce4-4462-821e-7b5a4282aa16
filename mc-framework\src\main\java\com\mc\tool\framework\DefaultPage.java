package com.mc.tool.framework;

import com.mc.tool.framework.interfaces.ApplicationBase;
import com.mc.tool.framework.interfaces.Entity;
import com.mc.tool.framework.interfaces.Page;
import com.mc.tool.framework.utility.InjectorProvider;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.concurrent.Task;
import javafx.scene.control.Button;
import javafx.scene.control.Label;
import javafx.scene.layout.HBox;
import javafx.scene.layout.Pane;

/**
 * .
 */
@SuppressFBWarnings("SIC_INNER_SHOULD_BE_STATIC_ANON")
public class DefaultPage implements Page {

  private String name;
  private String content;
  private HBox view;
  private SimpleBooleanProperty isVisible = new SimpleBooleanProperty(true);
  private Entity entity;

  /**
   * Construct the default page.
   *
   * @param entity  parent entity.
   * @param name    page name.
   * @param content page's content.
   */
  public DefaultPage(Entity entity, String name, String content) {
    this.entity = entity;
    this.name = name;
    this.content = content;
  }

  @Override
  public String getTitle() {
    return name;
  }

  @Override
  public Pane getView() {
    if (view == null) {
      view = new HBox();
      view.getChildren().add(new Label(content));

      Button btn = new Button("RunTask");
      view.getChildren().add(btn);
      btn.setOnAction(e -> {
        Task<Integer> task = new Task<Integer>() {

          @Override
          protected Integer call() throws Exception {
            for (int i = 0; i < 100; i++) {
              Thread.sleep(60);
              updateMessage("running at " + i);
              updateProgress(i, 99);
            }
            return 0;
          }
        };
        ApplicationBase applicationBase =
            InjectorProvider.getInjector().getInstance(ApplicationBase.class);
        applicationBase.getTaskManager().addBackgroundTask(task);
      });

      Button btn2 = new Button("Run Task Foreground");
      view.getChildren().add(btn2);
      btn2.setOnAction(e -> {
        Task<Integer> task = new Task<Integer>() {

          @Override
          protected Integer call() throws Exception {
            for (int i = 0; i < 100; i++) {
              Thread.sleep(60);
              updateMessage("running at " + i);
              updateProgress(i, 99);
            }
            return 0;
          }
        };
        ApplicationBase applicationBase =
            InjectorProvider.getInjector().getInstance(ApplicationBase.class);
        applicationBase.getTaskManager().addForegroundTask(task);
      });

      Button btn3 = new Button("Run Task Foreground No progress");
      view.getChildren().add(btn3);
      btn3.setOnAction(e -> {
        Task<Integer> task = new Task<Integer>() {

          @Override
          protected Integer call() throws Exception {
            for (int i = 0; i < 100; i++) {
              Thread.sleep(60);
              updateMessage("running at " + i);
            }
            return 0;
          }
        };
        ApplicationBase applicationBase =
            InjectorProvider.getInjector().getInstance(ApplicationBase.class);
        applicationBase.getTaskManager().addForegroundTask(task);
      });

      Button btn4 = new Button("Switch to hello");
      view.getChildren().add(btn4);
      btn4.setOnAction(e -> {
        ApplicationBase applicationBase =
            InjectorProvider.getInjector().getInstance(ApplicationBase.class);
        applicationBase.getViewManager().switchToPage(entity, "hello", null);
      });

      Button btn5 = new Button("Switch to world");
      view.getChildren().add(btn5);
      btn5.setOnAction(e -> {
        ApplicationBase applicationBase =
            InjectorProvider.getInjector().getInstance(ApplicationBase.class);
        applicationBase.getViewManager().switchToPage(entity, "world", null);
      });
    }
    return view;
  }

  @Override
  public BooleanProperty getVisibleProperty() {
    return isVisible;
  }

  @Override
  public String getName() {
    return name;
  }

  @Override
  public void showObject(Object object) {

  }

  @Override
  public String getStyleClass() {
    // TODO Auto-generated method stub
    return null;
  }

}
