package com.dooapp.fxform.model.impl;

import com.dooapp.fxform.model.PropertyElement;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.Property;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.beans.value.ObservableValue;

/**
 * A wrapper for {@link PropertyElement} which buffers the element value instead of writing it
 * directly back to the source bean.
 *
 * <AUTHOR> (<EMAIL>)
 */
public class BufferedPropertyElement<W> extends BufferedElement<W>
    implements PropertyElement<W> {

  private final PropertyElement<W> element;
  private BooleanProperty isChangeProperty = new SimpleBooleanProperty(false);
  private Boolean isCanReload = true;

  /**
   * Wraps the given element to create a buffered version of it.
   *
   * @param element element to buffer
   * @param bufferUserInput if true changes in buffered value are only written to the bean when
   *        {@link #commit()} is called
   * @param bufferBeanChanges if true changes in the bean property are only applied to the buffered
   *        value when {@link #reload()} is called
   */
  public BufferedPropertyElement(PropertyElement<W> element, boolean bufferUserInput,
      boolean bufferBeanChanges) {
    super(element, bufferBeanChanges);
    this.element = element;

    if (!bufferUserInput) {
      bufferedValue.addListener((observable, oldValue, newValue) -> element.setValue(newValue));
    }
    bufferedValue.addListener((observable, oldValue, newValue) -> {
      if (newValue != null) {
        if (element.getValue() != null && element.getValue().equals(bufferedValue.getValue())) {
          isChangeProperty.set(false);
        } else {
          isChangeProperty.set(true);
        }
      }

    });
    element.addListener((observable, oldValue, newValue) -> {
      if (newValue != null) {
        if (element.getValue().equals(bufferedValue.getValue())) {
          isChangeProperty.set(false);
        } else {
          isChangeProperty.set(true);
        }
      }

    });
  }

  @Override
  public void bind(ObservableValue<? extends W> observable) {
    bufferedValue.bind(observable);
  }

  @Override
  public void unbind() {
    bufferedValue.unbind();
  }

  @Override
  public boolean isBound() {
    return bufferedValue.isBound();
  }

  @Override
  public void bindBidirectional(Property<W> other) {
    bufferedValue.bindBidirectional(other);
  }

  @Override
  public void unbindBidirectional(Property<W> other) {
    bufferedValue.unbindBidirectional(other);
  }

  @Override
  public void setValue(W value) {
    isCanReload = false;
    bufferedValue.setValue(value);
  }

  /**
   * Writes the buffered value to the source bean.
   */
  public void commit() {
    isCanReload = true;
    element.setValue(bufferedValue.getValue());
  }

  public BooleanProperty isChange() {
    return isChangeProperty;
  }
  
  
  /**
   * 设备数据更新时刷新.
   */
  public void refresh() {
    if (isCanReload) {
      bufferedValue.setValue(element.getValue());
    }
  }

}
