package com.mc.tool.framework.systemedit.menu;

import com.mc.tool.framework.systemedit.controller.SystemEditControllable;
import com.mc.tool.framework.systemedit.datamodel.TargetDeviceType;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import com.mc.tool.framework.utility.I18nUtility;
import java.util.Collection;
import javafx.scene.control.Menu;
import javafx.scene.control.MenuItem;

/**
 * .
 */
public class MenuDeviceType extends Menu {
  Collection<VisualEditTerminal> terminals = null;

  /**
   * Constructor.
   *
   * @param controllable controllable
   */
  public MenuDeviceType(SystemEditControllable controllable) {
    setText(I18nUtility.getI18nBundle("systemedit").getString("menu.device_type"));
    Collection<VisualEditTerminal> terminals =
        MenuUtility.getSpecifiedSelectedItems(controllable, VisualEditTerminal.class, false);

    boolean isRx = true;
    boolean isTx = true;
    boolean isDeviceTypeChangable = true;
    for (VisualEditTerminal terminal : terminals) {
      isRx &= terminal.isRx();
      isTx &= terminal.isTx();
      isDeviceTypeChangable &= !controllable.isFixDeviceType(terminal);
    }

    if (isRx ^ isTx && isDeviceTypeChangable) {
      this.terminals = terminals;
      for (TargetDeviceType type : TargetDeviceType.values()) {
        if (type.getName().isEmpty()) {
          continue;
        }
        if (type.isRx() && isRx || type.isTx() && isTx) {
          getItems().add(new MenuDeviceTypeItem(type));
        }
      }
    } else {
      setDisable(true);
    }
  }


  class MenuDeviceTypeItem extends MenuItem {
    private TargetDeviceType targetDeviceType;

    public MenuDeviceTypeItem(TargetDeviceType type) {
      setText(type.getName());
      this.targetDeviceType = type;
      setOnAction((event) -> {
        onAction();
      });
    }

    private void onAction() {
      for (VisualEditTerminal terminal : terminals) {
        terminal.setTargetDeviceType(targetDeviceType);
      }
    }
  }

}
