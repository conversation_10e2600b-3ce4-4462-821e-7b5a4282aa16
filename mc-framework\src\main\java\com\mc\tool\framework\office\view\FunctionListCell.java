package com.mc.tool.framework.office.view;

import com.mc.tool.framework.systemedit.datamodel.SystemEditDefinition;
import com.mc.tool.framework.systemedit.datamodel.VisualEditFunc;
import java.io.IOException;
import java.net.URL;
import java.util.ResourceBundle;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.scene.Node;
import javafx.scene.control.Label;
import javafx.scene.control.ListCell;
import javafx.scene.image.Image;
import javafx.scene.input.ClipboardContent;
import javafx.scene.input.Dragboard;
import javafx.scene.input.MouseEvent;
import javafx.scene.input.TransferMode;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class FunctionListCell extends ListCell<VisualEditFunc> implements Initializable {
  @FXML
  private Label nameText;
  private Node root;

  /**
   * Constructor.
   */
  public FunctionListCell() {
    ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
    FXMLLoader loader = new FXMLLoader(
        classLoader.getResource("com/mc/tool/framework/office/office_function_list_cell.fxml"));
    loader.setController(this);
    try {
      root = loader.load();
    } catch (IOException exception) {
      log.warn("Fail to load office_function_list_cell.fxml", exception);
    }
  }

  @Override
  protected void updateItem(VisualEditFunc item, boolean empty) {
    super.updateItem(item, empty);
    if (empty) {
      setGraphic(null);
      nameText.textProperty().unbind();
    } else {
      setGraphic(root);
      nameText.textProperty().bind(item.nameProperty());
      setOnDragDetected((event) -> onDragDetected(event));
    }
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {

  }

  private void onDragDetected(MouseEvent event) {
    VisualEditFunc btnfunc = getItem();
    Image image = null;
    ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
    if (btnfunc.getNodeType().equals(SystemEditDefinition.VIDEO_WALL_CELL)) {
      image = new Image(classLoader.getResourceAsStream(
          OfficeConstants.VIDEO_WALL_IMAGE.substring(1)));
    } else if (btnfunc.getNodeType().equals(SystemEditDefinition.SEAT_CELL)) {
      image = new Image(classLoader.getResourceAsStream(OfficeConstants.SEAT_IMAGE.substring(1)));
    }
    Dragboard dbDragboard = startDragAndDrop(TransferMode.ANY);
    ClipboardContent content = new ClipboardContent();
    content.putString(btnfunc.getGuid());
    if (image != null) {
      dbDragboard.setDragView(image, image.getWidth() / 2, image.getHeight() / 2);
    }
    dbDragboard.setContent(content);
    event.consume();
  }
}
