/*
 * Copyright (c) 2014, Oracle and/or its affiliates. All rights reserved. ORACLE
 * PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 */

package javafx.scene.control;

import com.sun.javafx.scene.control.skin.resources.ControlResources;
import javafx.application.Platform;
import javafx.beans.NamedArg;
import javafx.geometry.Pos;
import javafx.scene.control.ButtonBar.ButtonData;
import javafx.scene.layout.GridPane;
import javafx.scene.layout.Priority;
import javafx.scene.layout.Region;

/**
 * A dialog that shows a text input control to the user.
 *
 * @see DialogEx
 * @since JavaFX 8u40
 */
public class TextInputDialogEx extends DialogEx<String> {

  private final GridPane grid;
  private final Label label;
  private final TextField textField;
  private final String defaultValue;



  /**
   * Creates a new TextInputDialogEx without a default value entered into the dialog
   * {@link TextField}.
   */
  public TextInputDialogEx() {
    this("");
  }

  /**
   * Creates a new TextInputDialogEx with the default value entered into the dialog
   * {@link TextField}.
   */
  public TextInputDialogEx(@NamedArg("defaultValue") String defaultValue) {
    final DialogPaneEx dialogPane = getDialogPane();

    // -- textfield
    this.textField = createTextField(defaultValue);
    this.textField.setMaxWidth(Double.MAX_VALUE);
    GridPane.setHgrow(textField, Priority.ALWAYS);
    GridPane.setFillWidth(textField, true);

    // -- label
    label = DialogPaneEx.createContentLabel(dialogPane.getContentText());
    label.setPrefWidth(Region.USE_COMPUTED_SIZE);
    label.textProperty().bind(dialogPane.contentTextProperty());

    this.defaultValue = defaultValue;

    this.grid = new GridPane();
    this.grid.setHgap(10);
    this.grid.setMaxWidth(Double.MAX_VALUE);
    this.grid.setAlignment(Pos.CENTER_LEFT);

    dialogPane.contentTextProperty().addListener(o -> updateGrid());

    setTitle(ControlResources.getString("Dialog.confirm.title"));
    dialogPane.setHeaderText(ControlResources.getString("Dialog.confirm.header"));
    dialogPane.getStyleClass().add("text-input-dialog");
    dialogPane.getButtonTypes().addAll(ButtonType.OK, ButtonType.CANCEL);

    updateGrid();

    setResultConverter((dialogButton) -> {
      ButtonData data = dialogButton == null ? null : dialogButton.getButtonData();
      return data == ButtonData.OK_DONE ? textField.getText() : null;
    });
  }

  protected TextField createTextField(String initText) {
    return new TextField(initText);
  }


  /**
   * Returns the {@link TextField} used within this dialog.
   */
  public final TextField getEditor() {
    return textField;
  }

  /**
   * Returns the default value that was specified in the constructor.
   */
  public final String getDefaultValue() {
    return defaultValue;
  }

  private void updateGrid() {
    grid.getChildren().clear();

    grid.add(label, 0, 0);
    grid.add(textField, 1, 0);
    getDialogPane().setContent(grid);

    Platform.runLater(() -> textField.requestFocus());
  }
}
