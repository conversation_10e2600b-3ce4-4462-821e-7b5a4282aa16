package com.mc.graph.interfaces;

import javafx.beans.property.DoubleProperty;
import javafx.scene.Node;
import javafx.scene.Parent;
import javafx.scene.paint.Color;

public interface GraphCanvas {
  /**
   * Get the canvas javafx node.
   * @return node instance.
   */
  Node getNode();
  
  /**
   * Get the actual container for cells and links.
   * @return container.
   */
  Parent getContainer();
  
  /**
   * Set the scale of x and y direction.
   * @param scale  scale value
   * @return if set successful, return true.
   */
  boolean setScale(double scale);
  
  void setBgColor(Color color);
  
  Color getBgColor();
  
  /**
   * Get the current scale value
   * @return scale value.
   */
  double getScale();
  
  /**
   * Get the scale property.
   * @return scale property.
   */
  DoubleProperty getScaleProperty();
  
  /**
   * 使内容适配视图.
   */
  void fitToView();
  
}
