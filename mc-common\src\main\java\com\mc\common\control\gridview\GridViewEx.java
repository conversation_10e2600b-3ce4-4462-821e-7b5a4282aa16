package com.mc.common.control.gridview;

import com.sun.javafx.css.converters.EnumConverter;
import impl.org.controlsfx.skin.GridViewSkinEx;
import javafx.beans.property.ObjectProperty;
import javafx.css.CssMetaData;
import javafx.css.StyleConverter;
import javafx.css.Styleable;
import javafx.css.StyleableObjectProperty;
import javafx.css.StyleableProperty;
import javafx.geometry.HPos;
import javafx.scene.control.Control;
import javafx.scene.control.Skin;
import org.controlsfx.control.GridView;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class GridViewEx<T> extends GridView<T> {
  @Override
  protected Skin<?> createDefaultSkin() {
    return new GridViewSkinEx<T>(this);
  }

  // --- horizontal alignment
  private ObjectProperty<HPos> horizontalAlignment;

  /**
   * 获取横向对齐方式属性.
   * 
   * @return 横向对齐方式
   */
  public final ObjectProperty<HPos> horizontalAlignmentProperty() {
    if (horizontalAlignment == null) {
      horizontalAlignment = new StyleableObjectProperty<HPos>(HPos.CENTER) {
        public CssMetaData<GridViewEx<?>, HPos> getCssMetaData() {
          return GridViewEx.StyleableProperties.HORIZONTAL_ALIGNMENT;
        }

        public Object getBean() {
          return GridViewEx.this;
        }

        public String getName() {
          return "horizontalAlignment";
        }
      };

    }
    return horizontalAlignment;
  }

  public final void setHorizontalAlignment(HPos value) {
    horizontalAlignmentProperty().set(value);
  }

  public final HPos getHorizontalAlignment() {
    return horizontalAlignment == null ? HPos.CENTER : horizontalAlignment.get();
  }



  /**
   * Stylesheet handling.
   */

  private static class StyleableProperties {
    private static final CssMetaData<GridViewEx<?>, Number> HORIZONTAL_CELL_SPACING =
        new CssMetaData<GridViewEx<?>, Number>("-fx-horizontal-cell-spacing", //$NON-NLS-1$
            StyleConverter.getSizeConverter(), 12d) {

          @Override
          public Double getInitialValue(GridViewEx<?> node) {
            return node.getHorizontalCellSpacing();
          }

          @Override
          public boolean isSettable(GridViewEx<?> node) {
            return node.horizontalCellSpacingProperty() == null
                || !node.horizontalCellSpacingProperty().isBound();
          }

          @Override
          @SuppressWarnings("unchecked")
          public StyleableProperty<Number> getStyleableProperty(GridViewEx<?> node) {
            return (StyleableProperty<Number>) node.horizontalCellSpacingProperty();
          }
        };

    private static final CssMetaData<GridViewEx<?>, Number> VERTICAL_CELL_SPACING =
        new CssMetaData<GridViewEx<?>, Number>("-fx-vertical-cell-spacing", //$NON-NLS-1$
            StyleConverter.getSizeConverter(), 12d) {

          @Override
          public Double getInitialValue(GridViewEx<?> node) {
            return node.getVerticalCellSpacing();
          }

          @Override
          public boolean isSettable(GridViewEx<?> node) {
            return node.verticalCellSpacingProperty() == null
                || !node.verticalCellSpacingProperty().isBound();
          }

          @Override
          @SuppressWarnings("unchecked")
          public StyleableProperty<Number> getStyleableProperty(GridViewEx<?> node) {
            return (StyleableProperty<Number>) node.verticalCellSpacingProperty();
          }
        };

    private static final CssMetaData<GridViewEx<?>, Number> CELL_WIDTH =
        new CssMetaData<GridViewEx<?>, Number>("-fx-cell-width", 
            StyleConverter.getSizeConverter(), //$NON-NLS-1$
            64d) {

          @Override
          public Double getInitialValue(GridViewEx<?> node) {
            return node.getCellWidth();
          }

          @Override
          public boolean isSettable(GridViewEx<?> node) {
            return node.cellWidthProperty() == null || !node.cellWidthProperty().isBound();
          }

          @Override
          @SuppressWarnings("unchecked")
          public StyleableProperty<Number> getStyleableProperty(GridViewEx<?> node) {
            return (StyleableProperty<Number>) node.cellWidthProperty();
          }
        };

    private static final CssMetaData<GridViewEx<?>, Number> CELL_HEIGHT =
        new CssMetaData<GridViewEx<?>, Number>("-fx-cell-height", 
            StyleConverter.getSizeConverter(), //$NON-NLS-1$
            64d) {

          @Override
          public Double getInitialValue(GridViewEx<?> node) {
            return node.getCellHeight();
          }

          @Override
          public boolean isSettable(GridViewEx<?> node) {
            return node.cellHeightProperty() == null || !node.cellHeightProperty().isBound();
          }

          @Override
          @SuppressWarnings("unchecked")
          public StyleableProperty<Number> getStyleableProperty(GridViewEx<?> node) {
            return (StyleableProperty<Number>) node.cellHeightProperty();
          }
        };

    private static final CssMetaData<GridViewEx<?>, HPos> HORIZONTAL_ALIGNMENT =
        new CssMetaData<GridViewEx<?>, HPos>("-fx-horizontal_alignment",
            new EnumConverter<HPos>(HPos.class), HPos.CENTER) {

          @Override
          public HPos getInitialValue(GridViewEx node) {
            return node.getHorizontalAlignment();
          }

          @Override
          public boolean isSettable(GridViewEx node) {
            return node.horizontalAlignment == null || !node.horizontalAlignment.isBound();
          }

          @Override
          public StyleableProperty<HPos> getStyleableProperty(GridViewEx node) {
            return (StyleableProperty<HPos>) node.horizontalAlignmentProperty();
          }
        };

    private static final List<CssMetaData<? extends Styleable, ?>> STYLEABLES;
    
    static {
      final List<CssMetaData<? extends Styleable, ?>> styleables =
          new ArrayList<CssMetaData<? extends Styleable, ?>>(Control.getClassCssMetaData());
      styleables.add(HORIZONTAL_CELL_SPACING);
      styleables.add(VERTICAL_CELL_SPACING);
      styleables.add(CELL_WIDTH);
      styleables.add(CELL_HEIGHT);
      styleables.add(HORIZONTAL_ALIGNMENT);
      STYLEABLES = Collections.unmodifiableList(styleables);
    }
  }

  /**
   * @return The CssMetaData associated with this class, which may include the CssMetaData of its
   *         super classes.
   */
  public static List<CssMetaData<? extends Styleable, ?>> getClassCssMetaData() {
    return StyleableProperties.STYLEABLES;
  }

  @Override
  public List<CssMetaData<? extends Styleable, ?>> getControlCssMetaData() {
    return getClassCssMetaData();
  }
}
