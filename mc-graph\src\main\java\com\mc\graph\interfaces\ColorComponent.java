package com.mc.graph.interfaces;

import javafx.scene.paint.Color;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.util.Collection;
import java.util.HashSet;
import java.util.Set;

@Slf4j
public class ColorComponent {
  @Setter
  private Color mergedColor = null;

  @Getter
  @Setter
  private String colorComponents = "";

  public Color getMergedColor() {
    return null;
  }

  /**
   * 获取所有的颜色的集合.
   * @return 颜色的集合
   */
  public Collection<Color> getRealColorComponets() {
    Set<Color> colorSet = new HashSet<>();
    String components = colorComponents;
    String[] items = components.split(";");
    for (String item : items) {
      if (item.isEmpty()) {
        continue;
      }
      try {
        Color itemColor = Color.web(item);
        colorSet.add(itemColor);
      } catch (Exception exception) {
        log.warn("Error color : {}", item);
      } 
    }
    return colorSet;
  }
}
