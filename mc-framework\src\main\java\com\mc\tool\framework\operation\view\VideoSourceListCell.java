package com.mc.tool.framework.operation.view;

import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import com.mc.tool.framework.utility.TargetDeviceLogoBinding;
import javafx.scene.control.ListCell;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;
import javafx.scene.input.ClipboardContent;
import javafx.scene.input.Dragboard;
import javafx.scene.input.MouseEvent;
import javafx.scene.input.TransferMode;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class VideoSourceListCell extends ListCell<VisualEditTerminal> {
  @Override
  protected void updateItem(VisualEditTerminal item, boolean empty) {
    super.updateItem(item, empty);
    if (!empty) {
      textProperty().bind(item.nameProperty());
      graphicProperty().bind(new TargetDeviceLogoBinding(item));
      setOnDragDetected((event) -> onDragDetected(event));
    } else {
      graphicProperty().unbind();
      setGraphic(null);
      textProperty().unbind();
      setText(null);
    }
  }

  protected void onDragDetected(MouseEvent event) {
    if (getItem() != null) {
      Dragboard dbDragboard = this.startDragAndDrop(TransferMode.ANY);
      ClipboardContent content = new ClipboardContent();
      content.putString(getItem().getGuid());
      Image image = getDragImage();
      dbDragboard.setDragView(image, image.getWidth() / 2, image.getHeight() / 2);
      dbDragboard.setContent(content);
    } else {
      log.warn("Item is null!");
    }
    event.consume();
  }

  protected Image getDragImage() {
    return ((ImageView) this.getGraphic()).getImage();
  }
}
