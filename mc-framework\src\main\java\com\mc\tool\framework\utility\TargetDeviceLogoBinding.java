package com.mc.tool.framework.utility;

import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import com.mc.tool.framework.systemedit.view.SystemeditConstants;
import javafx.beans.binding.ObjectBinding;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;

/**
 * .
 */
public class TargetDeviceLogoBinding extends ObjectBinding<ImageView> {
  private final VisualEditTerminal terminal;

  public TargetDeviceLogoBinding(VisualEditTerminal terminal) {
    this.terminal = terminal;
    super.bind(terminal.targetDeviceTypeProperty(), terminal.targetDeviceConnectedProperty());
  }


  @Override
  protected ImageView computeValue() {
    String offlineLogo =
        SystemeditConstants.RESOURCE_PATH_SHORT + (terminal.isTargetDeviceConnected() ? getLogo()
            : getLogo().replaceAll("online", "offline"));
    ImageView logo = new ImageView(
        new Image(Thread.currentThread().getContextClassLoader().getResourceAsStream(offlineLogo)));
    return logo;
  }


  protected String getLogo() {
    if (terminal.getTargetDeviceType() == null) {
      return "";
    }
    switch (terminal.getTargetDeviceType()) {
      case DVD:
        return SystemeditConstants.DVD_ONLINE_LOGO;
      case COMPUTER:
        return SystemeditConstants.COMPUTER_ONLINE_LOGO;
      case MONITOR:
        return SystemeditConstants.MONITOR_ONLINE_LOGO;
      case PROJECTOR:
        return SystemeditConstants.PROJECTOR_ONLINE_LOGO;
      default:
        return "";
    } // end switch
  }

}
