package com.mc.tool.framework.operation.videowall.menu;

import com.mc.tool.framework.operation.videowall.controller.VideoWallControllable;
import com.mc.tool.framework.operation.videowall.datamodel.interfaces.VideoObject;
import com.mc.tool.framework.utility.I18nUtility;
import java.util.Collection;

/**
 * .
 */
public class MenuDelete extends VideoWallMenuBase {

  public MenuDelete(VideoWallControllable controllable) {
    super(controllable);
  }

  @Override
  protected void onAction() {
    Collection<VideoObject> videos = controllable.getSelectedVideos();
    controllable.getVideoWallFunction().getVideoWallObject().getVideos().removeAll(videos);
  }

  @Override
  protected String getMenuText() {
    return I18nUtility.getI18nBundle("operation").getString("menu.delete");
  }

}
