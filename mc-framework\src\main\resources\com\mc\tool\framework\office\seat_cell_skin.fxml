<?import javafx.scene.control.Label?>
<?import javafx.scene.Group?>
<?import javafx.scene.layout.Pane?>
<?import javafx.scene.layout.Region?>
<?import javafx.scene.layout.VBox?>
<VBox xmlns="http://javafx.com/javafx/8" xmlns:fx="http://javafx.com/fxml/1" stylesheets="@seat_cell_skin.css"
      styleClass="region" fx:id="region" alignment="CENTER">
  <Pane styleClass="ratote" prefWidth="30" prefHeight="30"
        minWidth="30" minHeight="30" fx:id="ratote"/>
  <Region minHeight="9" maxHeight="9"/>
  <Group>
    <Pane styleClass="function-seat" fx:id="image" minWidth="132" minHeight="44"/>
  </Group>
  <Region minHeight="9" maxHeight="9"/>
  <Label fx:id="nameLabel" styleClass="function-seat-label"
         prefWidth="114" prefHeight="30" minHeight="30"/>
</VBox>