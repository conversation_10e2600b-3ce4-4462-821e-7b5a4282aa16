package com.mc.graph;

import com.mc.common.beans.BackgroundColorStyleBinding;
import com.mc.common.util.WeakAdapter;
import com.mc.graph.interfaces.OverviewableGraphCanvas;
import com.mc.graph.util.NodeUtil;
import javafx.beans.property.DoubleProperty;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleDoubleProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.beans.value.ChangeListener;
import javafx.beans.value.ObservableValue;
import javafx.geometry.Bounds;
import javafx.geometry.Rectangle2D;
import javafx.scene.Group;
import javafx.scene.Node;
import javafx.scene.Parent;
import javafx.scene.control.ScrollPane;
import javafx.scene.control.Skin;
import javafx.scene.layout.Pane;
import javafx.scene.paint.Color;
import javafx.scene.transform.Scale;
import lombok.Getter;

public class McGraphCanvas extends ScrollPane implements OverviewableGraphCanvas {
  public static final double MAX_SCALE = 2;
  public static final double MIN_SCALE = 0.5;
  private static final double MIN_PADDING = 200;
  private Scale scale = new Scale();
  private SimpleDoubleProperty scaleProperty = new SimpleDoubleProperty(1);
  private SimpleDoubleProperty horizontalVisibleProperty = new SimpleDoubleProperty(1);
  private SimpleDoubleProperty verticalVisibleProperty = new SimpleDoubleProperty(1);
  protected ObjectProperty<Color> bgColor = new SimpleObjectProperty<>(Color.WHITE);
  private InnerCanvas innerCanvas;
  
  private WeakAdapter weakAdapter = new WeakAdapter();

  /**
   * Construtor of McGraphCanvas.
   */
  public McGraphCanvas() {
    innerCanvas = createInnerCanvas();
    
    innerCanvas.getZoomContent().getTransforms().add(scale);

    this.setContent(innerCanvas.getContainer());
    /**
     * 不能设置为pannable，不然会与拖拽cell元素的操作冲突
     */
    this.setPannable(false);
    this.setHbarPolicy(ScrollBarPolicy.ALWAYS);
    this.setVbarPolicy(ScrollBarPolicy.ALWAYS);
    this.setStyle("-fx-background-color:transparent;");

    scaleProperty.addListener(weakAdapter.wrap(new ChangeListener<Number>() {

      @Override
      public void changed(ObservableValue<? extends Number> observable, Number oldValue,
          Number newValue) {
        scale.setX(newValue.doubleValue());
        scale.setY(newValue.doubleValue());
        updateBgPane();
      }
    }));

    innerCanvas.getScrollContent().layoutBoundsProperty()
        .addListener(weakAdapter.wrap(new ChangeListener<Bounds>() {

          @Override
          public void changed(ObservableValue<? extends Bounds> observable, Bounds oldValue,
              Bounds newValue) {
            updateBgPane();
          }
        }));

    this.viewportBoundsProperty().addListener(weakAdapter.wrap(new ChangeListener<Bounds>() {

      @Override
      public void changed(ObservableValue<? extends Bounds> observable, Bounds oldValue,
          Bounds newValue) {
        updateBgPane();
      }
    }));

  }

  @Override
  public boolean setScale(double scale) {
    if (scale > MAX_SCALE || scale < MIN_SCALE) {
      return false;
    }

    scaleProperty.set(scale);
    return true;
  }

  @Override
  public Node getNode() {
    return this;
  }

  @Override
  public double getScale() {
    return scaleProperty.get();
  }

  @Override
  public DoubleProperty getScaleProperty() {
    return scaleProperty;
  }

  @Override
  protected Skin<?> createDefaultSkin() {
    McGraphCanvasSkin skin = new McGraphCanvasSkin(this);
    horizontalVisibleProperty
        .bindBidirectional(skin.getHorizontalScrollBar().visibleAmountProperty());
    verticalVisibleProperty.bindBidirectional(skin.getVerticalScrollBar().visibleAmountProperty());
    return skin;
  }
  
  

  public Parent getContainer() {
    return innerCanvas.getZoomContent();
  }

  /**
   * Update the backgournd pane to center cells.
   */
  public void updateBgPane() {
    double containerWidth = this.getLayoutBounds().getWidth() / scale.getX();
    double containerHeight = this.getLayoutBounds().getHeight() / scale.getY();
    if (innerCanvas.getZoomContent().getChildren().size() > 1) {
      double minX = Double.MAX_VALUE;
      double maxX = -Double.MAX_VALUE;
      double minY = Double.MAX_VALUE;
      double maxY = -Double.MAX_VALUE;
      for (Node node : innerCanvas.getZoomContent().getChildren()) {
        if (node.equals(innerCanvas.getBgPane())) {
          continue;
        }
        if (NodeUtil.NODE_IGNORE.equals(node.getUserData())) {
          continue;
        }
        if (!node.isVisible()) {
          continue;
        }
        Bounds bounds = node.getBoundsInParent();
        minX = Math.min(minX, bounds.getMinX());
        maxX = Math.max(maxX, bounds.getMaxX());
        minY = Math.min(minY, bounds.getMinY());
        maxY = Math.max(maxY, bounds.getMaxY());
      }

      double width = maxX - minX + MIN_PADDING * 2;
      double height = maxY - minY + MIN_PADDING * 2;
      double bgWidth = Math.max(containerWidth, width);
      double bgHeight = Math.max(containerHeight, height);
      double bgX = (maxX + minX - bgWidth) / 2;
      double bgY = (maxY + minY - bgHeight) / 2;
      innerCanvas.getBgPane().setLayoutX(bgX);
      innerCanvas.getBgPane().setLayoutY(bgY);
      innerCanvas.getBgPane().setPrefSize(bgWidth, bgHeight);
    } else {
      innerCanvas.getBgPane().setLayoutX(0);
      innerCanvas.getBgPane().setLayoutY(0);
      innerCanvas.getBgPane().setPrefWidth(containerWidth);
      innerCanvas.getBgPane().setPrefHeight(containerHeight);
    }
  }

  @Override
  public DoubleProperty getHorizontalPosProperty() {
    return hvalueProperty();
  }

  @Override
  public DoubleProperty getVerticalPosProperty() {
    return vvalueProperty();
  }

  @Override
  public DoubleProperty getHorizontalVisibleProperty() {
    return horizontalVisibleProperty;
  }

  @Override
  public DoubleProperty getVerticalVisibleProperty() {
    return verticalVisibleProperty;
  }

  @Override
  public OverviewCanvas createOverviewCanvas() {
    InnerCanvas canvas = new InnerCanvas();
    canvas.getBgPane().layoutXProperty().bind(innerCanvas.getBgPane().layoutXProperty());
    canvas.getBgPane().layoutYProperty().bind(innerCanvas.getBgPane().layoutYProperty());
    canvas.getBgPane().prefWidthProperty().bind(innerCanvas.getBgPane().prefWidthProperty());
    canvas.getBgPane().prefHeightProperty().bind(innerCanvas.getBgPane().prefHeightProperty());
    return canvas;
  }
  
  protected InnerCanvas createInnerCanvas() {
    InnerCanvas canvas = new InnerCanvas();
    canvas.bgColor.bind(bgColor);
    return canvas;
  }

  protected static class InnerCanvas implements OverviewCanvas {
    @Getter
    private Group zoomContent;
    @Getter
    private Pane bgPane;
    @Getter
    private Group scrollContent;
    
    public ObjectProperty<Color> bgColor = new SimpleObjectProperty<>(Color.WHITE);

    public InnerCanvas() {
      zoomContent = createZoomContent();
      zoomContent.setStyle("-fx-padding:200;");
      bgPane = new Pane();
      bgPane.setLayoutX(0);
      bgPane.setLayoutY(0);
      bgPane.styleProperty().bind(new BackgroundColorStyleBinding(bgColor));

      zoomContent.getChildren().add(bgPane);
      scrollContent = new Group(zoomContent);
    }
    
    protected Group createZoomContent() {
      return new Group();
    }

    @Override
    public Parent getGraphContainer() {
      return zoomContent;
    }

    @Override
    public Parent getContainer() {
      return scrollContent;
    }

    @Override
    public void relocateContainer(double xpos, double ypos, double scaleFactor) {
      getContainer().setLayoutX(xpos - bgPane.getLayoutX() * scaleFactor);
      getContainer().setLayoutY(ypos - bgPane.getLayoutY() * scaleFactor);
    }
  }

  @Override
  public boolean isCellScaleEnable() {
    return false;
  }

  @Override
  public DoubleProperty getCellScaleProperty() {
    return null;
  }

  @Override
  public void setBgColor(Color color) {
    bgColor.set(color);
  }

  @Override
  public Color getBgColor() {
    return bgColor.get();
  }

  @Override
  public void fitToView() {
    updateBgPane();
    setHvalue((getHmin() + getHmax()) / 2);
    setVvalue((getVmin() + getVmax()) / 2);
  }

  @Override
  public void centerRegion(Rectangle2D region) {
    Bounds bgPaneBounds = innerCanvas.getBgPane().getBoundsInParent();
    double scaleX = scale.getX();
    double scaleY = scale.getY();
    region = new Rectangle2D((region.getMinX() - bgPaneBounds.getMinX()) * scaleX, 
        (region.getMinY() - bgPaneBounds.getMinY()) * scaleY, 
        region.getWidth() * scaleX, region.getHeight() * scaleY);
    double width = innerCanvas.getContainer().getBoundsInLocal().getWidth();
    double height = innerCanvas.getContainer().getBoundsInLocal().getHeight();
    
    double visibleWidth = width * getHorizontalVisibleProperty().get();
    double visibleHeight = height * getVerticalVisibleProperty().get();
    
    double newVisibleXpos = region.getMinX();
    if (region.getWidth() < visibleWidth) {
      newVisibleXpos -= (visibleWidth - region.getWidth()) / 2;
    } else {
      newVisibleXpos += (region.getWidth() - visibleWidth) / 2;
    }
    
    double newVisibleYpos = region.getMinY();
    if (region.getHeight() < visibleHeight) {
      newVisibleYpos -= (visibleHeight - region.getHeight()) / 2;
    } else {
      newVisibleYpos += (region.getHeight() - visibleHeight) / 2;
    }
    
    newVisibleXpos = Math.max(0, newVisibleXpos);
    newVisibleYpos = Math.max(0, newVisibleYpos);
    newVisibleXpos = Math.min(width - visibleWidth, newVisibleXpos);
    newVisibleYpos = Math.min(height - visibleHeight, newVisibleYpos);
    
    setHvalue(newVisibleXpos / (width - visibleWidth));
    setVvalue(newVisibleYpos / (height - visibleHeight));
  }
  
}
