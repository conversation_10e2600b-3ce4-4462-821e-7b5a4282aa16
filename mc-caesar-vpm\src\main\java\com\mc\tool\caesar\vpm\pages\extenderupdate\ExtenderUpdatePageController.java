package com.mc.tool.caesar.vpm.pages.extenderupdate;

import com.google.common.base.Strings;
import com.mc.common.util.PlatformUtility;
import com.mc.common.util.WeakAdapter;
import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.CaesarSwitchDataModel;
import com.mc.tool.caesar.api.datamodel.ConsoleData;
import com.mc.tool.caesar.api.datamodel.ExtenderData;
import com.mc.tool.caesar.api.datamodel.MatrixData;
import com.mc.tool.caesar.api.datamodel.MatrixDefinitionData;
import com.mc.tool.caesar.api.datamodel.ModuleData;
import com.mc.tool.caesar.api.datamodel.PortData;
import com.mc.tool.caesar.api.datamodel.vp.VpConsoleData;
import com.mc.tool.caesar.api.utils.Utilities;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.pages.extenderupdate.Bundle.NbBundle;
import com.mc.tool.caesar.vpm.pages.extenderupdate.offlinemanager.OffineManagerView;
import com.mc.tool.caesar.vpm.pages.extenderupdate.txgroup.InvalidTxRxGroupsManagerView;
import com.mc.tool.caesar.vpm.pages.update.UpdateData;
import com.mc.tool.caesar.vpm.pages.update.UpdateLogger;
import com.mc.tool.caesar.vpm.util.PortComparator;
import com.mc.tool.caesar.vpm.util.update.UpdateConstant;
import com.mc.tool.caesar.vpm.util.update.UpdatePackageInfo;
import com.mc.tool.caesar.vpm.util.update.UpdateUtilities;
import com.mc.tool.framework.interfaces.ApplicationBase;
import com.mc.tool.framework.interfaces.DeviceControllable;
import com.mc.tool.framework.interfaces.ViewControllable;
import com.mc.tool.framework.utility.InjectorProvider;
import com.mc.tool.framework.utility.UndecoratedAlert;
import com.mc.tool.framework.utility.UndecoratedDialog;
import com.mc.tool.framework.utility.dialogues.FileDialogue;
import com.mc.tool.framework.utility.dialogues.FileDialogueFactory;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import java.beans.PropertyChangeListener;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URL;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.ResourceBundle;
import java.util.Set;
import java.util.stream.Collectors;
import javafx.application.Platform;
import javafx.beans.binding.BooleanBinding;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.beans.value.ObservableValue;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.event.ActionEvent;
import javafx.event.EventHandler;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.AlertEx.AlertExType;
import javafx.scene.control.Button;
import javafx.scene.control.ButtonBar;
import javafx.scene.control.ButtonType;
import javafx.scene.control.CheckBox;
import javafx.scene.control.DialogPaneEx;
import javafx.scene.control.Label;
import javafx.scene.control.ListView;
import javafx.scene.control.ProgressBar;
import javafx.scene.control.SelectionMode;
import javafx.scene.control.Tooltip;
import javafx.scene.control.TreeItem;
import javafx.scene.control.TreeTableColumn;
import javafx.scene.control.TreeTableColumn.CellDataFeatures;
import javafx.scene.control.TreeTableView;
import javafx.scene.input.MouseEvent;
import javafx.stage.FileChooser.ExtensionFilter;
import javafx.stage.Window;
import javafx.util.Callback;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.archivers.ArchiveEntry;
import org.apache.commons.compress.archivers.cpio.CpioArchiveInputStream;
import org.controlsfx.control.CheckListView;

/**
 * .
 */
@Slf4j
@SuppressFBWarnings("NP_NULL_ON_SOME_PATH")
public class ExtenderUpdatePageController implements Initializable, ViewControllable {

  @FXML private TreeTableView<UpdateData> tableView;
  @FXML private Button updateButton;
  @FXML private Button loadButton;
  @FXML private Button cancelButton;
  @FXML private Button selectAllButton;
  @FXML private Button selectDiffVerButton;
  @FXML private Label updateFilePathLabel;
  @FXML private Label countNumLabel;
  @FXML private ListView<String> loglist;
  @FXML private TreeTableColumn<UpdateData, String> deviceCol;
  @FXML private TreeTableColumn<UpdateData, String> nameCol;
  @FXML private TreeTableColumn<UpdateData, String> typeCol;
  @FXML private TreeTableColumn<UpdateData, String> portCol;
  @FXML private TreeTableColumn<UpdateData, String> serialCol;
  @FXML private TreeTableColumn<UpdateData, String> hardwareVerCol;
  @FXML private TreeTableColumn<UpdateData, String> currentVersionCol;
  @FXML private TreeTableColumn<UpdateData, String> currentDateCol;
  @FXML private TreeTableColumn<UpdateData, String> updateVersionCol;
  @FXML private TreeTableColumn<UpdateData, String> updateDateCol;
  @FXML private TreeTableColumn<UpdateData, CheckBox> updateCol;
  @FXML private TreeTableColumn<UpdateData, ProgressBar> progressCol;

  private WeakAdapter weakAdapter = new WeakAdapter();

  private CaesarDeviceController deviceController = null;

  private ObservableList<String> logs = FXCollections.observableArrayList();
  private UpdateLogger logger = new UpdateLogger(logs);
  private UpdateExtenderUpdater updater;
  private UpdateMultiExtenderUpdater multiExtUpdater;

  private SimpleBooleanProperty updating = new SimpleBooleanProperty(false);
  private SimpleBooleanProperty updateCanecelled = new SimpleBooleanProperty(false);
  private SimpleBooleanProperty selectedAll = new SimpleBooleanProperty(false);
  private List<TreeItem<UpdateData>> availableGroup = new ArrayList<>(); // 可行的typeItem

  private List<UpdateData> updateDataList = new ArrayList<>();

  public ObservableList<UpdateData> data;

  protected final String[] updateData = {
    ExtenderData.PROPERTY_STATUS,
    ExtenderData.PROPERTY_VERSION,
    ExtenderData.PROPERTY_PORT,
    ExtenderData.PROPERTY_RDPORT,
    ExtenderData.PROPERTY_NAME,
    MatrixData.PROPERTY_STATUS,
    MatrixData.PROPERTY_HOSTADDRESS
  };

  private static final String UPGRADE_PACKAGE_SUFFIX_SWU = "swu";
  private static final String UPGRADE_PACKAGE_SUFFIX_BIN = "bin";

  /** . */
  @Override
  public void setDeviceController(DeviceControllable deviceController) {
    if (deviceController instanceof CaesarDeviceController) {
      this.deviceController = (CaesarDeviceController) deviceController;
    } else {
      log.warn("Error device controller type : {} !", deviceController.getClass().getName());
      return;
    }
    updateNodesImpl();
    updater = new UpdateExtenderUpdater(this.deviceController, logger, this);
    multiExtUpdater = new UpdateMultiExtenderUpdater(this.deviceController, logger, this);

    CaesarSwitchDataModel model = this.deviceController.getDataModel();
    model.addPropertyChangeListener(
        updateData,
        weakAdapter.wrap(
            (PropertyChangeListener)
                evt ->
                    Platform.runLater(
                        () -> {
                          if (isUpdating()) {
                            return;
                          }
                          this.deviceController
                              .submitAsync(() -> Utilities.getMatrixModels(model))
                              .whenComplete(
                                  (data, throwable) -> {
                                    if (throwable != null) {
                                      log.warn("Fail to get matrix models!", throwable);
                                      return;
                                    }
                                    PlatformUtility.runInFxThread(
                                        () -> {
                                          loadUpdateData(model, data);
                                          tableView.refresh();
                                        });
                                  });
                        })));
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {
    updateFilePathLabel.setStyle("-fx-border-width: 0 0 1 0;-fx-border-color: black;");
    BooleanBinding labelBinding = updateFilePathLabel.textProperty().isEmpty();
    updateButton.disableProperty().bind(updating.or(labelBinding));
    cancelButton.disableProperty().bind(updating.not());
    loadButton.disableProperty().bind(updating);
    selectAllButton.disableProperty().bind(updating.or(labelBinding));
    selectDiffVerButton.disableProperty().bind(updating.or(labelBinding));

    // 在更新期间，通过事件过滤器阻止用户交互，但允许滚动
    final EventHandler<MouseEvent> blockInteractionFilter = event -> {
      // 消费掉所有鼠标点击和释放事件，以防止修改任何控件的状态
      if (!updating.get()) {
        return;
      }
      if (event.getEventType() == MouseEvent.MOUSE_PRESSED
          || event.getEventType() == MouseEvent.MOUSE_RELEASED
          || event.getEventType() == MouseEvent.MOUSE_CLICKED) {
        event.consume();
      }
    };
    tableView.addEventFilter(MouseEvent.ANY, blockInteractionFilter);
    // 同时，在更新时将表格设置为半透明，以提供视觉反馈
    updating.addListener((observable, oldValue, newValue) -> {
      if (newValue) {
        tableView.setOpacity(0.6);
      } else {
        tableView.setOpacity(1.0);
      }
    });

    selectedAll.addListener(
        weakAdapter.wrap(
            (observable, oldValue, newValue) -> {
              boolean newv = newValue;
              if (newv) {
                selectAllButton.setText(
                    NbBundle.getMessage(
                        ExtenderUpdatePageController.class,
                        "ExtenderUpdate.seleteAllButton.text.opposite"));
              } else {
                selectAllButton.setText(
                    NbBundle.getMessage(
                        ExtenderUpdatePageController.class, "ExtenderUpdate.seleteAllButton.text"));
              }
              for (TreeItem<UpdateData> typeItem : availableGroup) {
                typeItem.getValue().setSelected(newv);
                if (newv) {
                  typeItem.getParent().setExpanded(true);
                }
              }
            }));

    loglist.setItems(logs);

    initTreeView();
    initButton();
    initLoadButton();
  }

  /** . */
  public TreeTableView<UpdateData> getTreeView() {
    return this.tableView;
  }

  /** . */
  public void initTreeView() {
    if (null == tableView) {
      return;
    }

    tableView.setShowRoot(false);

    nameCol.setCellValueFactory((item) -> item.getValue().getValue().getNameProperty());
    typeCol.setCellValueFactory((item) -> item.getValue().getValue().getTypeProperty());
    serialCol.setCellValueFactory((item) -> item.getValue().getValue().getSerialProperty());
    hardwareVerCol.setCellValueFactory(
        (item) -> item.getValue().getValue().getHardwareVersionProperty());
    currentVersionCol.setCellValueFactory(
        (item) -> item.getValue().getValue().getCurrentVersionProperty());
    currentDateCol.setCellValueFactory(
        (item) -> item.getValue().getValue().getCurrentDateProperty());
    updateVersionCol.setCellValueFactory(
        (item) -> item.getValue().getValue().getUpdateVersionProperty());
    updateDateCol.setCellValueFactory((item) -> item.getValue().getValue().getUpdateDateProperty());
    portCol.setCellValueFactory((item) -> item.getValue().getValue().getPortsProperty());
    portCol.setComparator(new PortComparator());
    deviceCol.setCellValueFactory((item) -> item.getValue().getValue().getDeviceNameProperty());

    updateCol.setCellValueFactory(
        (item) -> new SimpleObjectProperty<>(item.getValue().getValue().getCheckBox()));
    progressCol.setCellValueFactory(new ProgressCallback());
  }

  private static class ProgressCallback
      implements Callback<CellDataFeatures<UpdateData, ProgressBar>, ObservableValue<ProgressBar>> {
    @Override
    public ObservableValue<ProgressBar> call(CellDataFeatures<UpdateData, ProgressBar> param) {
      UpdateData updateData = param.getValue().getValue();
      ProgressBar progressBar = updateData.getProgressBar();
      if (updateData.getType() == null || updateData.getCurrentVersion() == null) {
        progressBar.setVisible(false);
      }
      return new SimpleObjectProperty<>(progressBar);
    }
  }

  public boolean isUpdating() {
    return updating.get();
  }

  public void setUpdating(boolean value) {
    this.updating.set(value);
  }

  public boolean isCancelled() {
    return updateCanecelled.get();
  }

  public void setCancelled(boolean value) {
    this.updateCanecelled.set(value);
  }

  /** . */
  public void updateNodes() {
    PlatformUtility.runInFxThread(this::updateNodesImpl);
  }

  /** . */
  private void updateNodesImpl() {
    if (tableView == null) {
      return;
    }

    selectedAll.set(false);
    updateFilePathLabel.setText("");
    availableGroup.clear();

    if (tableView.getRoot() == null) {
      UpdateData rootdata = new UpdateData(-1, (byte) 0, "s0.0.0.0".substring(1));
      initUpdateData(rootdata);
      rootdata.setName("系统");
      rootdata.setType("主机");
      updateDataList.add(rootdata);
      TreeItem<UpdateData> rootNode = new TreeItem<>(rootdata);
      tableView.setRoot(rootNode);
    }

    CaesarSwitchDataModel model = deviceController.getDataModel();
    deviceController
        .submitAsync(() -> Utilities.getMatrixModels(model))
        .whenComplete(
            (data, throwable) -> {
              if (throwable != null) {
                log.warn("Fail to get matrix models!", throwable);
                return;
              }
              PlatformUtility.runInFxThread(
                  () -> {
                    loadUpdateData(model, data);
                    for (TreeItem<UpdateData> dkmItem : tableView.getRoot().getChildren()) {
                      dkmItem.setExpanded(true);
                    }
                    tableView.refresh();
                  });
            });
  }

  private void loadUpdateData(
      CaesarSwitchDataModel model,
      Collection<Pair<MatrixDefinitionData, CaesarSwitchDataModel>> matrices) {
    List<TreeItem<UpdateData>> retainMatrixItems = new ArrayList<>();
    for (Pair<MatrixDefinitionData, CaesarSwitchDataModel> matrice : matrices) {
      MatrixDefinitionData mdd = matrice.getKey();
      CaesarSwitchDataModel matModel = matrice.getValue();
      try {
        String address = mdd.getAddress();
        String serial = "";
        try {
          serial = matModel.getSwitchModuleData().getModuleData(0).getSerial();
        } catch (RuntimeException exception) {
          log.warn("Fail to get model for {}!", address);
        }
        TreeItem<UpdateData> dkmNode =
            createDkmNode(mdd.getDevice(), address, tableView.getRoot(), serial);
        retainMatrixItems.add(dkmNode);
        List<TreeItem<UpdateData>> retainItems = new ArrayList<>();
        for (ModuleData moduleData : model.getSwitchModuleData().getModuleDatas()) {

          if (moduleData.isStatusActive()
              && moduleData.isStatusAvailable()
              && mdd.getFirstModule() <= moduleData.getOid()
              && moduleData.getOid() <= mdd.getLastModule()) {

            byte localLevel2 = 0;
            Pair<Integer, Integer> portRange = Utilities.getModulePortRange(model, moduleData);
            int startIndex = portRange.getKey() - 1;
            int endIndex = portRange.getValue();
            for (int i = startIndex; i < endIndex; i++) {
              localLevel2 = (byte) (localLevel2 + 1);
              PortData portData = model.getConfigData().getPortData(i);
              ExtenderData extenderData = portData.getExtenderData();
              if (extenderData != null && extenderData.isStatusActive()) {
                int localPort = 0;
                if (isPortInMatix(extenderData.getPort(), mdd)
                    && portData.getOid() + 1 == extenderData.getPort()) {
                  localPort = extenderData.getPort();
                } else if (isPortInMatix(extenderData.getRdPort(), mdd)
                    && !isPortInMatix(extenderData.getPort(), mdd)) {
                  localPort = extenderData.getRdPort();
                }
                if (localPort != 0) {
                  if (extenderData.isVpConType()) {
                    // 只显示vpcon的第一个端口
                    VpConsoleData targetConsoleData = null;
                    for (VpConsoleData vpConsoleData :
                        model.getConfigDataManager().getActiveVpconsolses()) {
                      if (!vpConsoleData.isStatusOnline()) {
                        continue;
                      }
                      ConsoleData[] consoleDatas = vpConsoleData.getInPortList();
                      if (consoleDatas.length == 0) {
                        continue;
                      }
                      if (consoleDatas[0].getExtenderData(0) == extenderData) {
                        targetConsoleData = vpConsoleData;
                      }
                    }
                    if (targetConsoleData == null) {
                      continue;
                    }
                  }
                  // 预览终端子类型是SECONDARY不升级
                  CaesarConstants.Extender.SpecialExtenderSubType specialExtenderSubType =
                      CaesarConstants.Extender.SpecialExtenderSubType.valueOf(
                          extenderData.getExtenderStatusInfo().getSpecialExtType(),
                          extenderData.getExtenderStatusInfo().getSpecialExtSubType());
                  if (CaesarConstants.Extender.SpecialExtenderSubType.SECONDARY.equals(
                      specialExtenderSubType)) {
                    continue;
                  }
                  TreeItem<UpdateData> exTreeItem =
                      createSlotNode(
                          moduleData.getOid(), localLevel2, extenderData, address, dkmNode);

                  retainItems.add(exTreeItem);
                }
              }
            }
          }
        }

        dkmNode.getChildren().retainAll(retainItems);
      } catch (RuntimeException ex) {
        log.warn("Fail to load update data!", ex);
      }
    }

    if (tableView.getRoot() != null) {
      tableView.getRoot().getChildren().retainAll(retainMatrixItems);
      ObservableList<TreeItem<UpdateData>> dkmList = tableView.getRoot().getChildren();
      for (TreeItem<UpdateData> dkm : dkmList) {
        FXCollections.sort(
            dkm.getChildren(),
            (re1, re2) -> {
              if (re1.getValue().getLevel1() > re2.getValue().getLevel1()) {
                return 1;
              } else if (re1.getValue().getLevel1() == re2.getValue().getLevel1()
                  && re1.getValue().getLevel2() > re2.getValue().getLevel2()) {
                return 1;
              } else if (re1.getValue().getLevel1() == re2.getValue().getLevel1()
                  && re1.getValue().getLevel2() == re2.getValue().getLevel2()) {
                return 0;
              } else if (re1.getValue().getLevel1() == re2.getValue().getLevel1()
                  && re1.getValue().getLevel2() < re2.getValue().getLevel2()) {
                return -1;
              } else if (re1.getValue().getLevel1() < re2.getValue().getLevel1()) {
                return -1;
              }
              return 0;
            });
      }
    }
  }

  /** initButton. */
  private void initButton() {
    updateButton.setOnAction(
        weakAdapter.wrap(
            (EventHandler<ActionEvent>)
                event -> {
                  String path = updateFilePathLabel.getTooltip().getText();
                  if (!Strings.isNullOrEmpty(path)) {
                    if (path.endsWith(UPGRADE_PACKAGE_SUFFIX_SWU)) {
                      updater.update();
                    } else if (path.endsWith(UPGRADE_PACKAGE_SUFFIX_BIN)) {
                      multiExtUpdater.update();
                    }
                  }
                }));

    cancelButton.setOnAction(
        weakAdapter.wrap(
            (EventHandler<ActionEvent>)
                event -> {
                  updateCanecelled.set(true);
                  updater.updateCancel();
                  multiExtUpdater.updateCancel();
                }));

    selectAllButton.setOnAction(
        weakAdapter.wrap(
            (EventHandler<ActionEvent>)
                event -> {
                  if (!updateFilePathLabel.getText().isEmpty()) {
                    selectedAll.set(!selectedAll.get());
                  }
                }));
    selectDiffVerButton.setOnAction(
        weakAdapter.wrap(
            (EventHandler<ActionEvent>)
                event -> {
                  Set<Integer> selectedExt = new HashSet<>();
                  for (TreeItem<UpdateData> typeItem : availableGroup) {
                    boolean isSameDate =
                        Objects.equals(
                            typeItem.getValue().getUpdateDate(),
                            typeItem.getValue().getCurrentDate());
                    boolean isSameVersion =
                        Objects.equals(
                            typeItem.getValue().getCurrentVersionProperty().getValue(),
                            typeItem.getValue().getUpdateVersionProperty().getValue());
                    boolean shouldSelect = !isSameDate || !isSameVersion;
                    if (shouldSelect
                        && !selectedExt.contains(typeItem.getParent().getValue().getId())) {
                      selectedExt.add(typeItem.getParent().getValue().getId());
                      typeItem.getValue().setSelected(true);
                      typeItem.getParent().setExpanded(true);
                    } else if (!shouldSelect
                        && !selectedExt.contains(typeItem.getParent().getValue().getId())) {
                      typeItem.getValue().setSelected(false);
                      typeItem.getParent().setExpanded(false);
                    }
                  }
                }));
  }


  /** . */
  private void initLoadButton() {
    loadButton.setOnAction(
        weakAdapter.wrap(
            new EventHandler<ActionEvent>() {
              @Override
              public void handle(ActionEvent event) {
                FileDialogueFactory factory =
                    InjectorProvider.getInjector().getInstance(FileDialogueFactory.class);
                FileDialogue fileDialogue = factory.createFileDialogue();
                fileDialogue.addExtensionFilter(new ExtensionFilter("SWU/BIN", "*.swu", "*.bin"));
                File choosedfile = fileDialogue.showOpenDialog(loadButton.getScene().getWindow());
                if (null == choosedfile) {
                  return;
                }
                if (choosedfile.getName().endsWith(UPGRADE_PACKAGE_SUFFIX_SWU)) {
                  loadSwuFile(choosedfile);
                } else if (choosedfile.getName().endsWith(UPGRADE_PACKAGE_SUFFIX_BIN)) {
                  loadBinFile(choosedfile);
                }
              }
            }));
  }

  private void loadBinFile(File choosedfile) {
    String updateFile = "update";
    String destDirPath = System.getProperty("user.dir") + "/" + updateFile;
    deleteDir(destDirPath);
    if (decompressCpio(choosedfile, destDirPath)) {
      File[] files = new File(destDirPath).listFiles(File::isFile);
      List<String> fileNameList =
          Arrays.stream(files)
              .filter(item -> item.getName().endsWith(".swu"))
              .map(File::getPath)
              .collect(Collectors.toList());
      FileSelectorDialog fileSelectorDialog = new FileSelectorDialog(fileNameList);
      ApplicationBase instance = InjectorProvider.getInjector().getInstance(ApplicationBase.class);
      fileSelectorDialog.initOwner(instance.getMainWindow());
      fileSelectorDialog.showAndWait();
      List<SelectorFile> fileSelectorDialogResult = fileSelectorDialog.getResult();
      if (fileSelectorDialogResult.isEmpty()) {
        return;
      }
      try {
        selectedAll.set(false);
        Map<String, UpdatePackageInfo> updatePackageInfoMap = new HashMap<>();
        for (File file : fileSelectorDialogResult) {
          updatePackageInfoMap.put(
              file.getPath(), UpdateUtilities.getUpdatePackageInfo(file.getPath()));
        }
        for (UpdatePackageInfo updatePackageInfo : updatePackageInfoMap.values()) {
          if (!updatePackageInfo.isExtenderUpdate() || !updatePackageInfo.isVersionValid()) {
            String message = null;
            if (!updatePackageInfo.isVersionValid()) {
              message =
                  NbBundle.getMessage(
                      ExtenderUpdatePageController.class,
                      "ExtenderUpdate.alert.warning.versionError.text");
            } else if (!updatePackageInfo.isExtenderUpdate()) {
              message =
                  NbBundle.getMessage(
                      ExtenderUpdatePageController.class,
                      "ExtenderUpdate.alert.warning.packageError.text");
            }
            String title =
                NbBundle.getMessage(
                    ExtenderUpdatePageController.class, "ExtenderUpdate.alert.warning.title");
            UndecoratedAlert alert = warningAlert(tableView.getScene().getWindow(), title, message);
            alert.showAndWait();
            return;
          }
        }
        updateFilePathLabel.setText(choosedfile.getName());
        updateFilePathLabel.setTooltip(new Tooltip(choosedfile.getPath()));

        clearSelectedAndProgress();
        availableGroup.clear();
        Map<String, Set<Integer>> availableExtIdMap = new HashMap<>();
        for (Map.Entry<String, UpdatePackageInfo> entry : updatePackageInfoMap.entrySet()) {
          UpdatePackageInfo updatePackageInfo = entry.getValue();
          Collection<String> targetType = updatePackageInfo.getTargetType();
          // 清除绑定
          List<String> typeList = new ArrayList<>();
          if (updatePackageInfo.getApp() != null) {
            typeList.add(UpdateConstant.UPDATE_APP_TYPE);
          }
          if (updatePackageInfo.getSys() != null) {
            typeList.add(UpdateConstant.UPDATE_SYS_TYPE);
          }
          if (updatePackageInfo.hasFpga()) {
            typeList.add(UpdateConstant.UPDATE_FPGA_TYPE);
          }
          // 更新版本
          Set<Integer> availableExtIds = new HashSet<>();
          for (TreeItem<UpdateData> dkmItem : tableView.getRoot().getChildren()) {
            for (TreeItem<UpdateData> extItem : dkmItem.getChildren()) {
              boolean hardwareMatch =
                  extItem.getValue().isHardwareVersionMatch(updatePackageInfo.getHardWare());
              ExtenderData extData = deviceController.getDataModel().getConfigDataManager()
                  .getExtenderDataById(extItem.getValue().getId());
              for (TreeItem<UpdateData> typeItem : extItem.getChildren()) {
                UpdateData updateData = typeItem.getValue();
                if (updateData.getType() != null
                    && typeList.contains(updateData.getType())
                    && targetType.contains(typeItem.getParent().getValue().getType())
                    && hardwareMatch) {
                  if (updateData.getType().equalsIgnoreCase(UpdateConstant.UPDATE_SYS_TYPE)) {
                    updateData.setUpdateVersion(updatePackageInfo.getSys());
                    updateData.setUpdateDate(updatePackageInfo.getSys().getDate());
                  } else if (updateData
                      .getType()
                      .equalsIgnoreCase(UpdateConstant.UPDATE_APP_TYPE)) {
                    updateData.setUpdateVersion(updatePackageInfo.getApp());
                    updateData.setUpdateDate(updatePackageInfo.getApp().getDate());
                  } else if (updateData
                      .getType()
                      .equalsIgnoreCase(UpdateConstant.UPDATE_FPGA_TYPE)) {
                    setFpgaVersion(updatePackageInfo, updateData, extData);
                  }
                  updateData.setDisabled(false);
                  availableGroup.add(typeItem);
                  availableExtIds.add(typeItem.getParent().getValue().getId());
                }
              }
            }
          }
          availableExtIdMap.put(entry.getKey(), availableExtIds);
        }
        multiExtUpdater.setAvailableGroup(availableGroup);
        multiExtUpdater.setAvailableExtIdMap(availableExtIdMap);
      } catch (IOException | RuntimeException ex) {
        log.warn("Fail to load update file!", ex);
      } finally {
        tableView.refresh();
      }
    }
  }

  private void loadSwuFile(File choosedfile) {
    try {
      selectedAll.set(false);
      UpdatePackageInfo updatePackageInfo =
          UpdateUtilities.getUpdatePackageInfo(choosedfile.getPath());
      if (!updatePackageInfo.isExtenderUpdate() || !updatePackageInfo.isVersionValid()) {
        String message = null;
        if (!updatePackageInfo.isVersionValid()) {
          message =
              NbBundle.getMessage(
                  ExtenderUpdatePageController.class,
                  "ExtenderUpdate.alert.warning.versionError.text");
        } else if (!updatePackageInfo.isExtenderUpdate()) {
          message =
              NbBundle.getMessage(
                  ExtenderUpdatePageController.class,
                  "ExtenderUpdate.alert.warning.packageError.text");
        }
        String title =
            NbBundle.getMessage(
                ExtenderUpdatePageController.class, "ExtenderUpdate.alert.warning.title");
        UndecoratedAlert alert = warningAlert(tableView.getScene().getWindow(), title, message);
        alert.showAndWait();
      } else {
        updateFilePathLabel.setText(choosedfile.getName());
        updateFilePathLabel.setTooltip(new Tooltip(choosedfile.getPath()));

        Collection<String> targetType = updatePackageInfo.getTargetType();
        clearSelectedAndProgress();
        // 清除绑定
        List<String> typeList = new ArrayList<>();
        if (updatePackageInfo.getApp() != null) {
          typeList.add(UpdateConstant.UPDATE_APP_TYPE);
        }
        if (updatePackageInfo.getSys() != null) {
          typeList.add(UpdateConstant.UPDATE_SYS_TYPE);
        }
        if (updatePackageInfo.hasFpga()) {
          typeList.add(UpdateConstant.UPDATE_FPGA_TYPE);
        }
        // 更新版本
        for (TreeItem<UpdateData> dkmItem : tableView.getRoot().getChildren()) {
          for (TreeItem<UpdateData> extItem : dkmItem.getChildren()) {
            boolean hardwareMatch =
                extItem.getValue().isHardwareVersionMatch(updatePackageInfo.getHardWare());
            ExtenderData extData = deviceController.getDataModel().getConfigDataManager()
                .getExtenderDataById(extItem.getValue().getId());
            for (TreeItem<UpdateData> typeItem : extItem.getChildren()) {
              UpdateData updateData = typeItem.getValue();
              if (updateData.getType() != null
                  && typeList.contains(updateData.getType())
                  && targetType.contains(typeItem.getParent().getValue().getType())
                  && hardwareMatch) {
                if (updateData.getType().equalsIgnoreCase(UpdateConstant.UPDATE_SYS_TYPE)) {
                  updateData.setUpdateVersion(updatePackageInfo.getSys());
                  updateData.setUpdateDate(updatePackageInfo.getSys().getDate());
                } else if (updateData.getType().equalsIgnoreCase(UpdateConstant.UPDATE_APP_TYPE)) {
                  updateData.setUpdateVersion(updatePackageInfo.getApp());
                  updateData.setUpdateDate(updatePackageInfo.getApp().getDate());
                } else if (updateData.getType().equalsIgnoreCase(UpdateConstant.UPDATE_FPGA_TYPE)) {
                  setFpgaVersion(updatePackageInfo, updateData, extData);
                }
                updateData.setDisabled(false);
              } else {
                if (!typeList.contains(updateData.getType()) || !hardwareMatch) {
                  updateData.setUpdateDate("");
                  updateData.setUpdateVersion(null);
                  updateData.setDisabled(true);
                }
              }
            }
          }
        }

        availableGroup.clear();
        for (TreeItem<UpdateData> dkmItem : tableView.getRoot().getChildren()) {
          for (TreeItem<UpdateData> extItem : dkmItem.getChildren()) {
            for (TreeItem<UpdateData> typeItem : extItem.getChildren()) {
              if (!typeItem.getValue().getDisabled()) {
                availableGroup.add(typeItem);
              }
            }
          }
        }
        updater.setFilePath(choosedfile.getPath());
        updater.setAvailableGroup(availableGroup);
      }
    } catch (IOException | RuntimeException ex) {
      log.warn("Fail to load update file!", ex);
    } finally {
      tableView.refresh();
    }
  }

  private void setFpgaVersion(UpdatePackageInfo updatePackageInfo, UpdateData updateData,
                              ExtenderData extData) {
    if (updatePackageInfo.getFpga() != null) {
      updateData.setUpdateVersion(updatePackageInfo.getFpga());
      updateData.setUpdateDate(updatePackageInfo.getFpga().getDate());
    } else if (extData != null) {
      if (extData.isCpuType() && updatePackageInfo.getFpgaHdmiTx() != null) {
        updateData.setUpdateVersion(updatePackageInfo.getFpgaHdmiTx());
        updateData.setUpdateDate(updatePackageInfo.getFpgaHdmiTx().getDate());
      } else if (extData.isConType()) {
        CaesarConstants.Extender.SpecialExtenderType extType =
            extData.getExtenderStatusInfo().getSpecialExtType();
        CaesarConstants.Extender.SpecialExtenderSubType extSubType =
            CaesarConstants.Extender.SpecialExtenderSubType.valueOf(extType,
                extData.getExtenderStatusInfo().getSpecialExtSubType());
        if (extType == CaesarConstants.Extender.SpecialExtenderType.HW_4VIEW
            || extType == CaesarConstants.Extender.SpecialExtenderType.HW_4VIEW_KAITO_02) {
          if (extSubType == CaesarConstants.Extender.SpecialExtenderSubType.NORMAL) {
            updateData.setUpdateVersion(updatePackageInfo.getFpgaHdmi4Rx());
            updateData.setUpdateDate(updatePackageInfo.getFpgaHdmi4Rx().getDate());
          } else if (extSubType == CaesarConstants.Extender.SpecialExtenderSubType.HALF_REDUNDANT) {
            updateData.setUpdateVersion(updatePackageInfo.getFpgaHdmi4RxHalf1G());
            updateData.setUpdateDate(updatePackageInfo.getFpgaHdmi4RxHalf1G().getDate());
          } else if (extSubType
              == CaesarConstants.Extender.SpecialExtenderSubType.HALF_REDUNDANT_2_5G) {
            updateData.setUpdateVersion(updatePackageInfo.getFpgaHdmi4RxHalf25G());
            updateData.setUpdateDate(updatePackageInfo.getFpgaHdmi4RxHalf25G().getDate());
          }
        } else if (extType == CaesarConstants.Extender.SpecialExtenderType.HW_PREVIEW) {
          updateData.setUpdateVersion(updatePackageInfo.getFpgaCaesarPreview());
          updateData.setUpdateDate(updatePackageInfo.getFpgaCaesarPreview().getDate());
        } else {
          updateData.setUpdateVersion(updatePackageInfo.getFpgaHdmiRx());
          updateData.setUpdateDate(updatePackageInfo.getFpgaHdmiRx().getDate());
        }
      }
    }
  }

  /** Clear Selected And Progress. */
  public void clearSelectedAndProgress() {
    for (UpdateData updateData : updateDataList) {
      updateData.setSelected(false);
      updateData.setUpdateVersion(null);
      updateData.setUpdateDate(null);
      updateData.setDisabled(true);
      updateData.getProgressBar().progressProperty().unbind();
      updateData.getProgressBar().setProgress(0);
    }
  }

  /** . */
  private TreeItem<UpdateData> createSlotNode(
      int level1,
      byte level2,
      ExtenderData extenderData,
      String address,
      TreeItem<UpdateData> parent) {
    String type = "";
    String extName = extenderData.getName();
    if (extenderData.isVp6ConType()) {
      type = UpdateConstant.EXTENDER_VPCON_TYPE;
    } else if (extenderData.isVp7ConType()) {
      type = UpdateConstant.EXTENDER_VP7_TYPE;
    } else if (Utilities.areBitsSet(
        extenderData.getType(), true, CaesarConstants.Extender.Type.CON)) {
      type = UpdateConstant.EXTENDER_CON_TYPE;
    } else if (Utilities.areBitsSet(
        extenderData.getType(), true, CaesarConstants.Extender.Type.CPU)) {
      type = UpdateConstant.EXTENDER_CPU_TYPE;
    }

    // 查找
    TreeItem<UpdateData> result = null;
    for (TreeItem<UpdateData> item : parent.getChildren()) {
      UpdateData updateData = item.getValue();
      if (updateData.getLevel1() == level1
          && updateData.getLevel2() == level2
          && updateData.getType().equals(type)
          && updateData.getId() == extenderData.getId()) {
        result = item;
        break;
      }
    }

    // 找不到就创建一个
    if (result == null) {
      UpdateData appData = new UpdateData(level1, level2, address);
      initUpdateData(appData);
      UpdateData fpgaData = new UpdateData(level1, level2, address);
      initUpdateData(fpgaData);
      UpdateData systemData = new UpdateData(level1, level2, address);
      initUpdateData(systemData);
      UpdateData slotData = new UpdateData(level1, level2, address);
      initUpdateData(slotData);

      // 1. 只有未被禁用的复选框才能改变整个组的选择状态。
      // 2. 当一个复选框被禁用时，它会自动取消选择，并且不会再响应或影响组内其他成员。
      // 3. 所有未被禁用的复选框将继续保持同步。
      SimpleBooleanProperty groupSelected = new SimpleBooleanProperty(false);
      for (UpdateData item : Arrays.asList(appData, fpgaData, systemData)) {
        item.getSelectedProperty().addListener(weakAdapter.wrap((obs, oldVal, newVal) -> {
          if (!item.getDisabled() && groupSelected.get() != newVal) {
            groupSelected.set(newVal);
          }
        }));

        groupSelected.addListener(weakAdapter.wrap((obs, oldVal, newVal) -> {
          if (!item.getDisabled() && item.getSelected() != newVal) {
            item.setSelected(newVal);
          }
        }));
      }

      updateDataList.add(slotData);
      updateDataList.add(appData);
      updateDataList.add(fpgaData);
      updateDataList.add(systemData);

      slotData.setParentName(parent.getValue().getName());
      slotData.setType(type);
      slotData.setId(extenderData.getId());
      if (extenderData.getConsoleData() != null) {
        slotData.setDeviceNameProperty(extenderData.getConsoleData().getNameProperty());
      } else if (extenderData.getCpuData() != null) {
        slotData.setDeviceNameProperty(extenderData.getCpuData().getNameProperty());
      }

      slotData.setSerial(extenderData.getSerial());
      slotData.setHardwareVersion(extenderData.getVersion().getHwVersionDef());
      appData.setType(UpdateConstant.UPDATE_APP_TYPE);
      appData.setParentName(extName);

      fpgaData.setType(UpdateConstant.UPDATE_FPGA_TYPE);
      fpgaData.setParentName(extName);

      systemData.setType(UpdateConstant.UPDATE_SYS_TYPE);
      systemData.setParentName(extName);

      TreeItem<UpdateData> slotItem = new TreeItem<>(slotData);
      TreeItem<UpdateData> appItem = new TreeItem<>(appData);
      TreeItem<UpdateData> fpgaItem = new TreeItem<>(fpgaData);
      TreeItem<UpdateData> systemItem = new TreeItem<>(systemData);
      slotItem.getChildren().addAll(Arrays.asList(appItem, fpgaItem, systemItem));
      result = slotItem;
      parent.getChildren().add(slotItem);
    }
    // 更新数据
    result.getValue().setName(extName);
    for (TreeItem<UpdateData> item : result.getChildren()) {
      UpdateData data = item.getValue();
      switch (data.getType()) {
        case UpdateConstant.UPDATE_APP_TYPE:
          data.setCurrentVersion(extenderData.getVersion().getAppVersion());
          break;
        case UpdateConstant.UPDATE_FPGA_TYPE:
          data.setCurrentVersion(extenderData.getVersion().getFpgaVersion());
          break;
        case UpdateConstant.UPDATE_SYS_TYPE:
          data.setCurrentVersion(extenderData.getVersion().getSystemVersion());
          break;
        default:
          break;
      }
      if (data.getCurrentVersion() != null) {
        data.setCurrentDate(
            String.format(
                "%04d.%02d.%02d",
                data.getCurrentVersion().getYear() + 2000,
                data.getCurrentVersion().getMonth(),
                data.getCurrentVersion().getDay()));
      }
    }

    result.getValue().setPorts(extenderData.getPortProperty().get());
    return result;
  }

  /** . */
  public TreeItem<UpdateData> createDkmNode(
      String name, String address, TreeItem<UpdateData> parent, String serial) {
    final int level1 = -1;
    final int level2 = 0;
    for (TreeItem<UpdateData> item : parent.getChildren()) {
      if (item.getValue().getName().equals(name)) {
        return item;
      }
    }

    UpdateData dkmData = new UpdateData(level1, (byte) level2, address);
    initUpdateData(dkmData);
    dkmData.setName(name);
    dkmData.setType(UpdateConstant.UPDATE_HOST_TYPE);
    dkmData.setParentName(tableView.getRoot().getValue().getName());
    dkmData.setSerial(serial);

    updateDataList.add(dkmData);
    TreeItem<UpdateData> dkmTreeItem = new TreeItem<>(dkmData);
    parent.getChildren().add(dkmTreeItem);

    return dkmTreeItem;
  }

  private boolean isPortInMatix(int port, MatrixDefinitionData mdd) {
    int minPort = mdd.getFirstPort();
    int maxPort = mdd.getLastPort();

    return port >= minPort && port <= maxPort;
  }

  @Override
  public DeviceControllable getDeviceController() {
    return deviceController;
  }

  @FXML
  protected void onShowOfflineManager() {
    OffineManagerView.showView(tableView.getScene().getWindow(), deviceController);
  }

  @FXML
  protected void onShowTxgroupManager() {
    new InvalidTxRxGroupsManagerView(tableView.getScene().getWindow(), deviceController)
        .showAndWait();
  }

  protected void initUpdateData(UpdateData updateData) {
    CheckBox checkBox = updateData.getCheckBox();
    if (updateData.getType() == null) {
      checkBox.setVisible(false);
    }

    updateData
        .getDisabledProperty()
        .addListener(
            weakAdapter.wrap(
                (observable, oldValue, newValue) -> {
                  if (newValue) {
                    updateData.setSelected(false);
                  }
                }));

    checkBox
        .selectedProperty()
        .addListener(
            weakAdapter.wrap(
                (observable, oldValue, newValue) -> {
                  boolean newv = newValue;
                  if (selectedAll.get() && !newv) {
                    List<UpdateData> selectedGroup = new ArrayList<>();
                    for (UpdateData ud : updateDataList) {
                      if (ud.getSelected()) {
                        selectedGroup.add(ud);
                      }
                    }
                    selectedAll.set(false);
                    for (UpdateData ud : selectedGroup) {
                      ud.setSelected(true);
                    }
                  }
                  updateData.setSelected(newv);
                  int count = countingSelectedExt();
                  countNumLabel.setText(String.valueOf(count));
                }));

    checkBox.selectedProperty().bindBidirectional(updateData.getSelectedProperty());
    checkBox.disableProperty().bindBidirectional(updateData.getDisabledProperty());
  }

  private int countingSelectedExt() {
    int count = 0;
    for (TreeItem<UpdateData> dkmItem : tableView.getRoot().getChildren()) {
      for (TreeItem<UpdateData> extItem : dkmItem.getChildren()) {
        boolean hasSelected = false;
        for (TreeItem<UpdateData> typeItem : extItem.getChildren()) {
          if (typeItem.getValue().getSelected()) {
            hasSelected = true;
            break;
          }
        }
        if (hasSelected) {
          count++;
        }
      }
    }
    return count;
  }

  private UndecoratedAlert warningAlert(Window window, String title, String message) {
    UndecoratedAlert alert = new UndecoratedAlert(AlertExType.WARNING);
    alert.initOwner(window);
    alert.setTitle(title);
    alert.setHeaderText(null);
    alert.setContentText(message);
    return alert;
  }

  /** 解压CPIO. */
  public static boolean decompressCpio(File srcFile, String destFileDir) {
    if (!srcFile.exists()) {
      return false;
    }
    InputStream is = null;
    CpioArchiveInputStream zais = null;
    try {
      is = new FileInputStream(srcFile);
      zais = new CpioArchiveInputStream(is);
      ArchiveEntry archiveEntry;
      while ((archiveEntry = zais.getNextEntry()) != null) {
        // 获取文件名
        String entryFileName = archiveEntry.getName();
        byte[] content = new byte[(int) archiveEntry.getSize()];
        if (zais.read(content) == -1) {
          log.info("there is no more data");
        }
        // 把解压出来的文件写到指定路径
        String entryFilePath = destFileDir + "/" + entryFileName;
        File entryFile = new File(entryFilePath);
        // 保证这个文件的父文件夹必须要存在
        if (!entryFile.getParentFile().exists() && !entryFile.getParentFile().mkdirs()) {
          log.error("Can not create the directory," + entryFile.getParentFile().getPath());
          return false;
        }
        try (FileOutputStream fos = new FileOutputStream(entryFile)) {
          try (OutputStream os = new BufferedOutputStream(fos)) {
            os.write(content);
            os.flush();
          }
        }
      }
    } catch (IOException ex) {
      log.error("", ex);
      return false;
    } finally {
      try {
        if (zais != null) {
          zais.close();
        }
        if (is != null) {
          is.close();
        }
      } catch (IOException ex) {
        log.error("", ex);
      }
    }
    return true;
  }

  /** 递归删除文件. */
  public static void deleteDir(String dirPath) {
    File file = new File(dirPath);
    if (!file.exists()) {
      return;
    }
    if (!file.isFile()) {
      File[] files = file.listFiles();
      if (files != null) {
        for (File value : files) {
          deleteDir(value.getAbsolutePath());
        }
      }
    }
    if (file.delete()) {
      log.error("{} cannot be deleted.", file.getName());
    }
  }

  static class SelectorFile extends File {

    private static final long serialVersionUID = -6307131928630349465L;

    public SelectorFile(String file) {
      super(file);
    }

    @Override
    public String toString() {
      return getName();
    }
  }

  static class FileSelectorDialog extends UndecoratedDialog<List<SelectorFile>> {

    private final CheckListView<SelectorFile> checkListView;

    public FileSelectorDialog(List<String> fileNameList) {
      ObservableList<SelectorFile> strings =
          FXCollections.observableArrayList(
              fileNameList.stream().map(SelectorFile::new).collect(Collectors.toList()));
      checkListView = new CheckListView<>(strings);
      checkListView.getSelectionModel().setSelectionMode(SelectionMode.MULTIPLE);
      setTitle(
          NbBundle.getMessage(
              ExtenderUpdatePageController.class, "ExtenderUpdate.fileSelectorDialog.title"));
      getDialogPane().setContent(checkListView);

      final DialogPaneEx dialogPane = getDialogPane();
      dialogPane.getButtonTypes().addAll(ButtonType.OK, ButtonType.CANCEL);
      setResultConverter(dialogButton -> {
        ButtonBar.ButtonData data = dialogButton == null ? null : dialogButton.getButtonData();
        if (data == ButtonBar.ButtonData.OK_DONE) {
          return checkListView.getCheckModel().getCheckedItems();
        }
        return Collections.emptyList();
      });
    }
  }
}
