package com.mc.common.beans;

import javafx.beans.InvalidationListener;
import javafx.beans.Observable;
import javafx.beans.WeakListener;

import java.util.Arrays;

public class SimpleObservable implements Observable {
  private InvalidationListener[] invalidationListeners = null;
  private int invalidationSize;
  private boolean locked = false;

  @Override
  public void addListener(InvalidationListener listener) {
    if (invalidationListeners == null) {
      invalidationListeners = new InvalidationListener[] {listener};
      invalidationSize = 1;
    } else {
      final int oldCapacity = invalidationListeners.length;
      if (locked) {
        final int newCapacity =
            invalidationSize < oldCapacity ? oldCapacity : (oldCapacity * 3) / 2 + 1;
        invalidationListeners = Arrays.copyOf(invalidationListeners, newCapacity);
      } else if (invalidationSize == oldCapacity) {
        invalidationSize = trim(invalidationSize, invalidationListeners);
        if (invalidationSize == oldCapacity) {
          final int newCapacity = (oldCapacity * 3) / 2 + 1;
          invalidationListeners = Arrays.copyOf(invalidationListeners, newCapacity);
        }
      }
      invalidationListeners[invalidationSize++] = listener;
    }
  }

  protected static int trim(int size, Object[] listeners) {
    for (int index = 0; index < size; index++) {
      final Object listener = listeners[index];
      if (listener instanceof WeakListener && ((WeakListener) listener).wasGarbageCollected()) {
        final int numMoved = size - index - 1;
        if (numMoved > 0) {
          System.arraycopy(listeners, index + 1, listeners, index, numMoved);
        }
        listeners[--size] = null; // Let gc do its work
        index--;
      }
    }
    return size;
  }

  @Override
  public void removeListener(InvalidationListener listener) {
    if (invalidationListeners != null) {
      for (int index = 0; index < invalidationSize; index++) {
        if (listener.equals(invalidationListeners[index])) {
          final int numMoved = invalidationSize - index - 1;
          final InvalidationListener[] oldListeners = invalidationListeners;
          if (locked) {
            invalidationListeners = new InvalidationListener[invalidationListeners.length];
            System.arraycopy(oldListeners, 0, invalidationListeners, 0, index);
          }
          if (numMoved > 0) {
            System.arraycopy(oldListeners, index + 1, invalidationListeners, index, numMoved);
          }
          invalidationSize--;
          if (!locked) {
            invalidationListeners[invalidationSize] = null; // Let gc do its work
          }
          break;
        }
      }
    }
  }

  /**
   * 状态更新，通知所有的listener.
   */
  public void update() {
    final InvalidationListener[] curInvalidationList = invalidationListeners;
    final int curInvalidationSize = invalidationSize;

    try {
      locked = true;
      for (int i = 0; i < curInvalidationSize; i++) {
        try {
          curInvalidationList[i].invalidated(this);
        } catch (Exception exception) {
          Thread.currentThread().getUncaughtExceptionHandler()
              .uncaughtException(Thread.currentThread(), exception);
        }
      }
    } finally {
      locked = false;
    }
  }

}
