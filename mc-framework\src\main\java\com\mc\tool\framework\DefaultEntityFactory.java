package com.mc.tool.framework;

import com.mc.tool.framework.interfaces.Entity;
import com.mc.tool.framework.interfaces.EntityFactory;
import com.mc.tool.framework.utility.TypeWrapper;
import java.io.File;
import java.util.Arrays;
import java.util.Collection;
import java.util.function.Consumer;

/**
 * .
 */
public class DefaultEntityFactory implements EntityFactory {

  private static final String TEST_TYPE = "monitor.create.default";

  @Override
  public void createEntity(String type, Consumer<Entity> postAction) {
    if (!isSupportEntity(type)) {
      postAction.accept(null);
    } else {
      postAction.accept(new DefaultEntity("Test"));
    }
  }

  @Override
  public void createEntity(File file, Consumer<Entity> postAction) {

  }

  @Override
  public Entity searchEntity(Collection<String> types) {
    return null;
  }

  @Override
  public boolean closeEntity(Entity entity) {
    return entity.close();
  }

  @Override
  public boolean isSupportEntity(String type) {
    return type.equals(TEST_TYPE);
  }

  @Override
  public Collection<TypeWrapper> getSupportEntityTypes() {
    TypeWrapper wrapper = new TypeWrapper("Test", TEST_TYPE, "");
    return Arrays.asList(wrapper);
  }

}
