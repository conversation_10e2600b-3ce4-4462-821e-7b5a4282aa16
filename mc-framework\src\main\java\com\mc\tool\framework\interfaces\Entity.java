package com.mc.tool.framework.interfaces;

import com.mc.tool.framework.utility.TypeWrapper;
import java.util.Collection;
import javafx.beans.property.ObjectProperty;
import javafx.collections.ObservableList;
import javafx.scene.Node;
import javafx.scene.layout.Background;
import javafx.util.Pair;

/**
 * .
 */
public interface Entity {
  /**
   * Get the name of the entity.
   *
   * @return name string.
   */
  String getName();

  /**
   * Get the type of the entity.
   *
   * @return type string.
   */
  String getType();

  /**
   * Get the pages the entity has.
   *
   * @return page collection.
   */
  Collection<Page> getPages();

  /**
   * Get the current showing page.
   *
   * @return currnet showing page.
   */
  Page getCurrentPage();

  /**
   * Set the current showing page.
   *
   * @param page current showing page.
   */
  void setCurrentPage(Page page);

  /**
   * Get the page whether has the specified name.
   *
   * @param name the page name
   * @return if the page is find, return it. Or return null.
   */
  Page getPageByName(String name);

  /**
   * Check whether the entity is importable.
   *
   * @return boolean as importable.
   */
  boolean isImportable();

  /**
   * 获取可导出的类型的集合.
   *
   * @return 可导出的类型的集合
   */
  Collection<TypeWrapper> getExportableTypes();

  /**
   * Export to the path.
   *
   * @param type export type
   * @param path location to be saved.
   * @return true when export successful.
   */
  boolean exports(String type, String path);

  /**
   * Import from the path.
   *
   * @param path location to import.
   * @return true when import successful.
   */
  boolean imports(String path);

  /**
   * Close the entity.
   *
   * @return true when close successful.
   */
  boolean close();

  /**
   * 获取菜单信息.
   *
   * @return 菜单信息.
   */
  Collection<Pair<String, Collection<TypeWrapper>>> getMenuGroup();

  void onMenu(TypeWrapper menuType);

  /**
   * 获取左侧的状态节点.
   *
   * @return 左侧的状态节点.
   */
  ObservableList<Node> getLeftStatus();

  /**
   * 获取右侧的状态节点.
   *
   * @return 右侧的状态节点.
   */
  ObservableList<Node> getRightStatus();

  /**
   * 设置是否在使用.
   *
   * @param active 使用与否
   */
  void setActive(boolean active);

  /**
   * 是否在使用.
   *
   * @return 使用与否
   */
  boolean isActive();

  /**
   * 获取是否是激活板卡的图标.
   *
   * @return 背景图标
   */
  ObjectProperty<Background> getActiveBgProperty();

  /**
   * 获取是否是备用板卡的图标.
   *
   * @return 背景图标
   */
  ObjectProperty<Background> getBackupBgProperty();
}
