package com.mc.tool.framework.systemedit.datamodel;

import com.mc.graph.interfaces.ConnectorIdentifier;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;

/**
 * .
 */
public class DefaultVisualEditSubTerminal extends AbstractVisualEditNode implements VisualEditSubTerminal {

  protected ObservableList<ConnectorIdentifier> connectorIds = FXCollections.observableArrayList();

  @Override
  public String getNodeType() {
    return SystemEditDefinition.SUB_TERMINAL_CELL;
  }

  @Override
  public int getPortCount() {
    return 1;
  }

  @Override
  public ObservableList<VisualEditTerminal> getAllTerminalChild() {
    return FXCollections.emptyObservableList();
  }

  @Override
  public ObservableList<ConnectorIdentifier> getConnectorId() {
    connectorIds.add(ConnectorIdentifier.getIdentifier(VisualEditSubTerminal.CONNECTOR));
    return connectorIds;
  }

  @Override
  public void init() {
    // TODO Auto-generated method stub

  }

  @Override
  public ObservableList<VisualEditFunc> getAllFunctions() {
    return FXCollections.observableArrayList();
  }

}
