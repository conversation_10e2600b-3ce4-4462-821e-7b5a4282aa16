<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.Label?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.Region?>
<HBox xmlns="http://javafx.com/javafx/8"
      xmlns:fx="http://javafx.com/fxml/1" stylesheets="@objectlist_cell.css">
  <Region fx:id="activeRegion" prefWidth="22"/>
  <HBox HBox.hgrow="ALWAYS" alignment="CENTER">
    <Label styleClass="name-label" fx:id="nameText" alignment="center" wrapText="true"/>
  </HBox>
  <Region fx:id="backupRegion" prefWidth="22"/>
  <HBox alignment="CENTER" prefWidth="22">
    <Label id="close-btn" prefWidth="9" prefHeight="9" fx:id="closeBtn" onMousePressed="#onClose"/>
  </HBox>
</HBox>