package com.mc.tool.framework;

import com.google.common.collect.BiMap;
import com.google.gson.GsonBuilder;
import com.google.gson.graph.GraphAdapterBuilder;
import com.google.gson.typeadapters.BimapCreator;
import com.google.gson.typeadapters.RuntimeTypeAdapterFactory;
import com.mc.graph.connector.AnomymousConnectorObject;
import com.mc.graph.connector.IntegerConnectorIdentifier;
import com.mc.graph.connector.StringConnectorIdentifier;
import com.mc.graph.interfaces.ConnectorIdentifier;
import com.mc.tool.framework.interfaces.Page;
import com.mc.tool.framework.systemedit.datamodel.DefaultCrossScreenFunc;
import com.mc.tool.framework.systemedit.datamodel.DefaultSeatFunc;
import com.mc.tool.framework.systemedit.datamodel.DefaultVideoWallFunc;
import com.mc.tool.framework.systemedit.datamodel.DefaultVisualEditMatrix;
import com.mc.tool.framework.systemedit.datamodel.DefaultVisualEditTerminal;
import com.mc.tool.framework.systemedit.datamodel.InnerVisualEditGroup;
import com.mc.tool.framework.systemedit.datamodel.VisualEditGroup;
import com.mc.tool.framework.systemedit.datamodel.VisualEditMatrix;
import com.mc.tool.framework.systemedit.datamodel.VisualEditModel;
import com.mc.tool.framework.systemedit.datamodel.VisualEditNode;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import com.mc.tool.framework.utility.TypeWrapper;
import com.mc.tool.framework.utility.UndecoratedAlert;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.scene.Node;
import javafx.scene.control.AlertEx.AlertExType;
import javafx.scene.layout.Background;
import javafx.util.Pair;
import org.hildan.fxgson.FxGsonBuilder;


/**
 * .
 */
public class DefaultEntity extends AbstractVisualEditEntity {

  private String name;
  private List<Page> pages = new ArrayList<>();

  /**
   * Create a default entity.
   *
   * @param name the entity name to be shown.
   */
  public DefaultEntity(String name) {
    this.name = name;
    deserializeAndInit();
    pages.add(new DefaultPage(this, "hello", "good"));
    pages.add(new DefaultPage(this, "world", "bad"));
    pages.add(new SystemEditPage(getVisualEditModel()));
    pages.add(new OperationPage(getVisualEditModel()));

    pages.add(new OfficePage(getVisualEditModel())); // add by Jacksonxie
    pages.add(new UserPage(getVisualEditModel())); // add by Jacksonxie
  }

  @Override
  public String getName() {
    return name;
  }

  @Override
  public String getType() {
    return "monitor.create.default";
  }

  @Override
  public Collection<Page> getPages() {
    return pages;
  }

  @Override
  public boolean isImportable() {
    return false;
  }

  @Override
  public boolean exports(String type, String path) {
    return false;
  }

  @Override
  public boolean imports(String path) {
    return false;
  }

  @Override
  public Page getPageByName(String name) {
    for (Page page : pages) {
      if (page.getName().equals(name)) {
        return page;
      }
    }
    return null;
  }

  @Override
  protected GsonBuilder createModelSerializationBuilder() {
    RuntimeTypeAdapterFactory<VisualEditNode> factory = RuntimeTypeAdapterFactory
        .of(VisualEditNode.class).registerSubtype(DefaultVisualEditMatrix.class)
        .registerSubtype(DefaultVisualEditTerminal.class).registerSubtype(VisualEditGroup.class)
        .registerSubtype(DefaultVideoWallFunc.class)
        .registerSubtype(DefaultSeatFunc.class)
        .registerSubtype(DefaultCrossScreenFunc.class)
        .registerSubtype(InnerVisualEditGroup.class);
    RuntimeTypeAdapterFactory<ConnectorIdentifier> connectorFactory = RuntimeTypeAdapterFactory
        .of(ConnectorIdentifier.class).registerSubtype(IntegerConnectorIdentifier.class)
        .registerSubtype(StringConnectorIdentifier.class)
        .registerSubtype(AnomymousConnectorObject.class);
    GsonBuilder builder =
        new FxGsonBuilder().builder().excludeFieldsWithoutExposeAnnotation().setPrettyPrinting()
            .enableComplexMapKeySerialization().registerTypeAdapter(BiMap.class, new BimapCreator())
            .registerTypeAdapterFactory(connectorFactory);
    new GraphAdapterBuilder().addDelegateAdapterFactory(factory).addType(VisualEditNode.class)
        .addType(VisualEditTerminal.class).addType(VisualEditMatrix.class)
        .addType(DefaultVisualEditMatrix.class).addType(DefaultVisualEditTerminal.class)
        .addType(VisualEditGroup.class).addType(DefaultVideoWallFunc.class)
        .addType(DefaultSeatFunc.class)
        .addType(DefaultCrossScreenFunc.class)
        .addType(InnerVisualEditGroup.class)
        .addType(VisualEditModel.class).registerOn(builder);
    return builder;
  }

  @Override
  public Collection<TypeWrapper> getExportableTypes() {
    return Collections.emptyList();
  }

  @Override
  public ObservableList<Node> getLeftStatus() {
    return FXCollections.observableArrayList();
  }

  @Override
  public ObservableList<Node> getRightStatus() {
    return FXCollections.observableArrayList();
  }

  @Override
  public ObjectProperty<Background> getActiveBgProperty() {
    return new SimpleObjectProperty<>(null);
  }

  @Override
  public ObjectProperty<Background> getBackupBgProperty() {
    return new SimpleObjectProperty<>(null);
  }

  @Override
  public Collection<Pair<String, Collection<TypeWrapper>>> getMenuGroup() {
    List<Pair<String, Collection<TypeWrapper>>> result = new ArrayList<>();
    TypeWrapper wrapper = new TypeWrapper("test", "test", "");
    List<TypeWrapper> typeWrappers = new ArrayList<>();
    typeWrappers.add(wrapper);

    result.add(new Pair<>("testmenu", typeWrappers));
    return result;
  }

  @Override
  public void onMenu(TypeWrapper menuType) {
    if (menuType.getType().equals("test")) {
      UndecoratedAlert alert = new UndecoratedAlert(AlertExType.INFORMATION);
      alert.setContentText("test menu!");
      alert.showAndWait();
    }
  }

}
