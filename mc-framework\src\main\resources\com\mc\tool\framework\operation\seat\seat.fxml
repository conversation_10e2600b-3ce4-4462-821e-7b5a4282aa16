<?xml version="1.0" encoding="UTF-8"?>

<?import com.mc.common.control.gridview.GridViewEx?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.ListView?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.Region?>
<?import javafx.scene.layout.StackPane?>
<?import javafx.scene.layout.VBox?>
<fx:root type="javafx.scene.layout.VBox" xmlns="http://javafx.com/javafx/8"
         xmlns:fx="http://javafx.com/fxml/1" stylesheets="@seat.css">
  <Region prefHeight="40" minHeight="40"/>
  <VBox VBox.vgrow="ALWAYS" id="screen-list-container">
    <GridViewEx id="screen-list" fx:id="screenList" VBox.vgrow="ALWAYS"/>
  </VBox>
  <HBox prefHeight="113" minHeight="113" styleClass="scenario-list-container">
    <StackPane minWidth="44" prefWidth="44">
      <Button onAction="#onScenarioToLeft" styleClass="image-button, scenario-left-btn"/>
    </StackPane>
    <ListView fx:id="scenarioList" styleClass="scenario-list" HBox.hgrow="ALWAYS" orientation="HORIZONTAL"/>
    <StackPane minWidth="44" prefWidth="44">
      <Button onAction="#onScenarioToRight" styleClass="image-button, scenario-right-btn"/>
    </StackPane>
  </HBox>
</fx:root>