package com.mc.tool.framework.operation.crossscreen.controller;

import com.mc.common.util.WeakAdapter;
import com.mc.tool.framework.operation.crossscreen.datamodel.MultiScreenObject;
import com.mc.tool.framework.systemedit.datamodel.MultiScreenFunc;
import com.mc.tool.framework.systemedit.datamodel.SystemEditDefinition;
import com.mc.tool.framework.systemedit.datamodel.VisualEditConnection;
import com.mc.tool.framework.systemedit.datamodel.VisualEditModel;
import com.mc.tool.framework.systemedit.datamodel.VisualEditNode;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import java.io.IOException;
import java.net.URL;
import java.util.ResourceBundle;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.collections.ListChangeListener;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.scene.canvas.Canvas;
import javafx.scene.control.ContextMenu;
import javafx.scene.control.Label;
import javafx.scene.image.Image;
import javafx.scene.input.ClipboardContent;
import javafx.scene.input.DragEvent;
import javafx.scene.input.Dragboard;
import javafx.scene.input.MouseEvent;
import javafx.scene.input.TransferMode;
import javafx.scene.layout.VBox;
import javafx.util.Pair;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public abstract class MultiScreenItem<DataTypeT extends MultiScreenObject,
    FuncTypeT extends MultiScreenFunc<DataTypeT>,
    ControllableTypeT extends MultiScreenControllable<DataTypeT, FuncTypeT>>
    extends VBox implements Initializable {
  @Getter
  protected final int row;
  @Getter
  protected final int column;
  @Getter
  protected final ObjectProperty<VisualEditTerminal> target;

  protected final VisualEditModel model;

  protected final FuncTypeT func;

  protected final ControllableTypeT controllable;
  @FXML
  protected Label screenImage;
  @FXML
  protected Label screenText;
  @FXML
  protected Canvas canvas;

  protected WeakAdapter weakAdapter = null;

  protected ContextMenu menu = new ContextMenu();

  private ObjectProperty<Pair<VisualEditTerminal, String>> connectedTerminal;

  /**
   * Constructor.
   *
   * @param row          行
   * @param column       列
   * @param target       对应rx
   * @param model        数据模型
   * @param func         func
   * @param controllable controllable
   */
  public MultiScreenItem(int row, int column, ObjectProperty<VisualEditTerminal> target,
                         VisualEditModel model, FuncTypeT func, ControllableTypeT controllable) {
    this.row = row;
    this.column = column;
    this.target = target;
    this.model = model;
    this.func = func;
    this.controllable = controllable;
    ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
    FXMLLoader loader = new FXMLLoader(
        classLoader.getResource("com/mc/tool/framework/operation/crossscreen/screenitem.fxml"));
    loader.setRoot(this);
    loader.setController(this);
    try {
      loader.load();
    } catch (IOException exception) {
      log.warn("Fail to load screenitem.fxml!", exception);
    }

    setMinSize(100, 100);
  }

  protected WeakAdapter getWeakAdapter() {
    if (weakAdapter == null) {
      weakAdapter = new WeakAdapter();
    }
    return weakAdapter;
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {
    screenImage.managedProperty().bind(screenImage.visibleProperty());
    screenText.managedProperty().bind(screenText.visibleProperty());
    screenImage.visibleProperty().bind(target.isNotNull());
    screenText.visibleProperty().bind(target.isNotNull());

    updateConnectedTerminal();
    updateTextBinding();
    updateImage();

    target.addListener(getWeakAdapter().wrap((observable, oldValue, newValue) -> {
      updateConnectedTerminal();
      updateTextBinding();
    }));

    model.getConnections()
        .addListener(getWeakAdapter().wrap(
            (ListChangeListener<VisualEditConnection>) change -> updateConnectedTerminal()));

    getConnectedTerminal()
        .addListener(getWeakAdapter().wrap((obs, oldVal, newVal) -> {
          updateTextBinding();
          updateImage();
        }));

    this.setOnDragDetected(this::onDragDetected);
    this.setOnDragOver(this::onDragOver);
    this.setOnDragDropped(this::onDragDrop);
  }

  protected abstract void updateImage();

  protected ObjectProperty<Pair<VisualEditTerminal, String>> getConnectedTerminal() {
    if (connectedTerminal == null) {
      connectedTerminal = new SimpleObjectProperty<>();
    }
    return connectedTerminal;
  }

  protected void updateTextBinding() {
    if (target.get() != null) {
      if (getConnectedTerminal().get() == null || getConnectedTerminal().get().getKey() == null) {
        screenText.textProperty().bind(target.get().nameProperty());
      } else {
        VisualEditTerminal tx = getConnectedTerminal().get().getKey();
        screenText.textProperty()
            .bind(target.get().nameProperty().concat("[").concat(tx.nameProperty()).concat("]"));
      }
    } else {
      screenText.textProperty().unbind();
    }
  }

  protected abstract void updateConnectedTerminal();

  protected VisualEditTerminal getTerminal(DragEvent event) {
    if (event.getDragboard().hasString()) {
      VisualEditNode node = model.findNodeByGuid(event.getDragboard().getString());
      if (node instanceof VisualEditTerminal) {
        return (VisualEditTerminal) node;
      } else {
        return null;
      }
    } else {
      return null;
    }
  }

  protected void onDragOver(DragEvent event) {
    VisualEditTerminal terminal = getTerminal(event);
    if (terminal != null
        && (terminal.isRx() && func.isScreenMovable() || terminal.isTx() && target.get() != null)
        && controllable.isConnetable()) {
      event.acceptTransferModes(TransferMode.ANY);
    } else if (terminal == null
        && event.getDragboard().getString().equals(SystemEditDefinition.EMPTY_TERMINAL_GUID)
        && controllable.isConnetable()) {
      event.acceptTransferModes(TransferMode.ANY);
    }
    event.consume();
  }

  protected void onDragDrop(DragEvent event) {
    VisualEditTerminal terminal = getTerminal(event);
    if (terminal != null && terminal.isRx() && func.isScreenMovable()) {
      Pair<Integer, Integer> pos = func.posOfScreen(terminal);
      func.moveScreen(pos.getKey(), pos.getValue(), getRow(), getColumn());
    } else if (terminal != null && target.get() != null && terminal.isTx() && controllable
        .isConnetable()) {
      controllable.connectScreen(terminal, target.get(), "full");
    } else if (terminal == null && event.getDragboard().getString()
        .equals(SystemEditDefinition.EMPTY_TERMINAL_GUID) && controllable.isConnetable()) {
      controllable.connectScreen(null, target.get(), "disconnect");
    }
    event.setDropCompleted(true);
    event.consume();
  }

  protected void onDragDetected(MouseEvent event) {
    if (target.get() != null && func.isScreenMovable()) {
      Dragboard dbDragboard = this.startDragAndDrop(TransferMode.ANY);
      ClipboardContent content = new ClipboardContent();
      content.putString(target.get().getGuid());
      Image image = new Image(Thread.currentThread().getContextClassLoader()
          .getResourceAsStream("com/mc/tool/framework/operation/crossscreen/screen.png"));
      dbDragboard.setDragView(image, image.getWidth() / 2, image.getHeight() / 2);
      dbDragboard.setContent(content);
    }
    event.consume();
  }
}