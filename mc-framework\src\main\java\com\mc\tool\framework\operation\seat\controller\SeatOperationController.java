package com.mc.tool.framework.operation.seat.controller;

import com.mc.common.control.gridview.GridViewEx;
import com.mc.tool.framework.interfaces.DeviceControllable;
import com.mc.tool.framework.operation.controller.DefaultSnapshotGetter;
import com.mc.tool.framework.operation.interfaces.SnapshotGetter;
import com.mc.tool.framework.operation.seat.datamodel.SeatData;
import com.mc.tool.framework.operation.seat.datamodel.SeatData.SeatConnection;
import com.mc.tool.framework.operation.seat.datamodel.SeatData.SeatMode;
import com.mc.tool.framework.operation.seat.scenario.SeatScenarioCell;
import com.mc.tool.framework.operation.seat.view.SeatCell;
import com.mc.tool.framework.systemedit.datamodel.DefaultSeatFunc;
import com.mc.tool.framework.systemedit.datamodel.SystemEditDefinition;
import com.mc.tool.framework.systemedit.datamodel.VisualEditFunc;
import com.mc.tool.framework.systemedit.datamodel.VisualEditModel;
import com.mc.tool.framework.systemedit.datamodel.VisualEditNode;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import com.mc.tool.framework.systemedit.view.ViewUtility;
import java.net.URL;
import java.util.Optional;
import java.util.ResourceBundle;
import java.util.concurrent.atomic.AtomicInteger;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.beans.value.ChangeListener;
import javafx.collections.FXCollections;
import javafx.collections.ListChangeListener;
import javafx.collections.MapChangeListener;
import javafx.collections.ObservableList;
import javafx.event.ActionEvent;
import javafx.event.Event;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.ListView;
import javafx.scene.input.KeyCode;
import javafx.scene.input.KeyEvent;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class SeatOperationController implements SeatControllable, Initializable {
  protected DefaultSeatFunc func = null;
  protected VisualEditModel model = null;
  @FXML
  protected GridViewEx<SeatConnection> screenList;
  @FXML
  protected ListView<SeatData> scenarioList;

  protected ObjectProperty<SeatData> currentScenario = new SimpleObjectProperty<>();

  protected ObservableList<SeatConnection> connectionList = FXCollections.observableArrayList();

  protected final ChangeListener<VisualEditTerminal> txChangeListener;

  protected final ChangeListener<SeatMode> connModeChangeListener;

  private AtomicInteger updateCount = new AtomicInteger(0);
  private boolean needToUpdate = false;

  private SnapshotGetter snapshotGetter;

  /**
   * Constructor.
   */
  public SeatOperationController() {
    txChangeListener = (observable, oldVal, newVal) -> {
      onConnectionChange();
    };

    connModeChangeListener = (observable, oldVal, newVal) -> {
      onConnectionChange();
    };

    connectionList.addListener(new ListChangeListener<SeatConnection>() {

      @Override
      public void onChanged(ListChangeListener.Change<? extends SeatConnection> change) {
        while (change.next()) {
          for (SeatConnection conn : change.getRemoved()) {
            conn.getTx().removeListener(txChangeListener);
            conn.getMode().removeListener(connModeChangeListener);
          }

          for (SeatConnection conn : change.getAddedSubList()) {
            conn.getTx().addListener(txChangeListener);
            conn.getMode().removeListener(connModeChangeListener);
          }
        }
        onConnectionChange();
      }
    });
  }

  @Override
  public void setDeviceController(DeviceControllable deviceController) {
  }

  @Override
  public void onConfig() {

  }

  @Override
  public void init(VisualEditModel model, VisualEditFunc currentFunction) {
    this.model = model;
    this.snapshotGetter = new DefaultSnapshotGetter(model);
    if (currentFunction instanceof DefaultSeatFunc) {
      func = (DefaultSeatFunc) currentFunction;
      connectionList.setAll(func.getConnections());
      func.getSeatData().getConnections()
          .addListener(new MapChangeListener<VisualEditTerminal, SeatConnection>() {

            @Override
            public void onChanged(
                MapChangeListener.Change<? extends VisualEditTerminal, ? extends SeatConnection>
                    change) {
              connectionList.setAll(func.getConnections());
            }
          });
    } else {
      log.warn("Error function type!");
    }
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {
    screenList.setCellFactory((view) -> {
      return new SeatCell(this, snapshotGetter);
    });

    screenList.setItems(connectionList);

    scenarioList.setItems(func.getScenarios());
    scenarioList.setCellFactory((list) -> new SeatScenarioCell(this));
  }

  @Override
  public void beginUpdate() {
    updateCount.incrementAndGet();
  }

  @Override
  public void endUpdate() {
    int count = updateCount.decrementAndGet();
    if (count <= 0) {
      updateCount.set(0);
      if (needToUpdate) {
        onConnectionChange();
      }
    }
  }

  protected void onConnectionChange() {
    checkUpdate();
  }

  protected boolean checkUpdate() {
    if (updateCount.get() > 0) {
      needToUpdate = true;
      return false;
    } else {
      needToUpdate = false;
      return true;
    }
  }

  @Override
  public VisualEditTerminal getTxByGuid(String guid) {
    VisualEditNode node = model.findNodeByGuid(guid);
    if (!(node instanceof VisualEditTerminal)
        && !guid.equals(SystemEditDefinition.EMPTY_TERMINAL_GUID)) {
      log.warn("The dragged node is not terminal!");
      return null;
    }
    VisualEditTerminal terminal = (VisualEditTerminal) node;
    if (terminal != null && !terminal.isTx()) {
      log.warn("The terminal is not tx!");
      return null;
    }
    return terminal;
  }

  @Override
  public void saveScenario() {
    if (!canSaveScenario()) {
      return;
    }

    if (currentScenario.get() != null && func.getScenarios().contains(currentScenario.get())) {
      String name = currentScenario.get().getName().get();
      func.getSeatData().copyTo(currentScenario.get());
      currentScenario.get().getName().set(name);
    } else {
      saveAsScenario();
    }
  }

  @Override
  public void saveAsScenario() {
    if (!canSaveScenario()) {
      return;
    }
    Optional<String> result =
        ViewUtility.getNameFromDialog(scenarioList.getScene().getWindow(), "");
    if (result.isPresent()) {
      String name = result.get();
      SeatData seatData = new SeatData();
      func.getSeatData().copyTo(seatData);
      seatData.getName().set(name);
      func.getScenarios().add(seatData);
      currentScenario.set(seatData);
    }
  }

  @Override
  public boolean isConfigable() {
    return false;
  }

  @Override
  public boolean canSaveScenario() {
    return getDeviceController() == null
        || getDeviceController().getUserRight().isSeatScenarioCreateDeletable();
  }

  @FXML
  protected void onScenarioToLeft(ActionEvent event) {
    Event.fireEvent(scenarioList,
        new KeyEvent(KeyEvent.KEY_PRESSED, "", "", KeyCode.PAGE_UP, false, false, false, false));
  }

  @FXML
  protected void onScenarioToRight(ActionEvent event) {
    Event.fireEvent(scenarioList,
        new KeyEvent(KeyEvent.KEY_PRESSED, "", "", KeyCode.PAGE_DOWN, false, false, false, false));
  }

  @Override
  public void activeScenario(SeatData scenario) {
    if (scenario == null) {
      return;
    }
    beginUpdate();
    scenario.copyTo(func.getSeatData());
    endUpdate();
    currentScenario.set(scenario);
  }

  @Override
  public ObjectProperty<SeatData> currentScenarioProperty() {
    return currentScenario;
  }

  @Override
  public boolean hasScenario(SeatData scenario) {
    return func.getScenarios().contains(scenario);
  }

  @Override
  public void deleteScenario(SeatData scenario) {
    func.getScenarios().remove(scenario);
  }

  @Override
  public DeviceControllable getDeviceController() {
    return null;
  }
}
