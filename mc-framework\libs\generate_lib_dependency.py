#-*- coding=utf-8 -*-
import json
jsonfile="libs.json"
dependency_string=u'<dependency>\n\t<groupId>%s</groupId>\n\t<artifactId>%s</artifactId>\n\t<version>%s</version>\n</dependency>'

file = open(jsonfile, 'r')
content = json.load(file)
libs = content[u'libs']
dependencies = ''
for lib in libs:
	file_path =  lib[u'file']
	group=lib.get(u'group')
	artifact=lib.get(u'artifact')
	version=lib.get(u'version')
	mvn_ver = lib.get(u'mvn_ver')
	pom = lib.get(u'pom')
	if mvn_ver is None:
		continue
	dependency = dependency_string%(group, artifact, mvn_ver)
	dependencies += dependency + '\n'
print dependencies
