package com.mc.tool.framework.operation.videowall.menu;

import com.mc.tool.framework.operation.videowall.controller.VideoWallControllable;
import com.mc.tool.framework.operation.videowall.datamodel.interfaces.VideoObject;
import com.mc.tool.framework.utility.I18nUtility;
import java.util.Collection;

/**
 * .
 */
public class MenuAlign extends VideoWallMenuBase {
  private AlignType type;

  /**
   * Constructor.
   *
   * @param controllable controllable
   * @param type         type
   */
  public MenuAlign(VideoWallControllable controllable, AlignType type) {
    super(controllable);
    this.type = type;
    setText(type.getName());
  }

  @Override
  protected void onAction() {
    Collection<VideoObject> datas = controllable.getSelectedVideos();
    if (datas.size() <= 1) {
      return;
    }

    controllable.beginUpdate();
    try {
      switch (type) {
        case LEFT_ALIGN:
          alignLeft(datas);
          break;
        case RIGHT_ALIGN:
          alignRight(datas);
          break;
        case TOP_ALIGN:
          alignTop(datas);
          break;
        case BOTTOM_ALIGN:
          alignBottom(datas);
          break;
        case VERT_CENTER_ALIGN:
          alignVertCenter(datas);
          break;
        case HORZ_CENTER_ALIGN:
          alignHorzCenter(datas);
          break;
        default:
          break;
      }
    } finally {
      controllable.endUpdate();
    }
  }

  protected void alignLeft(Collection<VideoObject> datas) {
    int left = getMinLeft(datas);
    for (VideoObject data : datas) {
      data.getXpos().set(left);
    }
  }

  protected void alignRight(Collection<VideoObject> datas) {
    int right = getMaxRight(datas);
    for (VideoObject data : datas) {
      data.getXpos().set(right - data.getWidth().get());
    }
  }

  protected void alignTop(Collection<VideoObject> datas) {
    int top = getMinTop(datas);
    for (VideoObject data : datas) {
      data.getYpos().set(top);
    }
  }

  protected void alignBottom(Collection<VideoObject> datas) {
    int bottom = getMaxBottom(datas);
    for (VideoObject data : datas) {
      data.getYpos().set(bottom - data.getHeight().get());
    }
  }

  protected void alignVertCenter(Collection<VideoObject> datas) {
    int top = getMinTop(datas);
    int bottom = getMaxBottom(datas);
    int ypos = (top + bottom) / 2;
    for (VideoObject data : datas) {
      data.getYpos().set(ypos - data.getHeight().get() / 2);
    }
  }

  protected void alignHorzCenter(Collection<VideoObject> datas) {
    int left = getMinLeft(datas);
    int right = getMaxRight(datas);
    int xpos = (left + right) / 2;
    for (VideoObject data : datas) {
      data.getXpos().set(xpos - data.getWidth().get() / 2);
    }
  }

  protected int getMinLeft(Collection<VideoObject> datas) {
    int left = Integer.MAX_VALUE;
    for (VideoObject videoData : datas) {
      if (videoData.getXpos().get() < left) {
        left = videoData.getXpos().get();
      }
    }
    return left;
  }

  protected int getMaxRight(Collection<VideoObject> datas) {
    int right = Integer.MIN_VALUE;
    for (VideoObject videoData : datas) {
      int currentRight = videoData.getXpos().get() + videoData.getWidth().get();
      if (currentRight > right) {
        right = currentRight;
      }
    }
    return right;
  }

  protected int getMinTop(Collection<VideoObject> datas) {
    int top = Integer.MAX_VALUE;
    for (VideoObject videoData : datas) {
      if (videoData.getYpos().get() < top) {
        top = videoData.getYpos().get();
      }
    }
    return top;
  }

  protected int getMaxBottom(Collection<VideoObject> datas) {
    int bottom = Integer.MIN_VALUE;
    for (VideoObject videoData : datas) {
      int currentBottom = videoData.getYpos().get() + videoData.getHeight().get();
      if (currentBottom > bottom) {
        bottom = currentBottom;
      }
    }
    return bottom;
  }

  @Override
  protected String getMenuText() {
    return null;
  }

  /**
   * .
   */
  public enum AlignType {
    LEFT_ALIGN(I18nUtility.getI18nBundle("operation").getString("menu.align.left")), RIGHT_ALIGN(
        I18nUtility.getI18nBundle("operation").getString("menu.align.right")), TOP_ALIGN(
        I18nUtility.getI18nBundle("operation").getString("menu.align.top")), BOTTOM_ALIGN(
        I18nUtility.getI18nBundle("operation").getString("menu.align.bottom")), VERT_CENTER_ALIGN(
        I18nUtility.getI18nBundle("operation").getString("menu.align.vert_center")), HORZ_CENTER_ALIGN(
        I18nUtility.getI18nBundle("operation").getString("menu.align.horz_center"));

    private String name;

    AlignType(String name) {
      this.name = name;
    }

    public String getName() {
      return name;
    }
  }
}
