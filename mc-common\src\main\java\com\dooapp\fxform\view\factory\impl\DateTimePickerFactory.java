package com.dooapp.fxform.view.factory.impl;

import com.dooapp.fxform.view.FXFormNode;
import com.dooapp.fxform.view.FXFormNodeWrapper;
import javafx.util.Callback;
import tornadofx.control.DateTimePicker;

public class DateTimePickerFactory implements Callback<Void, FXFormNode> {

  @Override
  public FXFormNode call(Void avoid) {
    DateTimePicker dateTimePicker = new DateTimePicker();
    return new FXFormNodeWrapper(dateTimePicker, dateTimePicker.dateTimeValueProperty());
  }
}
