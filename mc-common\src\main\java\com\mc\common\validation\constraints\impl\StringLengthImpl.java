package com.mc.common.validation.constraints.impl;

import com.mc.common.validation.constraints.StringLength;

import java.io.UnsupportedEncodingException;
import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

public class StringLengthImpl implements ConstraintValidator<StringLength, String> {

  private int max;

  @Override
  public void initialize(StringLength constraintAnnotation) {
    max = constraintAnnotation.max();

  }

  @Override
  public boolean isValid(String value, ConstraintValidatorContext context) {
    if (value == null) {
      return false;
    }
    try {
      return value.getBytes("utf-8").length <= max;
    } catch (UnsupportedEncodingException ex) {
      ex.printStackTrace();
    }
    return false;
  }



}
