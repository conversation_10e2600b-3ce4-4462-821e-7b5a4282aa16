package org.controlsfx.control;

import org.controlsfx.control.PropertySheet.Item;

import java.io.Serializable;
import java.util.Comparator;

public class PropertyItemComparator implements Comparator<Item> , Serializable {

  private static final long serialVersionUID = 3941149122177745241L;

  @Override
  public int compare(Item o1, Item o2) {
    return o1.getName().compareTo(o2.getName());
  }

}
