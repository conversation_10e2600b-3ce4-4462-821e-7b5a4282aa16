/*
 * Copyright (c) 2014, 2015, Oracle and/or its affiliates. All rights reserved. ORACLE
 * PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 */

package javafx.scene.control;

import com.sun.javafx.event.EventHandlerManager;
import com.sun.javafx.tk.Toolkit;
import javafx.beans.InvalidationListener;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.ReadOnlyBooleanProperty;
import javafx.beans.property.ReadOnlyDoubleProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.beans.property.StringProperty;
import javafx.collections.ListChangeListener;
import javafx.css.PseudoClass;
import javafx.event.Event;
import javafx.event.EventDispatchChain;
import javafx.event.EventHandler;
import javafx.event.EventTarget;
import javafx.scene.Node;
import javafx.scene.control.ButtonBar.ButtonData;
import javafx.stage.Modality;
import javafx.stage.Stage;
import javafx.stage.StageStyle;
import javafx.stage.Window;
import javafx.util.Callback;

import java.lang.ref.WeakReference;
import java.util.Optional;

public class DialogEx<R> implements EventTarget {

  final FxDialogEx dialog;

  private boolean isClosing;

  /**
   * Creates a dialog without a specified owner.
   */
  public DialogEx() {
    this.dialog = createDialog();
    dialogPane = new SimpleObjectProperty<DialogPaneEx>(this, "dialogPane", new DialogPaneEx()) {
      final InvalidationListener expandedListener = o -> {
        DialogPaneEx dialogPane = getDialogPane();
        if (dialogPane == null) {
          return;
        }

        final Node content = dialogPane.getExpandableContent();
        final boolean isExpanded = content != null && content.isVisible();
        setResizable(isExpanded);

        DialogEx.this.dialog.sizeToScene();
      };

      final InvalidationListener headerListener = o -> {
        updatePseudoClassState();
      };

      WeakReference<DialogPaneEx> dialogPaneRef = new WeakReference<>(null);

      @Override
      protected void invalidated() {
        DialogPaneEx oldDialogPane = dialogPaneRef.get();
        if (oldDialogPane != null) {
          // clean up
          oldDialogPane.expandedProperty().removeListener(expandedListener);
          oldDialogPane.headerProperty().removeListener(headerListener);
          oldDialogPane.headerTextProperty().removeListener(headerListener);
          oldDialogPane.setDialog(null);
        }

        final DialogPaneEx newDialogPane = getDialogPane();

        if (newDialogPane != null) {
          newDialogPane.setDialog(DialogEx.this);

          // if the buttons change, we dynamically update the dialog
          newDialogPane.getButtonTypes().addListener((ListChangeListener<ButtonType>) c -> {
            newDialogPane.requestLayout();
          });
          newDialogPane.expandedProperty().addListener(expandedListener);
          newDialogPane.headerProperty().addListener(headerListener);
          newDialogPane.headerTextProperty().addListener(headerListener);

          updatePseudoClassState();
          newDialogPane.requestLayout();
        }

        // push the new dialog down into the implementation for rendering
        if (newDialogPane != null) {
          dialog.setDialogPane(newDialogPane);
        }

        dialogPaneRef = new WeakReference<DialogPaneEx>(newDialogPane);
      }
    };

    setDialogPane(new DialogPaneEx());
    initModality(Modality.APPLICATION_MODAL);
  }
  
  protected FxDialogEx createDialog() {
    return new HeavyweightDialogEx(this);
  }

  /**
   * Shows the dialog but does not wait for a user response (in other words, this brings up a
   * non-blocking dialog). Users of this API must either poll the {@link #resultProperty() result
   * property}, or else add a listener to the result property to be informed of when it is set.
   * 
   * @throws IllegalStateException if this method is called on a thread other than the JavaFX
   *         Application Thread.
   */
  public final void show() {
    Toolkit.getToolkit().checkFxUserThread();

    Event.fireEvent(this, new DialogExEvent(this, DialogExEvent.DIALOGEX_SHOWING));
    if (Double.isNaN(getWidth()) && Double.isNaN(getHeight())) {
      dialog.sizeToScene();
    }

    dialog.show();

    Event.fireEvent(this, new DialogExEvent(this, DialogExEvent.DIALOGEX_SHOWN));
  }

  /**
   * Shows the dialog and waits for the user response (in other words, brings up a blocking dialog,
   * with the returned value the users input).
   * <p>
   * This method must be called on the JavaFX Application thread. Additionally, it must either be
   * called from an input event handler or from the run method of a Runnable passed to
   * {@link javafx.application.Platform#runLater Platform.runLater}. It must not be called during
   * animation or layout processing.
   * </p>
   *
   * @return An {@link Optional} that contains the {@link #resultProperty() result}. Refer to the
   *         {@link Dialog} class documentation for more detail.
   * @throws IllegalStateException if this method is called on a thread other than the JavaFX
   *         Application Thread.
   * @throws IllegalStateException if this method is called during animation or layout processing.
   */
  public final Optional<R> showAndWait() {
    Window window = getOwner();
    /**
     * fix bug #963:修复最小化时无法显示对话框的问题
     */
    if (window instanceof Stage) {
      ((Stage) window).setIconified(false);
    }
    Toolkit.getToolkit().checkFxUserThread();

    if (!Toolkit.getToolkit().canStartNestedEventLoop()) {
      throw new IllegalStateException(
          "showAndWait is not allowed during animation or layout processing");
    }

    Event.fireEvent(this, new DialogExEvent(this, DialogExEvent.DIALOGEX_SHOWING));
    if (Double.isNaN(getWidth()) && Double.isNaN(getHeight())) {
      dialog.sizeToScene();
    }


    // this is slightly odd - we fire the SHOWN event before the show()
    // call, so that users get the event before the dialog blocks
    Event.fireEvent(this, new DialogExEvent(this, DialogExEvent.DIALOGEX_SHOWN));

    dialog.showAndWait();
    return Optional.ofNullable(getResult());
  }

  /**
   * Hides the dialog.
   */
  public final void close() {
    if (isClosing) {
      return;
    }
    isClosing = true;

    final R result = getResult();

    // if the result is null and we do not have permission to close the
    // dialog, then we cancel the close request before any events are
    // even fired
    if (result == null && !dialog.requestPermissionToClose(this)) {
      isClosing = false;
      return;
    }

    // if we are here we have permission to close the dialog. However, we
    // may not have a result set to return to the user. Therefore, we need
    // to handle that before the dialog closes (especially in case the
    // dialog is blocking, in which case having a null result is really going
    // to mess up users).
    //
    // In cases where the result is null, and where the dialog has a cancel
    // button, we call into the result converter to see what to do. This is
    // used primarily to handle the requirement that the X button has the
    // same result as clicking the cancel button.
    //
    // A 'cancel button' can mean two different things (although they may
    // be the same thing):
    // 1) A button whose ButtonData is of type CANCEL_CLOSE.
    // 2) A button whose ButtonData returns true for isCancelButton().
    if (result == null) {
      ButtonType cancelButton = null;

      // we do two things here. We are primarily looking for a button with
      // ButtonData.CANCEL_CLOSE. If we find one, we use it as the result.
      // However, if we don't find one, we can also use any button that
      // is a cancel button.
      for (ButtonType button : getDialogPane().getButtonTypes()) {
        ButtonData buttonData = button.getButtonData();
        if (buttonData == null) {
          continue;
        }

        if (buttonData == ButtonData.CANCEL_CLOSE) {
          cancelButton = button;
          break;
        }
        if (buttonData.isCancelButton()) {
          cancelButton = button;
        }
      }

      impl_setResultAndClose(cancelButton, false);
    }

    // start normal closing process
    Event.fireEvent(this, new DialogExEvent(this, DialogExEvent.DIALOGEX_HIDING));

    DialogExEvent closeRequestEvent = new DialogExEvent(this, DialogExEvent.DIALOGEX_CLOSE_REQUEST);
    Event.fireEvent(this, closeRequestEvent);
    if (closeRequestEvent.isConsumed()) {
      isClosing = false;
      return;
    }

    dialog.close();

    Event.fireEvent(this, new DialogExEvent(this, DialogExEvent.DIALOGEX_HIDDEN));

    isClosing = false;
  }

  /**
   * closes the dialog.
   */
  public final void hide() {
    close();
  }

  /**
   * Specifies the modality for this dialog. This must be done prior to making the dialog visible.
   * The modality is one of: Modality.NONE, Modality.WINDOW_MODAL, or Modality.APPLICATION_MODAL.
   *
   * @param modality the modality for this dialog.
   *
   * @throws IllegalStateException if this property is set after the dialog has ever been made
   *         visible.
   *
   * @defaultValue Modality.APPLICATION_MODAL
   */
  public final void initModality(Modality modality) {
    dialog.initModality(modality);
  }

  /**
   * Retrieves the modality attribute for this dialog.
   *
   * @return the modality.
   */
  public final Modality getModality() {
    return dialog.getModality();
  }

  /**
   * Specifies the style for this dialog. This must be done prior to making the dialog visible. The
   * style is one of: StageStyle.DECORATED, StageStyle.UNDECORATED, StageStyle.TRANSPARENT,
   * StageStyle.UTILITY, or StageStyle.UNIFIED.
   *
   * @param style the style for this dialog.
   *
   * @throws IllegalStateException if this property is set after the dialog has ever been made
   *         visible.
   *
   * @defaultValue StageStyle.DECORATED
   */
  public final void initStyle(StageStyle style) {
    dialog.initStyle(style);
  }

  /**
   * Specifies the owner {@link Window} for this dialog, or null for a top-level, unowned dialog.
   * This must be done prior to making the dialog visible.
   *
   * @param window the owner {@link Window} for this dialog.
   *
   * @throws IllegalStateException if this property is set after the dialog has ever been made
   *         visible.
   *
   * @defaultValue null
   */
  public final void initOwner(Window window) {
    dialog.initOwner(window);
  }

  /**
   * Retrieves the owner Window for this dialog, or null for an unowned dialog.
   *
   * @return the owner Window.
   */
  public final Window getOwner() {
    return dialog.getOwner();
  }

  // --- dialog Pane
  /**
   * The root node of the dialog, the {@link DialogPaneEx} contains all visual elements shown in the
   * dialog. As such, it is possible to completely adjust the display of the dialog by modifying the
   * existing dialog pane or creating a new one.
   */
  private ObjectProperty<DialogPaneEx> dialogPane;

  public final ObjectProperty<DialogPaneEx> dialogPaneProperty() {
    return dialogPane;
  }

  public final DialogPaneEx getDialogPane() {
    return dialogPane.get();
  }

  public final void setDialogPane(DialogPaneEx value) {
    dialogPane.set(value);
  }


  // --- content text (forwarded from DialogPaneEx)
  /**
   * A property representing the content text for the dialog pane. The content text is lower
   * precedence than the {@link DialogPaneEx#contentProperty() content node}, meaning that if both
   * the content node and the contentText properties are set, the content text will not be displayed
   * in a default DialogPaneEx instance.
   */
  public final StringProperty contentTextProperty() {
    return getDialogPane().contentTextProperty();
  }

  /**
   * Returns the currently-set content text for this DialogPaneEx.
   */
  public final String getContentText() {
    return getDialogPane().getContentText();
  }

  /**
   * Sets the string to show in the dialog content area. Note that the content text is lower
   * precedence than the {@link DialogPaneEx#contentProperty() content node}, meaning that if both
   * the content node and the contentText properties are set, the content text will not be displayed
   * in a default DialogPaneEx instance.
   */
  public final void setContentText(String contentText) {
    getDialogPane().setContentText(contentText);
  }


  // --- header text (forwarded from DialogPaneEx)
  /**
   * A property representing the header text for the dialog pane. The header text is lower
   * precedence than the {@link DialogPaneEx#headerProperty() header node}, meaning that if both the
   * header node and the headerText properties are set, the header text will not be displayed in a
   * default DialogPaneEx instance.
   */
  public final StringProperty headerTextProperty() {
    return getDialogPane().headerTextProperty();
  }

  /**
   * Returns the currently-set header text for this DialogPaneEx.
   */
  public final String getHeaderText() {
    return getDialogPane().getHeaderText();
  }

  /**
   * Sets the string to show in the dialog header area. Note that the header text is lower
   * precedence than the {@link DialogPaneEx#headerProperty() header node}, meaning that if both the
   * header node and the headerText properties are set, the header text will not be displayed in a
   * default DialogPaneEx instance.
   */
  public final void setHeaderText(String headerText) {
    getDialogPane().setHeaderText(headerText);
  }


  // --- graphic (forwarded from DialogPaneEx)
  /**
   * The dialog graphic, presented either in the header, if one is showing, or to the left of the
   * {@link DialogPaneEx#contentProperty() content}.
   *
   * @return An ObjectProperty wrapping the current graphic.
   */
  public final ObjectProperty<Node> graphicProperty() {
    return getDialogPane().graphicProperty();
  }

  public final Node getGraphic() {
    return getDialogPane().getGraphic();
  }

  /**
   * Sets the dialog graphic, which will be displayed either in the header, if one is showing, or to
   * the left of the {@link DialogPaneEx#contentProperty() content}.
   *
   * @param graphic The new dialog graphic, or null if no graphic should be shown.
   */
  public final void setGraphic(Node graphic) {
    getDialogPane().setGraphic(graphic);
  }


  // --- result
  private final ObjectProperty<R> resultProperty = new SimpleObjectProperty<R>() {
    @Override
    protected void invalidated() {
      close();
    }
  };

  /**
   * A property representing what has been returned from the dialog. A result is generated through
   * the {@link #resultConverterProperty() result converter}, which is intended to convert from the
   * {@link ButtonType} that the user clicked on into a value of type R. Refer to the {@link Dialog}
   * class JavaDoc for more details.
   */
  public final ObjectProperty<R> resultProperty() {
    return resultProperty;
  }

  public final R getResult() {
    return resultProperty().get();
  }

  public final void setResult(R value) {
    this.resultProperty().set(value);
  }


  // --- result converter
  private final ObjectProperty<Callback<ButtonType, R>> resultConverterProperty =
      new SimpleObjectProperty<>(this, "resultConverter");

  /**
   * API to convert the {@link ButtonType} that the user clicked on into a result that can be
   * returned via the {@link #resultProperty() result} property. This is necessary as
   * {@link ButtonType} represents the visual button within the dialog, and do not know how to map
   * themselves to a valid result - that is a requirement of the dialog implementation by making use
   * of the result converter. In some cases, the result type of a Dialog subclass is ButtonType
   * (which means that the result converter can be null), but in some cases (where the result type,
   * R, is not ButtonType or Void), this callback must be specified.
   */
  public final ObjectProperty<Callback<ButtonType, R>> resultConverterProperty() {
    return resultConverterProperty;
  }

  public final Callback<ButtonType, R> getResultConverter() {
    return resultConverterProperty().get();
  }

  public final void setResultConverter(Callback<ButtonType, R> value) {
    this.resultConverterProperty().set(value);
  }


  // --- showing
  /**
   * Represents whether the dialog is currently showing.
   */
  public final ReadOnlyBooleanProperty showingProperty() {
    return dialog.showingProperty();
  }

  /**
   * Returns whether or not the dialog is showing.
   *
   * @return true if dialog is showing.
   */
  public final boolean isShowing() {
    return showingProperty().get();
  }


  // --- resizable
  /**
   * Represents whether the dialog is resizable.
   */
  public final BooleanProperty resizableProperty() {
    return dialog.resizableProperty();
  }

  /**
   * Returns whether or not the dialog is resizable.
   *
   * @return true if dialog is resizable.
   */
  public final boolean isResizable() {
    return resizableProperty().get();
  }

  /**
   * Sets whether the dialog can be resized by the user. Resizable dialogs can also be maximized (
   * maximize button becomes visible)
   *
   * @param resizable true if dialog should be resizable.
   */
  public final void setResizable(boolean resizable) {
    resizableProperty().set(resizable);
  }


  // --- width
  /**
   * Property representing the width of the dialog.
   */
  public final ReadOnlyDoubleProperty widthProperty() {
    return dialog.widthProperty();
  }

  /**
   * Returns the width of the dialog.
   */
  public final double getWidth() {
    return widthProperty().get();
  }

  /**
   * Sets the width of the dialog.
   */
  public final void setWidth(double width) {
    dialog.setWidth(width);
  }


  // --- height
  /**
   * Property representing the height of the dialog.
   */
  public final ReadOnlyDoubleProperty heightProperty() {
    return dialog.heightProperty();
  }

  /**
   * Returns the height of the dialog.
   */
  public final double getHeight() {
    return heightProperty().get();
  }

  /**
   * Sets the height of the dialog.
   */
  public final void setHeight(double height) {
    dialog.setHeight(height);
  }


  // --- title
  /**
   * Return the titleProperty of the dialog.
   */
  public final StringProperty titleProperty() {
    return this.dialog.titleProperty();
  }

  /**
   * Return the title of the dialog.
   */
  public final String getTitle() {
    return this.dialog.titleProperty().get();
  }

  /**
   * Change the Title of the dialog.
   * 
   * @param title title
   */
  public final void setTitle(String title) {
    this.dialog.titleProperty().set(title);
  }


  // --- x
  public final double getX() {
    return dialog.getX();
  }

  public final void setX(double xval) {
    dialog.setX(xval);
  }

  /**
   * The horizontal location of this {@code Dialog}. Changing this attribute will move the
   * {@code Dialog} horizontally.
   */
  public final ReadOnlyDoubleProperty xproperty() {
    return dialog.xproperty();
  }

  // --- y
  public final double getY() {
    return dialog.getY();
  }

  public final void setY(double yval) {
    dialog.setY(yval);
  }

  /**
   * The vertical location of this {@code Dialog}. Changing this attribute will move the
   * {@code Dialog} vertically.
   */
  public final ReadOnlyDoubleProperty yproperty() {
    return dialog.yproperty();
  }


  private final EventHandlerManager eventHandlerManager = new EventHandlerManager(this);

  @Override
  public EventDispatchChain buildEventDispatchChain(EventDispatchChain tail) {
    return tail.prepend(eventHandlerManager);
  }

  /**
   * Called just prior to the Dialog being shown.
   */
  private ObjectProperty<EventHandler<DialogExEvent>> onShowing;

  public final void setOnShowing(EventHandler<DialogExEvent> value) {
    onShowingProperty().set(value);
  }

  public final EventHandler<DialogExEvent> getOnShowing() {
    return onShowing == null ? null : onShowing.get();
  }

  /**
   * Get onshowing property.
   * @return onshowing property
   */
  public final ObjectProperty<EventHandler<DialogExEvent>> onShowingProperty() {
    if (onShowing == null) {
      onShowing = new SimpleObjectProperty<EventHandler<DialogExEvent>>(this, "onShowing") {
        @Override
        protected void invalidated() {
          eventHandlerManager.setEventHandler(DialogExEvent.DIALOGEX_SHOWING, get());
        }
      };
    }
    return onShowing;
  }

  /**
   * Called just after the Dialog is shown.
   */
  private ObjectProperty<EventHandler<DialogExEvent>> onShown;

  public final void setOnShown(EventHandler<DialogExEvent> value) {
    onShownProperty().set(value);
  }

  public final EventHandler<DialogExEvent> getOnShown() {
    return onShown == null ? null : onShown.get();
  }

  /**
   * Get onshownproperty.
   * @return onshown property
   */
  public final ObjectProperty<EventHandler<DialogExEvent>> onShownProperty() {
    if (onShown == null) {
      onShown = new SimpleObjectProperty<EventHandler<DialogExEvent>>(this, "onShown") {
        @Override
        protected void invalidated() {
          eventHandlerManager.setEventHandler(DialogExEvent.DIALOGEX_SHOWN, get());
        }
      };
    }
    return onShown;
  }

  /**
   * Called just prior to the Dialog being hidden.
   */
  private ObjectProperty<EventHandler<DialogExEvent>> onHiding;

  public final void setOnHiding(EventHandler<DialogExEvent> value) {
    onHidingProperty().set(value);
  }

  public final EventHandler<DialogExEvent> getOnHiding() {
    return onHiding == null ? null : onHiding.get();
  }

  /**
   * Get onhiding property.
   * @return onhiding property
   */
  public final ObjectProperty<EventHandler<DialogExEvent>> onHidingProperty() {
    if (onHiding == null) {
      onHiding = new SimpleObjectProperty<EventHandler<DialogExEvent>>(this, "onHiding") {
        @Override
        protected void invalidated() {
          eventHandlerManager.setEventHandler(DialogExEvent.DIALOGEX_HIDING, get());
        }
      };
    }
    return onHiding;
  }

  /**
   * Called just after the Dialog has been hidden. When the {@code Dialog} is hidden, this event
   * handler is invoked allowing the developer to clean up resources or perform other tasks when the
   * {@link Alert} is closed.
   */
  private ObjectProperty<EventHandler<DialogExEvent>> onHidden;

  public final void setOnHidden(EventHandler<DialogExEvent> value) {
    onHiddenProperty().set(value);
  }

  public final EventHandler<DialogExEvent> getOnHidden() {
    return onHidden == null ? null : onHidden.get();
  }

  /**
   * Get onhidden property.
   * @return onhidden property
   */
  public final ObjectProperty<EventHandler<DialogExEvent>> onHiddenProperty() {
    if (onHidden == null) {
      onHidden = new SimpleObjectProperty<EventHandler<DialogExEvent>>(this, "onHidden") {
        @Override
        protected void invalidated() {
          eventHandlerManager.setEventHandler(DialogExEvent.DIALOGEX_HIDDEN, get());
        }
      };
    }
    return onHidden;
  }

  /**
   * Called when there is an external request to close this {@code Dialog}. The installed event
   * handler can prevent dialog closing by consuming the received event.
   */
  private ObjectProperty<EventHandler<DialogExEvent>> onCloseRequest;

  public final void setOnCloseRequest(EventHandler<DialogExEvent> value) {
    onCloseRequestProperty().set(value);
  }

  public final EventHandler<DialogExEvent> getOnCloseRequest() {
    return (onCloseRequest != null) ? onCloseRequest.get() : null;
  }

  /**
   * Get oncloserequest property.
   * @return oncloserequest property
   */
  public final ObjectProperty<EventHandler<DialogExEvent>> onCloseRequestProperty() {
    if (onCloseRequest == null) {
      onCloseRequest =
          new SimpleObjectProperty<EventHandler<DialogExEvent>>(this, "onCloseRequest") {
            @Override
            protected void invalidated() {
              eventHandlerManager.setEventHandler(DialogExEvent.DIALOGEX_CLOSE_REQUEST, get());
            }
          };
    }
    return onCloseRequest;
  }



  // This code is called both in the normal and in the abnormal case (i.e.
  // both when a button is clicked and when the user forces a window closed
  // with keyboard OS-specific shortchuts or OS-native titlebar buttons).
  @SuppressWarnings("unchecked")
  void impl_setResultAndClose(ButtonType cmd, boolean close) {
    Callback<ButtonType, R> resultConverter = getResultConverter();

    R priorResultValue = getResult();
    R newResultValue = null;

    if (resultConverter == null) {
      // The choice to cast cmd to R here was a conscious decision, taking
      // into account the choices available to us. Firstly, to summarise the
      // issue, at this point here we have a null result converter, and no
      // idea how to convert the given ButtonType to R. Our options are:
      //
      // 1) We could throw an exception here, but this requires that all
      // developers who create a dialog set a result converter (at least
      // setResultConverter(buttonType -> (R) buttonType)). This is
      // non-intuitive and depends on the developer reading documentation.
      //
      // 2) We could set a default result converter in the resultConverter
      // property that does the identity conversion. This saves people from
      // having to set a default result converter, but it is a little odd
      // that the result converter is non-null by default.
      //
      // 3) We can cast the button type here, which is what we do. This means
      // that the result converter is null by default.
      //
      // In the case of option 1), developers will receive a NPE when the
      // dialog is closed, regardless of how it was closed. In the case of
      // option 2) and 3), the user unfortunately receives a ClassCastException
      // in their code. This is unfortunate as it is not immediately obvious
      // why the ClassCastException occurred, and how to resolve it. However,
      // we decided to take this later approach as it prevents the issue of
      // requiring all custom dialog developers from having to supply their
      // own result converters.
      newResultValue = (R) cmd;
    } else {
      newResultValue = resultConverter.call(cmd);
    }

    setResult(newResultValue);

    // fix for the case where we set the same result as what
    // was already set. We should still close the dialog, but
    // we need to special-case it here, as the result property
    // won't fire any event if the value won't change.
    if (close && priorResultValue == newResultValue) {
      close();
    }
  }



  private static final PseudoClass HEADER_PSEUDO_CLASS = 
      PseudoClass.getPseudoClass("header"); //$NON-NLS-1$
  private static final PseudoClass NO_HEADER_PSEUDO_CLASS = 
      PseudoClass.getPseudoClass("no-header"); //$NON-NLS-1$

  private void updatePseudoClassState() {
    DialogPaneEx dialogPane = getDialogPane();
    if (dialogPane != null) {
      final boolean hasHeader = getDialogPane().hasHeader();
      dialogPane.pseudoClassStateChanged(HEADER_PSEUDO_CLASS, hasHeader);
      dialogPane.pseudoClassStateChanged(NO_HEADER_PSEUDO_CLASS, !hasHeader);
    }
  }
}
