package com.mc.tool.framework.user.view;

import com.mc.tool.framework.systemedit.datamodel.VisualEditModel;
import com.mc.tool.framework.user.controller.UserPageController;
import com.mc.tool.framework.utility.InjectorProvider;
import java.io.IOException;
import java.net.URL;
import javafx.fxml.FXMLLoader;
import javafx.scene.layout.VBox;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public final class UserPageView extends VBox {

  @Getter
  private UserPageController controllable;

  /**
   * Constructor.
   */
  public UserPageView(VisualEditModel model) {

    try {
      controllable = InjectorProvider.getInjector().getInstance(UserPageController.class);
      controllable.initModel(model);

      URL location =
          getClass().getResource("/com/mc/tool/framework/user/user_main_view.fxml");
      FXMLLoader loader = new FXMLLoader(location);
      loader.setController(controllable);
      loader.setRoot(this);
      loader.load();
    } catch (IOException exc) {
      log.warn("Can not load user_main_view.fxml", exc);
    }

  }

}
