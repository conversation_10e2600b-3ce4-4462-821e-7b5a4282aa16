package impl.org.controlsfx.skin;

import com.mc.common.control.gridview.GridViewEx;
import javafx.geometry.HPos;
import javafx.scene.Node;

public class GridRowSkinEx<T> extends GridRowSkin<T> {

  public GridRowSkinEx(GridRow<T> control) {
    super(control);
  }

  @Override
  protected void layoutChildren(double xval, double yval, double wval, double hval) {
    double currentWidth = getSkinnable().getWidth();
    double cellWidth = getSkinnable().gridViewProperty().get().getCellWidth();
    double cellHeight = getSkinnable().gridViewProperty().get().getCellHeight();
    double horizontalCellSpacing =
        getSkinnable().gridViewProperty().get().getHorizontalCellSpacing();
    double verticalCellSpacing = getSkinnable().gridViewProperty().get().getVerticalCellSpacing();

    double xpos = 0;
    double ypos = 0;

    // This has been commented out as I removed the API from GridView until
    // a use case was created.
    HPos currentHorizontalAlignment =
        ((GridViewEx) getSkinnable().gridViewProperty().get()).getHorizontalAlignment();
    if (currentHorizontalAlignment != null) {
      if (currentHorizontalAlignment.equals(HPos.CENTER)) {
        xpos = Math.max(0, (currentWidth - computeCellWidth()) / 2);
      } else if (currentHorizontalAlignment.equals(HPos.RIGHT)) {
        xpos = currentWidth - computeCellWidth() - horizontalCellSpacing;
      }
    }

    for (Node child : getChildren()) {
      child.relocate(xpos + horizontalCellSpacing, ypos + verticalCellSpacing);
      child.resize(cellWidth, cellHeight);
      xpos = xpos + horizontalCellSpacing + cellWidth + horizontalCellSpacing;
    }
  }

  private double computeCellWidth() {
    double width = getChildren().size()
        * (getSkinnable().gridViewProperty().get().getHorizontalCellSpacing() * 2
            + getSkinnable().gridViewProperty().get().getCellWidth());
    return width;
  }


}
