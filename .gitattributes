# =================================================================
#  JavaFX Project .gitattributes
#  策略: 自动处理换行符 (跨平台推荐方案)
# =================================================================

# 自动处理所有文本文件。Git 会在提交时转换为 LF，
# 检出时转换为对应平台的换行符 (Windows 上是 CRLF, Mac/Linux 上是 LF)。
* text=auto

# --- 特殊文件处理 ---
# 强制特定文件类型使用 LF，例如 Shell 脚本。
*.sh      text eol=lf
*.bash    text eol=lf
*.py      text eol=lf
.gitattributes text eol=lf
# 对于 Windows 特有的文件，可以强制使用 CRLF
*.bat     text eol=crlf

# --- 二进制文件 ---
# 明确标记二进制文件，防止 Git 破坏它们。
*.png     binary
*.jpg     binary
*.jpeg    binary
*.gif     binary
*.jar     binary
*.ico     binary
*.keystore binary
*.exe     binary
*.zip     binary
*.tar     binary
*.gz      binary
