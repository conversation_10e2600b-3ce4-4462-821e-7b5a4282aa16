package com.mc.tool.framework.operation.crossscreen.scenario;

import com.mc.tool.framework.operation.crossscreen.controller.MultiScreenControllable;
import com.mc.tool.framework.operation.crossscreen.datamodel.MultiScreenObject;
import com.mc.tool.framework.systemedit.datamodel.MultiScreenFunc;
import com.mc.tool.framework.utility.I18nUtility;
import java.io.IOException;
import java.net.URL;
import java.util.ResourceBundle;
import javafx.beans.binding.Bindings;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.scene.control.ContextMenu;
import javafx.scene.control.Label;
import javafx.scene.control.ListCell;
import javafx.scene.control.MenuItem;
import javafx.scene.input.MouseButton;
import javafx.scene.input.MouseEvent;
import javafx.scene.layout.HBox;
import javafx.scene.layout.VBox;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class MultiScreenScenarioCell<DataTypeT extends MultiScreenObject,
    FuncTypeT extends MultiScreenFunc<DataTypeT>,
    ControllableTypeT extends MultiScreenControllable<DataTypeT, FuncTypeT>>
    extends ListCell<DataTypeT> implements Initializable {

  @FXML
  private Label scenarioNameLabel;
  @FXML
  private VBox root;
  @FXML
  private HBox nameBox;

  private ContextMenu menu = new ContextMenu();

  private final ControllableTypeT controllable;

  /**
   * Constructor.
   *
   * @param controllable controllable
   */
  public MultiScreenScenarioCell(ControllableTypeT controllable) {
    this.controllable = controllable;
    ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
    URL location = classLoader
        .getResource("com/mc/tool/framework/operation/crossscreen/scenario/scenario_cell.fxml");
    FXMLLoader loader = new FXMLLoader(location);
    loader.setController(this);
    try {
      loader.load();
    } catch (IOException exception) {
      log.warn("Fail to load scenario_cell.fxml!", exception);
    }
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {
    root.addEventHandler(MouseEvent.MOUSE_CLICKED, (event) -> {
      if (event.getButton() == MouseButton.PRIMARY && event.getClickCount() == 2) {
        boolean activatable = true;
        if (activatable) {
          controllable.activeScenario(getItem());
        }
      } else if (event.getButton() == MouseButton.SECONDARY && event.getClickCount() == 1) {
        if (menu.isShowing()) {
          menu.hide();
        } else {
          menu.getItems().clear();
          boolean deletable = controllable.getDeviceController() == null || controllable
              .getDeviceController().getUserRight().isSeatScenarioCreateDeletable();
          if (deletable) {
            MenuItem item = new MenuItem();
            item.setText(I18nUtility.getI18nBundle("operation").getString("menu.delete"));
            item.setDisable(!controllable.hasScenario(getItem()));
            item.setOnAction((menuEvent) -> controllable.deleteScenario(getItem()));
            menu.getItems().add(item);
          }
          menu.show(root.getScene().getWindow(), event.getScreenX(), event.getScreenY());
        }
      }
    });
  }

  @Override
  protected void updateItem(DataTypeT item, boolean empty) {
    super.updateItem(item, empty);
    if (empty) {
      setGraphic(null);
    } else {
      setGraphic(root);
      scenarioNameLabel.textProperty().bind(item.getName());
      root.styleProperty()
          .bind(Bindings.when(this.selectedProperty())
              .then("-fx-border-size:2;-fx-border-color:#f08519;")
              .otherwise("-fx-border-size:1;-fx-border-color:#e6e6e6;"));

      nameBox.styleProperty()
          .bind(Bindings.when(Bindings.equal(controllable.currentScenarioProperty(), getItem()))
              .then("-fx-background-color:#f08619;").otherwise("-fx-background-color:#44464a;"));
    }
  }
}
