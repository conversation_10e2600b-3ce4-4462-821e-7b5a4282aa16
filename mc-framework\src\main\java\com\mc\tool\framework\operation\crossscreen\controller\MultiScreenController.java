package com.mc.tool.framework.operation.crossscreen.controller;

import com.google.common.eventbus.Subscribe;
import com.mc.tool.framework.event.VisualEditFuncBeginUpdateEvent;
import com.mc.tool.framework.event.VisualEditFuncEndUpdateEvent;
import com.mc.tool.framework.interfaces.DeviceControllable;
import com.mc.tool.framework.operation.crossscreen.datamodel.MultiScreenObject;
import com.mc.tool.framework.operation.crossscreen.scenario.MultiScreenScenarioCell;
import com.mc.tool.framework.systemedit.datamodel.MultiScreenFunc;
import com.mc.tool.framework.systemedit.datamodel.VisualEditConnection;
import com.mc.tool.framework.systemedit.datamodel.VisualEditModel;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import com.mc.tool.framework.systemedit.view.ViewUtility;
import com.mc.tool.framework.utility.EventBusProvider;
import java.net.URL;
import java.util.Optional;
import java.util.ResourceBundle;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.event.ActionEvent;
import javafx.event.Event;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.Group;
import javafx.scene.control.Label;
import javafx.scene.control.ListView;
import javafx.scene.control.ScrollPane;
import javafx.scene.input.KeyCode;
import javafx.scene.input.KeyEvent;
import javafx.scene.input.MouseEvent;
import javafx.scene.layout.GridPane;
import javafx.scene.layout.HBox;
import javafx.scene.layout.StackPane;

/**
 * .
 */
public abstract class MultiScreenController<DataTypeT extends MultiScreenObject,
    FuncTypeT extends MultiScreenFunc<DataTypeT>,
    ControllableTypeT extends MultiScreenControllable<DataTypeT, FuncTypeT>,
    ScreenItemTypeT extends MultiScreenItem<DataTypeT, FuncTypeT, ControllableTypeT>>
    implements MultiScreenControllable<DataTypeT, FuncTypeT>, Initializable {

  public static final double MIN_SCALE = 0.5;
  public static final double MAX_SCALE = 2;
  public static final double SCALE_FACTOR = 1.1;

  private AtomicInteger updateCount = new AtomicInteger(0);
  protected FuncTypeT func = null;

  @FXML
  protected ListView<DataTypeT> scenarioList;

  @FXML
  protected GridPane screenList;

  @FXML
  protected Group screenGroup;

  @FXML
  protected StackPane screenStack;

  @FXML
  protected ScrollPane screenScroller;

  @FXML
  protected HBox scenarioListContainer;

  @FXML
  protected Label zoominBtn;

  @FXML
  protected Label zoomoutBtn;

  protected ObjectProperty<DataTypeT> currentScenario = new SimpleObjectProperty<>();

  @Override
  public void beginUpdate() {
    updateCount.incrementAndGet();
  }

  @Override
  public void endUpdate() {
    updateCount.decrementAndGet();
  }

  public boolean isUpdating() {
    return updateCount.get() != 0;
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {
    rebuildScreenList();

    func.getFuncData().getRows().addListener((obs, oldVal, newVal) -> rebuildScreenList());

    func.getFuncData().getColumns().addListener((obs, oldVal, newVal) -> rebuildScreenList());

    scenarioListContainer.managedProperty().bind(scenarioListContainer.visibleProperty());
    if (!canSaveScenario()) {
      scenarioListContainer.setVisible(false);
    }
    scenarioList.setItems(func.getScenarios());
    scenarioList.setCellFactory(
        (view) -> new MultiScreenScenarioCell<DataTypeT, FuncTypeT,
            MultiScreenControllable<DataTypeT, FuncTypeT>>(this));

    screenScroller.viewportBoundsProperty().addListener(
        (observable, oldValue, newValue) -> screenStack.setMinSize(newValue.getWidth(), newValue.getHeight()));

    zoominBtn.disableProperty().bind(screenGroup.scaleXProperty().greaterThanOrEqualTo(MAX_SCALE));
    zoomoutBtn.disableProperty().bind(screenGroup.scaleXProperty().lessThanOrEqualTo(MIN_SCALE));
  }

  private void rebuildScreenList() {
    if (!screenList.getChildren().isEmpty()) {
      screenList.getChildren().retainAll(screenList.getChildren().get(0));
    }
    screenList.setGridLinesVisible(true);
    for (int i = 0; i < func.getMaxRow(); i++) {
      for (int j = 0; j < func.getMaxColumn(); j++) {
        int index = i * func.getMaxColumn() + j;
        ObjectProperty<VisualEditTerminal> target = func.getFuncData().getTarget(index);
        ScreenItemTypeT item = createScreenItem(i, j, target);
        screenList.add(item, j, i);
      }
    }
  }

  protected abstract ScreenItemTypeT createScreenItem(int row, int column,
                                                      ObjectProperty<VisualEditTerminal> target);

  protected abstract DataTypeT createFuncData();

  @Override
  public void onConfig() {

  }

  @Override
  public void saveScenario() {
    if (!canSaveScenario()) {
      return;
    }

    if (currentScenario.get() != null && func.getScenarios().contains(currentScenario.get())) {
      String name = currentScenario.get().getName().get();
      func.getFuncData().copyTo(currentScenario.get());
      currentScenario.get().getName().set(name);
    } else {
      saveAsScenario();
    }
  }

  @Override
  public void saveAsScenario() {
    if (!canSaveScenario()) {
      return;
    }
    Optional<String> result =
        ViewUtility.getNameFromDialog(scenarioList.getScene().getWindow(), "");
    if (result.isPresent()) {
      String name = result.get();
      DataTypeT data = createFuncData();
      func.getFuncData().copyTo(data);
      data.getName().set(name);
      func.getScenarios().add(data);
      currentScenario.set(data);
    }
  }

  @Override
  public boolean isConfigable() {
    return false;
  }

  @Override
  public boolean canSaveScenario() {
    return true;
  }

  @Override
  public void setDeviceController(DeviceControllable deviceController) {

  }

  @Override
  public DeviceControllable getDeviceController() {
    return null;
  }

  @Override
  public void init(VisualEditModel model, FuncTypeT currentFunction) {
    EventBusProvider.getEventBus().register(this);
    this.func = currentFunction;
  }

  @Override
  public boolean isConnetable() {
    return true;
  }

  @FXML
  protected void onZoomin(MouseEvent event) {
    screenGroup.setScaleX(screenGroup.getScaleX() * SCALE_FACTOR);
    screenGroup.setScaleY(screenGroup.getScaleY() * SCALE_FACTOR);
  }

  @FXML
  protected void onZoomout(MouseEvent event) {
    screenGroup.setScaleX(screenGroup.getScaleX() / SCALE_FACTOR);
    screenGroup.setScaleY(screenGroup.getScaleY() / SCALE_FACTOR);
  }

  @FXML
  protected void onRestore(MouseEvent event) {
    screenGroup.setScaleX(1);
    screenGroup.setScaleY(1);
  }

  @FXML
  protected void onScenarioToLeft(ActionEvent event) {
    Event.fireEvent(scenarioList,
        new KeyEvent(KeyEvent.KEY_PRESSED, "", "", KeyCode.PAGE_UP, false, false, false, false));
  }

  @FXML
  protected void onScenarioToRight(ActionEvent event) {
    Event.fireEvent(scenarioList,
        new KeyEvent(KeyEvent.KEY_PRESSED, "", "", KeyCode.PAGE_DOWN, false, false, false, false));
  }

  @Subscribe
  protected void onFuncBeginUpdate(VisualEditFuncBeginUpdateEvent event) {
    if (event.getFunc() == func) {
      beginUpdate();
    }
  }

  @Subscribe
  protected void onFuncEndUpdate(VisualEditFuncEndUpdateEvent event) {
    if (event.getFunc() == func) {
      endUpdate();
    }
  }

  @Override
  public void close() {
    EventBusProvider.getEventBus().unregister(this);
  }

  @Override
  public boolean hasScenario(DataTypeT scenario) {
    return func.getScenarios().contains(scenario);
  }

  @Override
  public void deleteScenario(DataTypeT scenario) {
    func.getScenarios().remove(scenario);
  }

  @Override
  public ObjectProperty<DataTypeT> currentScenarioProperty() {
    return currentScenario;
  }

  @Override
  public void activeScenario(DataTypeT scenario) {
    if (scenario == null) {
      return;
    }
    beginUpdate();
    scenario.copyTo(func.getFuncData());
    // 删除当前不在group中的项
    for (ObjectProperty<VisualEditTerminal> target : func.getFuncData().getTargets()) {
      if (!func.getChildren().contains(target.get())) {
        target.set(null);
      }
    }
    func.getFuncData().getConnections().entrySet()
        .removeIf((item) -> !func.getChildren().contains(item.getKey()));
    // 建立连接
    connectScreen(func.getFuncData().getConnections().entrySet().stream().map((item) -> {
      if (item.getValue() == null) {
        return new VisualEditConnection(null, null, item.getKey(), null, "");
      } else {
        return item.getValue();
      }
    }).collect(Collectors.toList()));
    endUpdate();
    currentScenario.set(scenario);
  }

  @Override
  public void updateConnection(VisualEditTerminal rx, VisualEditConnection connection) {
    func.getFuncData().getConnections().put(rx, connection);
  }
}
