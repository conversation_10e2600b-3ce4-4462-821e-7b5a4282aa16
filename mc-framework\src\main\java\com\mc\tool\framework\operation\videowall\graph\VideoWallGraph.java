package com.mc.tool.framework.operation.videowall.graph;

import com.google.common.collect.Lists;
import com.mc.graph.McGraph;
import com.mc.graph.canvas.PageCanvas;
import com.mc.graph.handler.KeyboardHandler;
import com.mc.graph.interfaces.OverviewableGraphCanvas;
import com.mc.graph.interfaces.SkinFactory;
import com.mc.graph.util.EventHandlerGroup;
import com.mc.tool.framework.operation.interfaces.SnapshotGetter;
import javafx.geometry.Rectangle2D;
import javafx.scene.Parent;
import javafx.scene.input.MouseEvent;
import javafx.scene.paint.Color;
import lombok.Getter;

/**
 * .
 */

public class VideoWallGraph extends McGraph {
  @Getter
  private PageRectangleSelectionController pageRectangleSelectionController;

  private final SnapshotGetter snapshotGetter;

  /**
   * Constructor.
   */
  public VideoWallGraph(SnapshotGetter snapshotGetter) {
    this.snapshotGetter = snapshotGetter;
    setCellOrderable(true);
  }

  @Override
  public void init() {
    super.init();
    KeyboardHandler handler = new KeyboardHandler(this);
    handler.install();

    addPageSelectionHandler();
  }

  /**
   * 添加selection的handler.
   */
  public void addPageSelectionHandler() {
    Parent root = getCanvas().getContainer();
    EventHandlerGroup<MouseEvent> dragHandlerGroup = new EventHandlerGroup<>();
    EventHandlerGroup<MouseEvent> pressHandlerGroup = new EventHandlerGroup<>();
    EventHandlerGroup<MouseEvent> releaseHandlerGroup = new EventHandlerGroup<>();

    root.addEventHandler(MouseEvent.MOUSE_PRESSED, pressHandlerGroup);
    root.addEventHandler(MouseEvent.MOUSE_DRAGGED, dragHandlerGroup);
    root.addEventHandler(MouseEvent.MOUSE_RELEASED, releaseHandlerGroup);

    pageRectangleSelectionController = new PageRectangleSelectionController(getPageCanvas());
    pageRectangleSelectionController.apply(dragHandlerGroup, pressHandlerGroup,
        releaseHandlerGroup);
  }

  @Override
  protected SkinFactory createSkinFactory() {
    return new VideoWallSkinFactory(snapshotGetter);
  }

  @Override
  protected OverviewableGraphCanvas createGraphCanvas() {
    PageCanvas canvas = new PageCanvas();
    canvas.setAllPageAreas(Lists.newArrayList(
        new Rectangle2D(0, 0, 1920, 1080),
        new Rectangle2D(1920, 0, 1920, 1080),
        new Rectangle2D(0, 1080, 1920, 1080),
        new Rectangle2D(1920, 1080, 1920, 1080)
    ));
    canvas.setBgColor(Color.TRANSPARENT);
    canvas.setPageColor(Color.rgb(123, 122, 128, 0.5));
    canvas.setPageStrokeColor(Color.rgb(0, 0, 0, 0.5));
    canvas.setBorderColor(Color.BLACK);
    return canvas;
  }

  public PageCanvas getPageCanvas() {
    return PageCanvas.class.cast(getCanvas());
  }
}
