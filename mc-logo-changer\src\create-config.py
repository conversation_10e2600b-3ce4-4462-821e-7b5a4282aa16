import os
import sys
import json
CONFIG_OUTPUT_FILE = "{outputfile}"
CONFIG_OUTPUT_FILE_NAME = "{outputfile.name}"
CONFIG_INPUT_FILE = "{jar}"
CONFIG_MAIN_CLASS = "{mainclass}"
CONFIG_PRODUCT = "{product}"
CONFIG_TITLE = "{title}"

def main(inputfile, outputfile, oeminfojson, launch4jconfig, product):
	oeminfojson_stream = open(oeminfojson, "r")
	oeminfo = json.loads("".join(oeminfojson_stream.readlines()))
	oeminfojson_stream.close()
	
	launch4jconfig_stream = open(launch4jconfig, "r")
	launch4jconfig_info = "".join(launch4jconfig_stream.readlines())
	
	#replace info
	outputfilename = os.path.basename(outputfile)
	mainclass = ""
	if product == "caesar":
		mainclass = "com.mc.tool.caesar.vpm.CaesarApp"
	elif product == "capture":
		mainclass = "com.mc.tool.capture.vpm.CaptureApp"
	elif product == "honor":
		mainclass = "com.mc.tool.honor.vpm.HonorApp"
	
	newProduct = oeminfo["productName"]
	newTitle = oeminfo["title"]
	
	launch4jconfig_info = launch4jconfig_info.replace(CONFIG_OUTPUT_FILE, outputfile)
	launch4jconfig_info = launch4jconfig_info.replace(CONFIG_OUTPUT_FILE_NAME, outputfilename)
	launch4jconfig_info = launch4jconfig_info.replace(CONFIG_INPUT_FILE, inputfile)
	launch4jconfig_info = launch4jconfig_info.replace(CONFIG_MAIN_CLASS, mainclass)
	launch4jconfig_info = launch4jconfig_info.replace(CONFIG_PRODUCT, newProduct)
	launch4jconfig_info = launch4jconfig_info.replace(CONFIG_TITLE, newTitle)
	
	launch4jconfig_new_stream = open(launch4jconfig + ".new", "w")
	launch4jconfig_new_stream.write(launch4jconfig_info)
	launch4jconfig_new_stream.close()
	

if __name__ == "__main__":
	if len(sys.argv) != 6:
		print("sytax error!")
		exit()
	main(sys.argv[1], sys.argv[2], sys.argv[3], sys.argv[4], sys.argv[5])