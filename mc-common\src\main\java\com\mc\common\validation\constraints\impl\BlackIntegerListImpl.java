package com.mc.common.validation.constraints.impl;

import com.mc.common.validation.constraints.BlackIntegerList;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

public class BlackIntegerListImpl implements ConstraintValidator<BlackIntegerList, Integer> {
  private int[] items;

  @Override
  public void initialize(BlackIntegerList constraintAnnotation) {
    items = constraintAnnotation.blackItems();
  }

  @Override
  public boolean isValid(Integer value, ConstraintValidatorContext context) {
    if (value == null) {
      return true;
    }
    for (int item : items) {
      if (value == item) {
        return false;
      }
    }
    return true;
  }

}
