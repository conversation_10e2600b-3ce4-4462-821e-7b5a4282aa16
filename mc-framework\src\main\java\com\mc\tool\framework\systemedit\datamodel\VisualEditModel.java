package com.mc.tool.framework.systemedit.datamodel;

import com.google.gson.annotations.Expose;
import com.mc.common.beans.SimpleObservable;
import com.mc.common.util.PlatformUtility;
import com.mc.common.util.WeakAdapter;
import com.mc.graph.interfaces.ConnectorIdentifier;
import com.mc.tool.framework.office.datamodel.OfficeData;
import com.mc.tool.framework.utility.AggregatedObservableArrayList;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javafx.beans.Observable;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.beans.value.ChangeListener;
import javafx.collections.FXCollections;
import javafx.collections.ListChangeListener;
import javafx.collections.ObservableList;
import javafx.collections.ObservableMap;
import javafx.collections.transformation.FilteredListEx;
import javafx.scene.image.Image;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class VisualEditModel {
  @Expose
  private ObservableList<VisualEditConnection> connections = FXCollections.observableArrayList();

  private ObservableList<VisualEditConnection> readOnlyConnections;
  @Expose
  private ObservableList<VisualEditNode> roots = FXCollections.observableArrayList();
  @Expose
  private ObservableList<VisualEditLink> links = FXCollections.observableArrayList();
  @Expose
  private ObservableList<OfficeData> officeFunctions = FXCollections.observableArrayList();

  private ObservableMap<VisualEditTerminal, ObjectProperty<Image>> terminalSnapshots =
      FXCollections.observableHashMap();

  private AggregatedObservableArrayList<VisualEditFunc> allFunctions =
      new AggregatedObservableArrayList<>("allFunctions");
  private ObservableList<VisualEditFunc> readOnlyAllFunctions = allFunctions.getAggregatedList();
  private ObservableList<VisualEditFunc> readOnlyOnlineFunctions;
  private ObservableList<VisualEditFunc> readOnlyOnlineOperatableFunctions;

  private AggregatedObservableArrayList<VisualEditTerminal> allTerminalsAggregated =
      new AggregatedObservableArrayList<>("allTerminalsAggregated");

  private ObservableList<VisualEditTerminal> allTerminals =
      allTerminalsAggregated.getAggregatedList();

  private Map<VisualEditTerminal, Set<VisualEditConnection>> terminal2conns = new HashMap<>();

  private ChangeListener<Boolean> onlineChangeListener;
  /**
   * 当增删terminal时或者online状态有改变时会触发.
   */
  @Getter
  private SimpleObservable terminalStatusObservable = new SimpleObservable();

  @Getter
  private SimpleObservable groupObservable = new SimpleObservable();

  private WeakAdapter weakAdapter = new WeakAdapter();

  public ObservableList<VisualEditNode> getRoots() {
    return roots;
  }

  /**
   * 复制数据到另外一个模型.
   *
   * @param model 另外一个模型
   */
  public void copyTo(VisualEditModel model) {
    if (model == this) {
      return;
    }
    model.roots.setAll(roots);
    model.connections.setAll(connections);
    model.links.setAll(links);
    model.initNodes();
    model.officeFunctions.setAll(officeFunctions);
    model.terminalStatusObservable.update();
    model.groupObservable.update();
  }

  /**
   * 初始化.
   */
  public synchronized void init() {
    // 当在线状态有改变时通知observer
    onlineChangeListener =
        weakAdapter.wrap((observalbe, oldVal, newVal) -> terminalStatusObservable.update());

    allTerminals.addListener(weakAdapter.wrap((ListChangeListener<VisualEditTerminal>) change -> {
      boolean hasAddOrRemoved = false;
      while (change.next()) {
        for (VisualEditTerminal terminal : change.getRemoved()) {
          terminal.onlineProperty().removeListener(onlineChangeListener);
          hasAddOrRemoved = true;
        }
        for (VisualEditTerminal terminal : change.getAddedSubList()) {
          terminal.onlineProperty().addListener(onlineChangeListener);
          hasAddOrRemoved = true;
        }
      }
      if (hasAddOrRemoved) {
        PlatformUtility.runInFxThreadLater(() -> terminalStatusObservable.update());
      }
    }));
    //
    initNodes();

    roots.addListener(weakAdapter.wrap((ListChangeListener<VisualEditNode>) (change) -> {
      while (change.next()) {
        for (VisualEditNode node : change.getAddedSubList()) {
          allFunctions.appendList(node.getAllFunctions());
          allTerminalsAggregated.appendList(node.getAllTerminalChild());
        }
        for (VisualEditNode node : change.getRemoved()) {
          allFunctions.removeList(node.getAllFunctions());
          allTerminalsAggregated.removeList(node.getAllTerminalChild());
        }
      }
    }));

    readOnlyConnections = FXCollections.unmodifiableObservableList(connections);

    FilteredListEx<VisualEditFunc> onlineFuncFilterList = new FilteredListEx<>(readOnlyAllFunctions,
        (func) -> {
          for (VisualEditTerminal terminal : func.getAllTerminalChild()) {
            if (terminal.isOnline()) {
              return true;
            }
          }
          return false;
        });
    onlineFuncFilterList.addDependencies(terminalStatusObservable);
    readOnlyOnlineFunctions = onlineFuncFilterList;

    readOnlyOnlineOperatableFunctions =
        readOnlyOnlineFunctions.filtered(VisualEditFunc::isOperatable);

    readOnlyAllFunctions
        .addListener(weakAdapter.wrap((ListChangeListener<VisualEditFunc>) change -> {
          List<OfficeData> removeList = new ArrayList<>();
          while (change.next()) {
            for (VisualEditFunc func : change.getRemoved()) {
              for (OfficeData data : officeFunctions) {
                if (data.getFunc().getGuid().equals(func.getGuid())) {
                  removeList.add(data);
                }
              }
            }
          }
          officeFunctions.removeAll(removeList);
        }));

    for (VisualEditConnection connection : connections) {
      addConn2Map(connection);
    }

    // listener必须添加在readonly connections，避免listener顺序错乱
    getConnections().addListener((ListChangeListener<VisualEditConnection>) (change) -> {
      while (change.next()) {
        for (VisualEditConnection connection : change.getRemoved()) {
          removeConnFromMap(connection);
        }
        for (VisualEditConnection connection : change.getAddedSubList()) {
          addConn2Map(connection);
        }
      }
    });
  }

  private void addConn2Map(VisualEditConnection connection) {
    Set<VisualEditConnection> rxConns = terminal2conns.get(connection.getRxTerminal());
    if (rxConns == null) {
      rxConns = new HashSet<>();
      terminal2conns.put(connection.getRxTerminal(), rxConns);
    }
    rxConns.add(connection);
    Set<VisualEditConnection> txConns = terminal2conns.get(connection.getTxTerminal());
    if (txConns == null) {
      txConns = new HashSet<>();
      terminal2conns.put(connection.getTxTerminal(), txConns);
    }
    txConns.add(connection);
  }

  private void removeConnFromMap(VisualEditConnection connection) {
    Set<VisualEditConnection> rxConns = terminal2conns.get(connection.getRxTerminal());
    if (rxConns != null) {
      rxConns.remove(connection);
      if (rxConns.isEmpty()) {
        terminal2conns.remove(connection.getRxTerminal());
      }
    }
    Set<VisualEditConnection> txConns = terminal2conns.get(connection.getTxTerminal());
    if (txConns != null) {
      txConns.remove(connection);
      if (txConns.isEmpty()) {
        terminal2conns.remove(connection.getTxTerminal());
      }
    }
  }

  protected synchronized void initNodes() {
    allFunctions.clearList();
    allTerminalsAggregated.clearList();
    List<VisualEditFunc> removeFuncs = new ArrayList<>();
    for (VisualEditNode node : roots) {
      node.recursiveInit();
      allFunctions.appendList(node.getAllFunctions());
      allTerminalsAggregated.appendList(node.getAllTerminalChild());

      for (VisualEditFunc func : node.getAllFunctions()) {
        if (func.getChildren().size() == 0) {
          removeFuncs.add(func);
        }
      }
    }
    for (VisualEditFunc func : removeFuncs) {
      deleteGroup(func);
    }

    List<OfficeData> removeOffice = new ArrayList<>();
    for (OfficeData officeData : officeFunctions) {
      if (!getAllFuncs().contains(officeData.getFunc())) {
        removeOffice.add(officeData);
      }
    }
    officeFunctions.removeAll(removeOffice);
  }

  public synchronized void addLink(VisualEditLink link) {
    links.add(link);
  }

  public synchronized void addOfficeFunction(OfficeData officeData) {
    officeFunctions.add(officeData);
  }

  public synchronized void clearOfficeFunction(OfficeData officeData) {
    officeFunctions.clear();
  }

  public synchronized ObservableList<OfficeData> getOfficeFunctions() {
    return officeFunctions;
  }


  public synchronized void addConnection(VisualEditConnection connection) {
    connections.add(connection);
  }

  public synchronized void setAllConnections(Collection<VisualEditConnection> connections) {
    this.connections.setAll(connections);
  }

  public synchronized void removeConnections(VisualEditConnection... connections) {
    this.connections.removeAll(Arrays.asList(connections));
  }

  public synchronized void clearConnections() {
    connections.clear();
  }

  public ObservableList<VisualEditConnection> getConnections() {
    return readOnlyConnections;
  }

  /**
   * 获取所有可用的功能组.
   *
   * @return 所有可用的功能组.
   */
  public ObservableList<VisualEditFunc> getAllOnlineOperatableFuncs() {
    return readOnlyOnlineOperatableFunctions;
  }

  public ObservableList<VisualEditFunc> getAllOnlineFuncs() {
    return readOnlyOnlineFunctions;
  }

  public ObservableList<VisualEditFunc> getAllFuncs() {
    return readOnlyAllFunctions;
  }

  public ObservableList<VisualEditTerminal> getAllTerminals() {
    return allTerminals;
  }

  public ObservableMap<VisualEditTerminal, ObjectProperty<Image>> getTerminalSnapshots() {
    return terminalSnapshots;
  }

  public synchronized void updateTerminalSnapshot(VisualEditTerminal terminal, Image image) {
    ObjectProperty<Image> imageProperty = getTerminalSnapshot(terminal);
    imageProperty.set(image);
  }

  /**
   * 获取terminal的截图.
   *
   * @param terminal 要获取截图的terminal
   * @return 截图的property
   */
  public synchronized ObjectProperty<Image> getTerminalSnapshot(VisualEditTerminal terminal) {
    if (terminal == null) {
      return new SimpleObjectProperty<>();
    }
    ObjectProperty<Image> imageProperty = terminalSnapshots.get(terminal);
    if (imageProperty == null) {
      imageProperty = new SimpleObjectProperty<>();
      terminalSnapshots.put(terminal, imageProperty);
    }
    return imageProperty;
  }

  /**
   * Add a matrix system to the model.
   *
   * @param node the root of the matrix system tree.
   */
  public synchronized void addItem(VisualEditNode node) {
    if (node.getParent() != null) {
      return;
    }

    roots.add(node);
  }

  /**
   * 通过guid来查找模型中的节点.
   *
   * @param guid 节点的guid
   * @return 如果找到，返回相应的node，否则返回null
   */
  public synchronized VisualEditNode findNodeByGuid(String guid) {
    for (VisualEditNode node : roots) {
      if (node.getGuid().equals(guid)) {
        return node;
      } else {
        VisualEditNode result = node.findChildByGuid(guid, true);
        if (result != null) {
          return result;
        }
      }
    } // end for
    return null;
  }

  /**
   * 通过名称来查找模型中的节点.
   *
   * @param name 节点的名称
   * @return 如果找到，返回相应的node，否则返回null
   */
  public synchronized VisualEditNode findNodeByName(String name) {
    for (VisualEditNode node : roots) {
      if (node.getName().equals(name)) {
        return node;
      } else {
        VisualEditNode result = node.findChildByName(name, true);
        if (result != null) {
          return result;
        }
      }
    } // end for
    return null;
  }

  /**
   * 删除组.
   *
   * @param group 组
   * @return 如果删除成功，返回true
   */
  public synchronized boolean deleteGroup(VisualEditGroup group) {
    VisualEditNode parent = group.getParent();
    if (parent == null) {
      log.warn("Group's parent is null!");
      return false;
    }
    Collection<VisualEditNode> children = group.getChildren();
    int index = parent.indexOfChild(group);
    if (index < 0) {
      group.setParent(null);
    } else {
      parent.removeAndAdd(Collections.singletonList(group), children, index);
    }
    groupObservable.update();
    return true;
  }

  /**
   * 删除终端.
   *
   * @param terminal 终端
   * @return 如果删除成功，返回true
   */
  public synchronized boolean deleteTerminal(VisualEditTerminal terminal) {
    VisualEditMatrix matrix = findMatrix(terminal);
    if (matrix == null) {
      log.warn("matrix is null!");
      return false;
    }
    matrix.removeChildren(true, true, terminal);
    terminalSnapshots.remove(terminal);
    return true;
  }

  /**
   * 查找指定节点的根节点matrix.
   *
   * @param node 找matrix的节点.
   * @return 如果找到，返回该matrix
   */
  public synchronized VisualEditMatrix findMatrix(VisualEditNode node) {
    while (node != null && !(node instanceof VisualEditMatrix)) {
      node = node.getParent();
    }
    return (VisualEditMatrix) node;
  }

  /**
   * 移动一个节点作为另外一个节点的子节点.
   *
   * @param from 要移动的节点.
   * @param to   接收移动的节点作为子节点的节点.
   * @return 如果移动成功，返回true
   */
  public synchronized boolean moveToBase(VisualEditNode from, VisualEditNode to) {
    if (from == null || to == null) {
      return false;
    }
    if (from.getParent() == to) {
      return true;
    }
    if (from.getParent() != null) {
      from.getParent().removeChildren(false, from);
    }
    to.addChildren(from);
    return true;
  }

  /**
   * 移动一个节点作为另外一个节点的子节点，并删除空group.
   *
   * @param from 要移动的节点.
   * @param to   接收移动的节点作为子节点的节点.
   * @return 如果移动成功，返回true
   */
  public synchronized boolean moveTo(VisualEditNode from, VisualEditNode to) {
    if (!moveToBase(from, to)) {
      return false;
    }
    deleteEmptyGroup(from.getParent());
    return true;
  }

  protected synchronized void deleteEmptyGroup(VisualEditNode node) {
    if (node == null) {
      return;
    }
    if (node.getChildren().size() != 0) {
      return;
    }
    if (!(node instanceof VisualEditGroup)) {
      return;
    }
    VisualEditNode parent = node.getParent();
    deleteGroup((VisualEditGroup) node);
    deleteEmptyGroup(parent);
  }

  /**
   * 添加组.
   *
   * @param groupName  组名
   * @param groupClazz 组类型
   * @param nodes      添加到组的节点.
   * @return 如果添加成功，返回添加的组的实例，否则返回null
   */
  public synchronized <T extends VisualEditGroup> T addGroup(String groupName,
                                                             Class<T> groupClazz, VisualEditNode... nodes) {
    if (nodes.length == 0) {
      return null;
    }
    // 所有的节点的父节点必须是一样的
    VisualEditNode parent = nodes[0].getParent();
    for (int i = 1; i < nodes.length; i++) {
      if (parent != nodes[i].getParent()) {
        log.warn("All nodes' parent must be the same.");
        return null;
      }
    }
    // 父节点不能为空
    if (parent == null) {
      log.warn("Node's parent can not be null.");
      return null;
    }

    // 创建group
    T newGroup;
    if (groupClazz == null) {
      return null;
    } else {
      try {
        newGroup = groupClazz.newInstance();
      } catch (InstantiationException | IllegalAccessException exception) {
        log.warn("Can not create new group !", exception);
        return null;
      }
    }
    newGroup.init();
    if (groupName != null) {
      newGroup.setName(groupName);
    } else {
      newGroup.setName("Group" + 0);
    }

    int index = parent.getChildren().size() - nodes.length;
    List<Integer> indexList = new ArrayList<>();
    for (VisualEditNode node : nodes) {
      int newIndex = parent.indexOfChild(node);
      indexList.add(newIndex);
      if (newIndex < index) {
        index = newIndex;
      }
    }
    if (index < 0) {
      log.warn(
          "Something wrong on adding group! Parent's children size is {}. "
              + "But the size of nodes to be removed is {}",
          parent.getChildren().size(), nodes.length);
      index = 0;
    }

    //根据索引排序node
    final List<VisualEditNode> sortedList = Arrays.asList(nodes);
    Map<VisualEditNode, Integer> listIndexMap = new HashMap<>();
    int listIndex = 0;
    for (VisualEditNode node : sortedList) {
      listIndexMap.put(node, listIndex);
      listIndex++;
    }

    sortedList.sort(Comparator.comparingInt(s -> {
      Integer indexOfItem = listIndexMap.get(s);
      if (indexOfItem != null && indexOfItem >= 0 && indexOfItem < indexList.size()) {
        return indexList.get(indexOfItem);
      } else {
        return -1;
      }
    }));

    listIndexMap.clear();

    nodes = sortedList.toArray(new VisualEditNode[0]);
    newGroup.addChildren(nodes);
    parent.removeAndAdd(Arrays.asList(nodes), Collections.singletonList(newGroup), index);
    groupObservable.update();
    return newGroup;
  }

  /**
   * 获取功能组可用的TX.
   *
   * @param function 功能组
   * @return 可用的TX的列表
   */
  public synchronized ObservableList<VisualEditTerminal> getFunctionAvailableTx(
      VisualEditFunc function) {
    VisualEditNode parent = function;
    while (parent.getParent() != null) {
      parent = parent.getParent();
    }
    if (parent == function) {
      log.warn("Function has no parent !");
      return FXCollections.observableArrayList();
    }
    if (parent instanceof VisualEditMatrix) {
      VisualEditMatrix matrix = (VisualEditMatrix) parent;
      FilteredListEx<VisualEditTerminal> onlineTxFilter = new FilteredListEx<>(
          matrix.getAllTxChildTerminal(), VisualEditTerminal::isOnline);
      onlineTxFilter.setExtractor((terminal) -> new Observable[] {terminal.onlineProperty()});
      return onlineTxFilter;
    } else {
      log.warn("Can not find the matrix!");
      return FXCollections.observableArrayList();
    }
  }

  /**
   * 获取与input有连接关系的所有terminal.
   *
   * @param input input terminal
   * @return 有连接关系的terminal的集合
   */
  public synchronized Collection<VisualEditTerminal> getConnectedTerminal(
      VisualEditTerminal input) {
    List<VisualEditTerminal> result = new ArrayList<>();
    if (!terminal2conns.containsKey(input)) {
      return result;
    }
    for (VisualEditConnection conn : terminal2conns.get(input)) {
      if (input.isRx() && conn.getRxTerminal() == input) {
        result.add(conn.getTxTerminal());
      } else if (input.isTx() && conn.getTxTerminal() == input) {
        result.add(conn.getRxTerminal());
      }
    }
    return result;
  }

  /**
   * 获取terminal相关的连接.
   *
   * @param input terminal
   * @return 相关的连接的集合
   */
  public synchronized Collection<VisualEditConnection> getTerminalConnections(
      VisualEditTerminal input) {
    if (terminal2conns.containsKey(input)) {
      return terminal2conns.get(input);
    } else {
      return Collections.emptyList();
    }
  }

  /**
   * 获取与input有连接关系的所有terminal与其连接的Connector.
   *
   * @param input input terminal
   * @return 有连接关系的terminal与connector的map
   */
  public synchronized Map<VisualEditTerminal, Set<ConnectorIdentifier>> getConnectedTerminalPort(
      VisualEditTerminal input) {
    Map<VisualEditTerminal, Set<ConnectorIdentifier>> result = new HashMap<>();
    if (!terminal2conns.containsKey(input)) {
      return result;
    }
    for (VisualEditConnection connection : terminal2conns.get(input)) {
      VisualEditTerminal terminal = null;
      ConnectorIdentifier connectorIdentifier = null;
      if (input.isRx() && connection.getRxTerminal() == input) {
        terminal = connection.getTxTerminal();
        connectorIdentifier = connection.getTxPort();
      } else if (input.isTx() && connection.getTxTerminal() == input) {
        terminal = connection.getRxTerminal();
        connectorIdentifier = connection.getRxPort();
      }
      if (terminal != null) {
        Set<ConnectorIdentifier> ports = result.get(terminal);
        if (ports == null) {
          ports = new HashSet<>();
        }
        ports.add(connectorIdentifier);
        result.put(terminal, ports);
      }
    }
    return result;
  }

  /**
   * 获取与input有连接关系的所有terminal与其连接模式.
   *
   * @param input input terminal
   * @return 有连接关系的terminal与连接模式的map
   */
  public synchronized Map<VisualEditTerminal, Collection<String>> getConnectedTerminalMode(
      VisualEditTerminal input) {
    Map<VisualEditTerminal, Collection<String>> result = new HashMap<>();
    if (!terminal2conns.containsKey(input)) {
      return result;
    }
    for (VisualEditConnection conn : terminal2conns.get(input)) {
      VisualEditTerminal terminal = null;
      String mode = conn.getMode();
      if (input.isRx() && conn.getRxTerminal() == input) {
        terminal = conn.getTxTerminal();
      } else if (input.isTx() && conn.getTxTerminal() == input) {
        terminal = conn.getRxTerminal();
      }

      if (terminal != null) {
        Collection<String> modes = result.get(terminal);
        if (modes == null) {
          modes = new ArrayList<>();
        }
        modes.add(mode);
        result.put(terminal, modes);
      }
    }
    return result;
  }

}
