package com.mc.tool.framework;

import com.mc.common.util.PlatformUtility;
import com.mc.tool.framework.event.BgProgressUpdateEvent;
import com.mc.tool.framework.event.FgProgressUpdateEvent;
import com.mc.tool.framework.interfaces.TaskManager;
import com.mc.tool.framework.utility.EventBusProvider;
import java.util.LinkedList;
import java.util.Queue;
import javafx.concurrent.Task;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class DefaultTaskManager implements TaskManager {

  private Queue<Task<?>> bgTaskQueue = new LinkedList<>();
  private boolean stopRunning = false;

  /**
   * Init the task thread.
   */
  public void init() {
    Thread thread = new Thread(() -> {
      runBgTask();
    });
    thread.setDaemon(true);
    thread.start();
  }

  @Override
  public boolean addBackgroundTask(Task<?> task) {
    synchronized (bgTaskQueue) {
      bgTaskQueue.add(task);
    }
    return true;
  }

  @Override
  public boolean addForegroundTask(Task<?> task) {
    Thread thread = new Thread(() -> {
      runFgTask(task);
    });
    thread.setDaemon(true);
    thread.start();
    return true;
  }

  /**
   * Run the foreground task one by one.
   */
  public void runFgTask(Task<?> task) {
    if (task != null) {
      PlatformUtility.runInFxThread(() -> {
        FgProgressUpdateEvent event = new FgProgressUpdateEvent();
        event.setProgressProperty(task.progressProperty());
        event.setTextProperty(task.messageProperty());
        EventBusProvider.getEventBus().post(event);
      });
      task.run();
      PlatformUtility.runInFxThread(() -> {
        FgProgressUpdateEvent event = new FgProgressUpdateEvent();
        event.setProgressProperty(null);
        event.setTextProperty(null);
        EventBusProvider.getEventBus().post(event);
      });
    }

    try {
      Thread.sleep(10);
    } catch (InterruptedException exp) {
      log.warn("Sleep error", exp);
    }
  }

  /**
   * Run the background task one by one.
   */
  private void runBgTask() {
    while (!stopRunning) {
      Task<?> task = null;
      synchronized (bgTaskQueue) {
        if (!bgTaskQueue.isEmpty()) {
          task = bgTaskQueue.poll();
        }
      }
      if (task != null) {
        final Task<?> finalTask = task;
        PlatformUtility.runInFxThread(() -> {
          BgProgressUpdateEvent event = new BgProgressUpdateEvent();
          event.setProgressProperty(finalTask.progressProperty());
          event.setTextProperty(finalTask.messageProperty());
          EventBusProvider.getEventBus().post(event);
        });
        task.run();
        PlatformUtility.runInFxThread(() -> {
          BgProgressUpdateEvent event = new BgProgressUpdateEvent();
          event.setProgressProperty(null);
          event.setTextProperty(null);
          EventBusProvider.getEventBus().post(event);
        });
      }
      try {
        Thread.sleep(10);
      } catch (InterruptedException exp) {
        log.warn("Sleep error", exp);
      }
    }

  }

  @Override
  public void exit() {
    stopRunning = true;
  }


}
