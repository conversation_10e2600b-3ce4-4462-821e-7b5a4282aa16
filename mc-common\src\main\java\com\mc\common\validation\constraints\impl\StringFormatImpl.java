package com.mc.common.validation.constraints.impl;

import com.mc.common.validation.constraints.StringFormat;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

public class StringFormatImpl implements ConstraintValidator<StringFormat, String> {
  private String format;

  @Override
  public void initialize(StringFormat constraintAnnotation) {
    format = constraintAnnotation.format();
  }

  @Override
  public boolean isValid(String value, ConstraintValidatorContext context) {
    if (value == null) {
      return false;
    }
    return value.matches(format);
  }

}
