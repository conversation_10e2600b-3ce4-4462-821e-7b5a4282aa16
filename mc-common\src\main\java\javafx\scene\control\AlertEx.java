/*
 * Copyright (c) 2014, Oracle and/or its affiliates. All rights reserved. ORACLE
 * PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 */

package javafx.scene.control;

import com.sun.javafx.scene.control.skin.resources.ControlResources;
import javafx.beans.InvalidationListener;
import javafx.beans.NamedArg;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.collections.ListChangeListener;
import javafx.collections.ObservableList;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class AlertEx extends DialogEx<ButtonType> {

  /**
   * An enumeration containing the available, pre-built alert types that the {@link AlertEx} class
   * can use to pre-populate various properties.
   * @since JavaFX 8u40
   */
  public enum AlertExType {
    /**
     * The NONE alert type has the effect of not setting any default properties in the AlertEx.
     */
    NONE,

    /**
     * The INFORMATION alert type configures the AlertEx dialog to appear in a way that suggests the
     * content of the dialog is informing the user of a piece of information. This includes an
     * 'information' image, an appropriate title and header, and just an OK button for the user to
     * click on to dismiss the dialog.
     */
    INFORMATION,

    /**
     * The WARNING alert type configures the AlertEx dialog to appear in a way that suggests the
     * content of the dialog is warning the user about some fact or action. This includes a
     * 'warning' image, an appropriate title and header, and just an OK button for the user to click
     * on to dismiss the dialog.
     */
    WARNING,

    /**
     * The CONFIRMATION alert type configures the AlertEx dialog to appear in a way that suggests
     * the content of the dialog is seeking confirmation from the user. This includes a
     * 'confirmation' image, an appropriate title and header, and both OK and Cancel buttons for the
     * user to click on to dismiss the dialog.
     */
    CONFIRMATION,

    /**
     * The ERROR alert type configures the AlertEx dialog to appear in a way that suggests that
     * something has gone wrong. This includes an 'error' image, an appropriate title and header,
     * and just an OK button for the user to click on to dismiss the dialog.
     */
    ERROR
  }

  private WeakReference<DialogPaneEx> dialogPaneRef;

  private boolean installingDefaults = false;
  private boolean hasCustomButtons = false;
  private boolean hasCustomTitle = false;
  private boolean hasCustomHeaderText = false;

  private final InvalidationListener headerTextListener = o -> {
    if (!installingDefaults) {
      hasCustomHeaderText = true;
    }
  };

  private final InvalidationListener titleListener = o -> {
    if (!installingDefaults) {
      hasCustomTitle = true;
    }
  };

  private final ListChangeListener<ButtonType> buttonsListener = change -> {
    if (!installingDefaults) {
      hasCustomButtons = true;
    }
  };


  /**
   * Creates an alert with the given AlertType (refer to the {@link AlertExType} documentation for
   * clarification over which one is most appropriate).
   * By passing in an AlertType, default values for the {@link #titleProperty() title},
   * {@link #headerTextProperty() headerText}, and {@link #graphicProperty() graphic} properties are
   * set, as well as the relevant {@link #getButtonTypes() buttons} being installed. Once the
   * AlertEx is instantiated, developers are able to modify the values of the alert as desired.
   * It is important to note that the one property that does not have a default value set, and which
   * therefore the developer must set, is the {@link #contentTextProperty() content text} property
   * (or alternatively, the developer may call {@code alert.getDialogPane().setContent(Node)} if
   * they want a more complex alert). If the contentText (or content) properties are not set, there
   * is no useful information presented to end users.
   */
  public AlertEx(@NamedArg("alertType") AlertExType alertType) {
    this(alertType, "");
  }

  /**
   * Creates an alert with the given contentText, ButtonTypes, and AlertType (refer to the
   * {@link AlertExType} documentation for clarification over which one is most appropriate).
   * By passing in a variable number of ButtonType arguments, the developer is directly overriding
   * the default buttons that will be displayed in the dialog, replacing the pre-defined buttons
   * with whatever is specified in the varargs array.
   * By passing in an AlertType, default values for the {@link #titleProperty() title},
   * {@link #headerTextProperty() headerText}, and {@link #graphicProperty() graphic} properties are
   * set. Once the AlertEx is instantiated, developers are able to modify the values of the alert as
   * desired.
   */
  public AlertEx(@NamedArg("alertType") AlertExType alertType,
      @NamedArg("contentText") String contentText, ButtonType... buttons) {
    super();

    final DialogPaneEx dialogPane = getDialogPane();
    dialogPane.setContentText(contentText);
    getDialogPane().getStyleClass().add("alert");

    dialogPaneRef = new WeakReference<>(dialogPane);

    hasCustomButtons = buttons != null && buttons.length > 0;
    if (hasCustomButtons) {
      for (ButtonType btnType : buttons) {
        dialogPane.getButtonTypes().addAll(btnType);
      }
    }

    setAlertType(alertType);

    // listening to property changes on Dialog and DialogPane
    dialogPaneProperty().addListener(o -> updateListeners());
    titleProperty().addListener(titleListener);
    updateListeners();
  }



  /**
   * When creating an AlertEx instance, users must pass in an {@link AlertExType} enumeration value.
   * It is by passing in this value that the AlertEx instance will configure itself appropriately
   * (by setting default values for many of the {@link Dialog} properties, including
   * {@link #titleProperty() title}, {@link #headerTextProperty() header}, and
   * {@link #graphicProperty() graphic}, as well as the default {@link #getButtonTypes() buttons}
   * that are expected in a dialog of the given type.
   */
  // --- alertType
  private final ObjectProperty<AlertExType> alertType =
      new SimpleObjectProperty<AlertExType>(null) {
        final String[] styleClasses =
            new String[] {"information", "warning", "error", "confirmation"};

        protected void invalidated() {
          String newTitle = "";
          String newHeader = "";
          // Node newGraphic = null;
          String styleClass = "";
          ButtonType[] newButtons = new ButtonType[] {ButtonType.OK};
          switch (getAlertType()) {
            case NONE: {
              newButtons = new ButtonType[] {};
              break;
            }
            case INFORMATION: {
              newTitle = ControlResources.getString("Dialog.info.title");
              newHeader = ControlResources.getString("Dialog.info.header");
              styleClass = "information";
              break;
            }
            case WARNING: {
              newTitle = ControlResources.getString("Dialog.warning.title");
              newHeader = ControlResources.getString("Dialog.warning.header");
              styleClass = "warning";
              break;
            }
            case ERROR: {
              newTitle = ControlResources.getString("Dialog.error.title");
              newHeader = ControlResources.getString("Dialog.error.header");
              styleClass = "error";
              break;
            }
            case CONFIRMATION: {
              newTitle = ControlResources.getString("Dialog.confirm.title");
              newHeader = ControlResources.getString("Dialog.confirm.header");
              styleClass = "confirmation";
              newButtons = new ButtonType[] {ButtonType.OK, ButtonType.CANCEL};
              break;
            }
            default:
              break;
          }

          installingDefaults = true;
          if (!hasCustomTitle) {
            setTitle(newTitle);
          }
          if (!hasCustomHeaderText) {
            setHeaderText(newHeader);
          }
          if (!hasCustomButtons) {
            getButtonTypes().setAll(newButtons);
          }

          // update the style class based on the alert type. We use this to
          // specify the default graphic to use (i.e. via CSS).
          DialogPaneEx dialogPane = getDialogPane();
          if (dialogPane != null) {
            List<String> toRemove = new ArrayList<>(Arrays.asList(styleClasses));
            toRemove.remove(styleClass);
            dialogPane.getStyleClass().removeAll(toRemove);
            if (!dialogPane.getStyleClass().contains(styleClass)) {
              dialogPane.getStyleClass().add(styleClass);
            }
          }

          installingDefaults = false;
        }
      };

  public final AlertExType getAlertType() {
    return alertType.get();
  }

  public final void setAlertType(AlertExType alertType) {
    this.alertType.setValue(alertType);
  }

  public final ObjectProperty<AlertExType> alertTypeProperty() {
    return alertType;
  }


  /**
   * Returns an {@link ObservableList} of all {@link ButtonType} instances that are currently set
   * inside this AlertEx instance. A ButtonType may either be one of the pre-defined types (e.g.
   * {@link ButtonType#OK}), or it may be a custom type (created via the
   * {@link ButtonType#ButtonType(String)} or
   * {@link ButtonType#ButtonType(String, javafx.scene.control.ButtonBar.ButtonData)} constructors.
   * Readers should refer to the {@link ButtonType} class documentation for more details, but at a
   * high level, each ButtonType instance is converted to a Node (although most commonly a
   * {@link Button}) via the (overridable) {@link DialogPane#createButton(ButtonType)} method on
   * {@link DialogPane}.
   */
  // --- buttonTypes
  public final ObservableList<ButtonType> getButtonTypes() {
    return getDialogPane().getButtonTypes();
  }


  private void updateListeners() {
    DialogPaneEx oldPane = dialogPaneRef.get();

    if (oldPane != null) {
      oldPane.headerTextProperty().removeListener(headerTextListener);
      oldPane.getButtonTypes().removeListener(buttonsListener);
    }

    // listen to changes to properties that would be changed by alertType being
    // changed, so that we only change values that are still at their default
    // value (i.e. the user hasn't changed them, so we are free to set them
    // to a new default value when the alertType changes).

    DialogPaneEx newPane = getDialogPane();
    if (newPane != null) {
      newPane.headerTextProperty().addListener(headerTextListener);
      newPane.getButtonTypes().addListener(buttonsListener);
    }

    dialogPaneRef = new WeakReference<DialogPaneEx>(newPane);
  }
}
