package com.mc.tool.framework.systemedit.graph;

import com.mc.graph.McGraph;
import com.mc.graph.interfaces.CellBehavior;
import com.mc.graph.interfaces.CellSkin;
import com.mc.graph.interfaces.LinkBehavior;
import com.mc.graph.interfaces.NewLinkSkin;
import com.mc.graph.interfaces.OverviewableGraphCanvas;
import com.mc.graph.interfaces.SkinFactory;
import com.mc.tool.framework.systemedit.behavior.HighLightCellBehavior;
import com.mc.tool.framework.systemedit.behavior.OrderCellBehavior;
import com.mc.tool.framework.systemedit.behavior.SystemEditNewLinkBehavior;
import com.mc.tool.framework.systemedit.controller.SystemEditControllable;
import com.mc.tool.framework.systemedit.datamodel.SystemEditDefinition;
import java.lang.ref.WeakReference;

/**
 * .
 */
public class SystemEditGraph extends McGraph {
  private final WeakReference<SystemEditControllable> controllable;

  public SystemEditGraph(SystemEditControllable controllable) {
    this.controllable = new WeakReference<>(controllable);
  }

  @Override
  protected SkinFactory createSkinFactory() {
    return new SeSkinfactory();
  }

  @Override
  protected OverviewableGraphCanvas createGraphCanvas() {
    return new SystemEditCanvas();
  }

  @Override
  public void createCellBehavior(CellSkin cellSkin) {
    super.createCellBehavior(cellSkin);

    if (cellSkin.getCell().getType().equals(SystemEditDefinition.GROUP_CELL)
        || cellSkin.getCell().getType().equals(SystemEditDefinition.TERMINAL_CELL)
        || cellSkin.getCell().getType().equals(SystemEditDefinition.VIDEO_WALL_CELL)
        || cellSkin.getCell().getType().equals(SystemEditDefinition.SEAT_CELL)
        || cellSkin.getCell().getType().equals(SystemEditDefinition.PREVIEW_CELL)
        || cellSkin.getCell().getType().equals(SystemEditDefinition.SNAPSHOT_CELL)) {
      CellBehavior orderBehavior =
          new OrderCellBehavior(this, getSystemEditControllable(), cellSkin);
      cellSkin.addCellBehavior(orderBehavior);
    }

    CellBehavior highLightBehavior = new HighLightCellBehavior(this);
    cellSkin.addCellBehavior(highLightBehavior);
  }

  @Override
  public LinkBehavior createNewLinkBehavior(NewLinkSkin linkSkin) {
    LinkBehavior behavior =
        new SystemEditNewLinkBehavior(getSystemEditControllable(), this, linkSkin);
    linkSkin.setBehavior(behavior);
    return behavior;
  }

  protected SystemEditControllable getSystemEditControllable() {
    return controllable.get();
  }
}
