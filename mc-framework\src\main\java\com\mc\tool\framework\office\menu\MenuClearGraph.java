package com.mc.tool.framework.office.menu;

import com.mc.tool.framework.office.controller.OfficeControllable;
import com.mc.tool.framework.utility.I18nUtility;
import javafx.scene.control.MenuItem;

/**
 * .
 */
public class MenuClearGraph extends MenuItem {
  private OfficeControllable controllable;

  /**
   * Constructor.
   *
   * @param controllable controllable
   */
  public MenuClearGraph(OfficeControllable controllable) {
    this.controllable = controllable;
    this.setOnAction((event) -> {
      onAction();
    });
    this.setText(I18nUtility.getI18nBundle("office").getString("menu.clear_graph"));
  }

  private void onAction() {
    controllable.deleteAll();
  }

}
