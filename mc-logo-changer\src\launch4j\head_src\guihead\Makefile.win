# Project: guihead
# Makefile created by Dev-C++ 4.9.9.2

CPP  = g++.exe
CC   = gcc.exe
WINDRES = windres.exe
RES  = 
OBJ  = ../../head/guihead.o ../../head/head.o $(RES)
LINKOBJ  = ../../head/guihead.o ../../head/head.o $(RES)
LIBS =  -L"C:/Dev-Cpp/lib" -mwindows -n  -s 
INCS =  -I"C:/Dev-Cpp/include" 
CXXINCS =  -I"C:/Dev-Cpp/lib/gcc/mingw32/3.4.2/include"  -I"C:/Dev-Cpp/include/c++/3.4.2/backward"  -I"C:/Dev-Cpp/include/c++/3.4.2/mingw32"  -I"C:/Dev-Cpp/include/c++/3.4.2"  -I"C:/Dev-Cpp/include" 
BIN  = guihead.exe
CXXFLAGS = $(CXXINCS)   -fexpensive-optimizations -O3
CFLAGS = $(INCS)   -fexpensive-optimizations -O3
RM = rm -f

.PHONY: all all-before all-after clean clean-custom

all: all-before guihead.exe all-after


clean: clean-custom
	${RM} $(OBJ) $(BIN)

$(BIN): $(OBJ)
#	$(CC) $(LINKOBJ) -o "guihead.exe" $(LIBS)

../../head/guihead.o: guihead.c
	$(CC) -c guihead.c -o ../../head/guihead.o $(CFLAGS)

../../head/head.o: ../head.c
	$(CC) -c ../head.c -o ../../head/head.o $(CFLAGS)
