package com.mc.graph;

import com.google.common.eventbus.Subscribe;

import com.mc.graph.event.EditHappenEvent;
import com.mc.graph.interfaces.UndoManager;
import com.mc.graph.interfaces.UndoableEdit;

import java.util.ArrayList;
import java.util.List;

public class DefaultUndoMananger implements UndoManager {
  private List<UndoableEdit> undoableEditList = new ArrayList<>();
  private int currentIndex = 0;
  
  @Override
  public void undo() {
    if (!canUndo()) {
      return;
    }
    
    currentIndex--;
    undoableEditList.get(currentIndex).undo();
  }

  @Override
  public void redo() {
    if (!canRedo()) {
      return;
    }
    undoableEditList.get(currentIndex).redo();
    currentIndex++;
  }

  @Override
  public boolean canUndo() {
    return currentIndex > 0;
  }

  @Override
  public boolean canRedo() {
    return undoableEditList.size() > currentIndex;
  }

  @Override
  public void clear() {
    undoableEditList.clear();
    currentIndex = 0;
  }
  
  /**
   * Process the undoable edit event.
   * @param event undoable edit happen event
   */
  @Subscribe
  public void onEditHappen(EditHappenEvent event) {
    if (undoableEditList.size() != currentIndex) {
      undoableEditList = undoableEditList.subList(0, currentIndex);
    }
    undoableEditList.add(event.getEdit());
    currentIndex++;
  }
  
}
