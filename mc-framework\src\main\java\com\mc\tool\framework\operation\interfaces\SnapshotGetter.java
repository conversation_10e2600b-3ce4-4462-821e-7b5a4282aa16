package com.mc.tool.framework.operation.interfaces;

import com.mc.graph.interfaces.CellBindedObject;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import javafx.beans.property.ObjectProperty;
import javafx.scene.image.Image;

/**
 * .
 */
public interface SnapshotGetter {
  /**
   * 获取缩略图.
   *
   * @param object 与缩略图绑定的object
   * @return 返回相应图片的property, 当不需要的时候要调用unbind
   */
  ObjectProperty<Image> getSnapshot(CellBindedObject object);

  /**
   * 获取缩略图.
   *
   * @param terminal terminal
   * @return 返回相应图片的property, 当不需要的时候要调用unbind
   */
  ObjectProperty<Image> getSnapshot(ObjectProperty<VisualEditTerminal> terminal);
}
