package com.mc.tool.framework.operation.videowall.menu;

import com.mc.tool.framework.operation.videowall.controller.VideoSplitMode;
import com.mc.tool.framework.operation.videowall.controller.VideoWallControllable;
import com.mc.tool.framework.operation.videowall.datamodel.interfaces.VideoObject;
import java.util.Collection;

/**
 * .
 */
public class MenuSplit extends VideoWallMenuBase {
  private final VideoSplitMode mode;

  /**
   * Constructor.
   *
   * @param controllable controllable
   * @param mode         split mode
   */
  public MenuSplit(VideoWallControllable controllable, VideoSplitMode mode) {
    super(controllable);
    // 只选中一个而且视频源为空时才能用
    Collection<VideoObject> videos = controllable.getSelectedVideos();
    if (controllable.getSelectedVideos().size() != 1) {
      setDisable(true);
    } else {
      VideoObject videoData = videos.iterator().next();
      if (videoData.getSource().get() != null || videoData.getWidth().get() < mode.getMinWidth()
          || videoData.getHeight().get() < mode.getMinHeight()) {
        setDisable(true);
      }
    }

    this.mode = mode;
    setText(mode.getText());
  }


  @Override
  protected void onAction() {
    Collection<VideoObject> videos = controllable.getSelectedVideos();
    VideoObject data = videos.iterator().next();
    controllable.splitVideo(data, mode);
  }

  @Override
  protected String getMenuText() {
    return "";
  }

}
