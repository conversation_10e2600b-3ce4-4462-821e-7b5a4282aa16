osd:[t(200) l(200) w(80) h(400) c(a4ff) bc(80000000) a(63) id(0x0 0x1 0x2 0x3 0x4 0x5 0x6 0x7)]
input(iw ih ow oh):[0(100 100 100 90) 1(100 100 100 92) 2(1920 2160 50 91) 3(1920 2160 50 93) 4(100 100 100 94) 5(100 100 100 95) 6(1920 2160 50 91) 7(1920 2160 50 93) ]
vert_cut(start_line, video_src):[0(0 0) 1(0 1) 2(0 2) 3(0 3) 4(0 4) 5(0 5) 6(0 6) 7(0 7) 8(0 0) 9(0 0) 10(0 0) 11(0 0) 12(0 0) 13(0 0) 14(0 0) 15(0 0) 16(0 0) 17(0 0) 18(0 0) 19(0 0) ]
horz_cut(start_px, end_px, horz_cut_cnt, vert_cut_index):[0(0 99 0 0) 1(0 99 0 1) 2(0 49 0 2) 3(0 49 0 3) 4(0 99 0 4) 5(0 99 0 5) 6(0 49 0 6) 7(0 49 0 7) 8(0 0 0 20) 9(0 0 0 20) 10(0 0 0 20) 11(0 0 0 20) 12(0 0 0 20) 13(0 0 0 20) 14(0 0 0 20) 15(0 0 0 20) 16(0 0 0 20) 17(0 0 0 20) 18(0 0 0 20) 19(0 0 0 20) ]
output(oport olayer iw ih ow oh):[0(0 0 100 90 100 90) 1(2 0 100 92 100 92) 2(0 1 50 91 50 91) 3(2 1 50 93 50 93) 4(4 0 100 94 100 94) 5(5 0 100 95 100 95) 6(0 2 50 91 50 91) 7(2 2 50 93 50 93) 8(0 6 0 0 0 0) 9(0 6 0 0 0 0) 10(0 6 0 0 0 0) 11(0 6 0 0 0 0) 12(0 6 0 0 0 0) 13(0 6 0 0 0 0) 14(0 6 0 0 0 0) 15(0 6 0 0 0 0) 16(0 6 0 0 0 0) 17(0 6 0 0 0 0) 18(0 6 0 0 0 0) 19(0 6 0 0 0 0) ]
layer(start_line start_px w h a):8[0(0 0 100 90 127) 1(0 0 50 91 127) 2(0 50 50 91 127) 12(0 0 100 92 127) 13(0 0 50 93 127) 14(0 50 50 93 127) 24(0 0 100 94 127) 30(0 0 100 95 127) ]
