package com.mc.tool.framework.controller;

import com.google.common.eventbus.Subscribe;
import com.google.inject.Inject;
import com.mc.common.util.PlatformUtility;
import com.mc.common.util.WeakAdapter;
import com.mc.tool.framework.event.BgProgressUpdateEvent;
import com.mc.tool.framework.event.FgProgressUpdateEvent;
import com.mc.tool.framework.event.PageVisibilityChangeEvent;
import com.mc.tool.framework.event.SwitchPageEvent;
import com.mc.tool.framework.interfaces.ApplicationBase;
import com.mc.tool.framework.interfaces.Entity;
import com.mc.tool.framework.interfaces.Page;
import com.mc.tool.framework.utility.EntityUtility;
import com.mc.tool.framework.utility.EventBusProvider;
import com.mc.tool.framework.utility.TypeWrapper;
import com.mc.tool.framework.view.AboutView;
import com.mc.tool.framework.view.ConfigView;
import com.mc.tool.framework.view.ObjectListView;
import java.io.File;
import java.net.URL;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.ResourceBundle;
import javafx.application.Platform;
import javafx.beans.binding.Bindings;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.ReadOnlyDoubleProperty;
import javafx.beans.property.ReadOnlyStringProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.beans.value.ChangeListener;
import javafx.collections.ListChangeListener;
import javafx.event.ActionEvent;
import javafx.event.EventHandler;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.Node;
import javafx.scene.control.Button;
import javafx.scene.control.Tab;
import javafx.scene.control.TabPane;
import javafx.scene.input.DragEvent;
import javafx.scene.input.TransferMode;
import javafx.scene.layout.HBox;
import javafx.scene.layout.VBox;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;
import org.controlsfx.control.MaskerPane;
import org.controlsfx.control.StatusBar;

/**
 * .
 */
@Slf4j
public class FrameController implements Initializable {
  @FXML
  protected TabPane pagesTabPanel;
  @FXML
  protected VBox startPage;
  @FXML
  protected HBox startButtonContainer;
  @FXML
  protected ObjectListView deviceView;
  @FXML
  protected ObjectListView monitorView;
  @FXML
  protected StatusBar statusBar;
  @FXML
  protected VBox container;
  @FXML
  protected MaskerPane maskerPane;
  @FXML
  protected MainMasterDetailPane masterDetailPane;
  @FXML
  protected Button hideShowMenuBtn;

  @Inject
  protected ApplicationBase applicationBase;

  private ListChangeListener<Node> leftStatusListener;
  private ListChangeListener<Node> rightStatusListener;


  private ObjectProperty<Entity> currentEntity = new SimpleObjectProperty<>();

  private WeakAdapter weakAdapter = new WeakAdapter();
  private ChangeListener<Boolean> visibilityChangeListener;

  /**
   * Initialize the controller.
   */
  public void initialize(URL location, ResourceBundle resources) {
    EventBusProvider.getEventBus().register(this);

    deviceView.getController().init(EntityUtility.DEVICE_TYPE,
        resources.getString("framework.objectlist.device_name"));
    deviceView.getController().initContextMenu();

    monitorView.getController().init(EntityUtility.MONITOR_TYPE,
        resources.getString("framework.objectlist.monitor_name"));
    monitorView.getController().initContextMenu();

    deviceView.getController().getSelectionModel().getSelectedItems()
        .addListener(weakAdapter.wrap(new ListChangeListener<Entity>() {

          @Override
          public void onChanged(ListChangeListener.Change<? extends Entity> change) {
            if (!deviceView.getController().getSelectionModel().getSelectedItems().isEmpty()) {
              monitorView.getController().getSelectionModel().clearSelection();
              showEntity(deviceView.getController().getSelectionModel().getSelectedItem());
            } else {
              showEntity(null);
            }
          }
        }));

    monitorView.getController().getSelectionModel().getSelectedItems()
        .addListener(weakAdapter.wrap(new ListChangeListener<Entity>() {

          @Override
          public void onChanged(ListChangeListener.Change<? extends Entity> change) {
            if (!monitorView.getController().getSelectionModel().getSelectedItems().isEmpty()) {
              deviceView.getController().getSelectionModel().clearSelection();
              showEntity(monitorView.getController().getSelectionModel().getSelectedItem());
            } else {
              showEntity(null);
            }
          }
        }));
    // 隐藏没有选项的列表
    deviceView.managedProperty().bind(deviceView.visibleProperty());
    monitorView.managedProperty().bind(monitorView.visibleProperty());
    boolean hasDeviceType = false;
    boolean hasMonitorType = false;
    for (TypeWrapper type : applicationBase.getEntityFactory().getSupportEntityTypes()) {
      if (EntityUtility.isDeviceType(type.getType())) {
        hasDeviceType = true;
      }
      if (EntityUtility.isMonitorType(type.getType())) {
        hasMonitorType = true;
      }
    }
    if (!hasDeviceType) {
      deviceView.setVisible(false);
    }
    if (!hasMonitorType) {
      monitorView.setVisible(false);
    }
    // startpage与tabpage切换显示
    pagesTabPanel.managedProperty().bind(pagesTabPanel.visibleProperty());
    startPage.managedProperty().bind(startPage.visibleProperty());
    pagesTabPanel.visibleProperty()
        .bind(Bindings.isNotEmpty(applicationBase.getEntityMananger().getAllEntity()));
    startPage.visibleProperty()
        .bind(Bindings.isEmpty(applicationBase.getEntityMananger().getAllEntity()));

    initStartPageButtons();

    pagesTabPanel.getSelectionModel().selectedItemProperty()
        .addListener(weakAdapter.wrap((observable, oldVal, newVal) -> {
          if (getCurrentEntity() == null) {
            return;
          }
          if (newVal == null) {
            getCurrentEntity().setCurrentPage(null);
          } else {
            String title = newVal.getText();
            for (Page page : getCurrentEntity().getPages()) {
              if (page.getTitle().equals(title)) {
                getCurrentEntity().setCurrentPage(page);
              }
            }
          }
        }));

    deviceView.setOnDragOver(weakAdapter.wrap((EventHandler<DragEvent>) (event) -> {
      if (!event.getDragboard().getFiles().isEmpty()) {
        event.acceptTransferModes(TransferMode.ANY);
      }
      event.consume();
    }));

    deviceView.setOnDragDropped(weakAdapter.wrap((EventHandler<DragEvent>) (event) -> {
      for (File file : event.getDragboard().getFiles()) {
        applicationBase.importFile(file);
      }
    }));

    hideShowMenuBtn.graphicProperty().addListener(weakAdapter.wrap((observable, oldVal, newVal) -> {
      if (oldVal != null) {
        oldVal.rotateProperty().unbind();
      }
      if (newVal != null) {
        newVal.rotateProperty()
            .bind(Bindings.when(masterDetailPane.showDetailNodeProperty()).then(0).otherwise(180));
      }
    }));

    hideShowMenuBtn.setOnAction(weakAdapter.wrap(this::onHideShowMenu));

    leftStatusListener = weakAdapter.wrap((ListChangeListener<Node>) (change) -> {
      if (getCurrentEntity() == null) {
        statusBar.getLeftItems().clear();
      } else {
        statusBar.getLeftItems().setAll(getCurrentEntity().getLeftStatus());
      }
    });

    rightStatusListener = weakAdapter.wrap((ListChangeListener<Node>) (change) -> {
      if (getCurrentEntity() == null) {
        statusBar.getRightItems().clear();
      } else {
        statusBar.getRightItems().setAll(getCurrentEntity().getRightStatus());
      }
    });

    updateStatusItems(null, currentEntity.get());
    currentEntity
        .addListener(weakAdapter.wrap((obs, oldVal, newVal) -> updateStatusItems(oldVal, newVal)));
  }

  protected void initStartPageButtons() {
    Collection<Pair<String, Runnable>> actions = deviceView.getController().getTypeActions();
    for (Pair<String, Runnable> action : actions) {
      Button button = new Button();
      button.getStyleClass().add("common-button");
      button.setText(action.getKey());
      Runnable runnable = action.getValue();
      button.setOnAction(e -> runnable.run());
      startButtonContainer.getChildren().add(button);
    }
  }

  /**
   * 更新状态栏的项.
   */
  private void updateStatusItems(Entity oldEntity, Entity newEntity) {
    if (oldEntity != null) {
      oldEntity.getLeftStatus().removeListener(leftStatusListener);
      oldEntity.getRightStatus().removeListener(rightStatusListener);
    }

    Entity entity = newEntity;
    if (entity == null) {
      statusBar.getLeftItems().clear();
      statusBar.getRightItems().clear();
      return;
    }

    statusBar.getLeftItems().setAll(entity.getLeftStatus());
    statusBar.getRightItems().setAll(entity.getRightStatus());
    entity.getLeftStatus().addListener(leftStatusListener);
    entity.getRightStatus().addListener(rightStatusListener);
  }

  private void showEntity(Entity entity) {
    if (currentEntity.get() == entity) {
      return;
    }
    if (visibilityChangeListener == null) {
      visibilityChangeListener = weakAdapter.wrap((obs, oldVal, newVal) -> {
        Entity temp = getCurrentEntity();
        if (temp == null) {
          return;
        }
        for (Page page : temp.getPages()) {
          if (page.getVisibleProperty() == obs) {
            onPageVisibilityChange(page, newVal);
            break;
          }
        }
      });
    }
    //删除listener
    if (currentEntity.get() != null) {
      for (Page page : currentEntity.get().getPages()) {
        page.getVisibleProperty().removeListener(visibilityChangeListener);
      }
      currentEntity.get().setActive(false);
    }
    //更新tab
    currentEntity.set(null);
    for (Tab tab : pagesTabPanel.getTabs()) {
      tab.setContent(null);
    }
    pagesTabPanel.getTabs().clear();
    if (entity == null) {
      return;
    }

    List<Tab> tabs = new ArrayList<>();
    Tab selectedTab = null;
    for (Page page : entity.getPages()) {
      page.getVisibleProperty().addListener(visibilityChangeListener);

      if (!page.getVisibleProperty().get()) {
        continue;
      }
      Tab tab = createTabByPage(page);
      tabs.add(tab);
      if (page == entity.getCurrentPage()) {
        selectedTab = tab;
      }
    }
    pagesTabPanel.getTabs().setAll(tabs);
    if (selectedTab != null) {
      pagesTabPanel.getSelectionModel().select(selectedTab);
    }
    entity.setActive(true);
    currentEntity.set(entity);
  }

  protected Tab createTabByPage(Page page) {
    Tab tab = new Tab(page.getTitle(), page.getView());
    tab.getStyleClass().add(page.getStyleClass());
    tab.setId(page.getName());
    return tab;
  }

  public Entity getCurrentEntity() {
    return currentEntity.get();
  }

  public ObjectProperty<Entity> currentEntityProperty() {
    return currentEntity;
  }

  public void onConfig() {
    ConfigView.show(container.getScene().getWindow());
  }

  public void onAbout() {
    AboutView.show(container.getScene().getWindow());
  }

  /**
   * 关闭软件.
   */
  public void onClose() {
    onDestroy();
    Platform.exit();
  }

  public void onDestroy() {
    EventBusProvider.getEventBus().unregister(this);
    pagesTabPanel.getTabs().clear();
  }

  /**
   * Show or hide the progress in status bar.
   *
   * @param event task change event.
   */
  @Subscribe
  public void onUpdateBgProgress(BgProgressUpdateEvent event) {
    Platform.runLater(() -> {
      ReadOnlyStringProperty text = event.getTextProperty();
      ReadOnlyDoubleProperty progress = event.getProgressProperty();

      if (text != null && progress != null) {
        statusBar.progressProperty().bind(progress);
        statusBar.textProperty().bind(text);
      } else {
        statusBar.progressProperty().unbind();
        statusBar.textProperty().unbind();
        statusBar.setProgress(0);
        statusBar.setText("");
      }
    });
  }

  /**
   * Show or hide the progress in the foreground. When the progress is shown, user can not operate
   * the gui.
   *
   * @param event task change event.
   */
  @Subscribe
  public void onUpdateFgProgress(FgProgressUpdateEvent event) {
    Platform.runLater(() -> {
      ReadOnlyStringProperty text = event.getTextProperty();
      ReadOnlyDoubleProperty progress = event.getProgressProperty();

      if (text != null && progress != null) {
        maskerPane.textProperty().bind(text);
        maskerPane.progressProperty().bind(progress);
        maskerPane.setVisible(true);
      } else {
        maskerPane.setVisible(false);
        maskerPane.textProperty().unbind();
        maskerPane.progressProperty().unbind();
      }
    });
  }

  /**
   * Switch to a page.
   *
   * @param event switch event.
   */
  @Subscribe
  public void onSwitchPage(SwitchPageEvent event) {
    Platform.runLater(() -> {
      if (getCurrentEntity() != event.getEntity()) {
        showEntity(event.getEntity());
      }
      Page page = event.getEntity().getPageByName(event.getPageName());
      if (page != null) {
        for (Tab tab : pagesTabPanel.getTabs()) {
          if (tab.getId().equals(page.getName())) {
            pagesTabPanel.getSelectionModel().select(tab);
          }
        }
        page.showObject(event.getShowObject());
      }
    });

  }

  protected void onPageVisibilityChange(Page page, boolean visible) {
    Entity entity = getCurrentEntity();
    if (entity == null || page == null) {
      return;
    }
    // 查找page与index
    int index = -1;
    for (Page item : entity.getPages()) {
      index++;
      if (item == page) {
        break;
      }
      if (!item.getVisibleProperty().get()) {
        index--;
      }
    }

    // 增删tab
    Tab tabToDo = null;
    for (Tab tab : pagesTabPanel.getTabs()) {
      if (tab.getId().equals(page.getName())) {
        tabToDo = tab;
        break;
      }
    }
    if (visible && tabToDo == null) {
      //添加tab
      tabToDo = createTabByPage(page);
      pagesTabPanel.getTabs().add(Math.min(index, pagesTabPanel.getTabs().size()), tabToDo);
    } else if (!visible && tabToDo != null) {
      //删除tab
      pagesTabPanel.getTabs().remove(tabToDo);
    }

  }

  /**
   * 页面可视属性修改事件处理.
   *
   * @param event 修改事件
   */
  @Subscribe
  public void onPageVisibilityChange(PageVisibilityChangeEvent event) {
    PlatformUtility.runInFxThread(() -> {
      Entity entity = getCurrentEntity();
      if (entity == null) {
        return;
      }
      Page page = entity.getPageByName(event.getPageName());
      if (page == null) {
        log.warn("Fail to find page : {}!", event.getPageName());
        return;
      }
      page.getVisibleProperty().set(event.isVisible());
    });
  }

  public void onHideShowMenu(ActionEvent event) {
    masterDetailPane.setShowDetailNode(!masterDetailPane.isShowDetailNode());
  }

}
