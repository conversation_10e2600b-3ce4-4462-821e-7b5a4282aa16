package com.mc.tool.framework.operation.office.view;

import com.mc.graph.interfaces.CellSkin;
import com.mc.tool.framework.office.datamodel.OfficeData;
import com.mc.tool.framework.operation.controller.OperationPageControllable;
import com.mc.tool.framework.operation.office.graph.OfficeSceneGraph;
import com.mc.tool.framework.systemedit.datamodel.VisualEditModel;
import java.io.IOException;
import java.net.URL;
import java.util.ResourceBundle;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.scene.control.Slider;
import javafx.scene.layout.Priority;
import javafx.scene.layout.VBox;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public final class OfficeSceneView extends VBox implements Initializable {
  private final OperationPageControllable controllable;
  private final VisualEditModel model;

  @FXML
  private VBox graphContainer;

  @FXML
  private Slider zoomSlider;

  private OfficeSceneGraph graph;

  /**
   * Constructor.
   *
   * @param controllable controllable
   * @param model        model
   */
  public OfficeSceneView(OperationPageControllable controllable, VisualEditModel model) {
    this.controllable = controllable;
    this.model = model;
    FXMLLoader loader = new FXMLLoader(
        getClass().getResource("/com/mc/tool/framework/operation/office/office_scene_view.fxml"));
    loader.setController(this);
    loader.setRoot(this);
    try {
      loader.load();
    } catch (IOException exception) {
      log.warn("Fail to load office_scene_view.fxml!", exception);
    }
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {
    graph = new OfficeSceneGraph(controllable);
    graph.init();
    graphContainer.getChildren().add(graph.getCanvas().getNode());
    VBox.setVgrow(graph.getCanvas().getNode(), Priority.ALWAYS);
    zoomSlider.valueProperty().bindBidirectional(graph.getCanvas().getScaleProperty());

    for (OfficeData data : model.getOfficeFunctions()) {
      addOfficeItem(graph, data);
    }

    for (CellSkin skin : graph.getSkinManager().getAllCellSkin()) {
      skin.rotatableProperty().set(false);
    }
  }

  /**
   * 使内容适配显示视图.
   */
  public void fitToView() {
    graph.getCanvas().fitToView();
  }

  protected void addOfficeItem(OfficeSceneGraph graph, OfficeData data) {
    graph.insertCell("", "", data.getXpos().get(), data.getYpos().get(), data.getWidth().get(),
        data.getHeight().get(), data.getType(), data);
  }
}
