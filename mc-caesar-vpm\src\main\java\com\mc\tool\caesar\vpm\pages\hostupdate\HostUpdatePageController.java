package com.mc.tool.caesar.vpm.pages.hostupdate;

import com.mc.common.util.PlatformUtility;
import com.mc.common.util.WeakAdapter;
import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.CaesarCpuTypeProperty;
import com.mc.tool.caesar.api.CaesarSwitchDataModel;
import com.mc.tool.caesar.api.datamodel.MatrixData;
import com.mc.tool.caesar.api.datamodel.MatrixDefinitionData;
import com.mc.tool.caesar.api.datamodel.ModuleData;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.utils.Utilities;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.caesar.vpm.pages.hostupdate.Bundle.NbBundle;
import com.mc.tool.caesar.vpm.pages.update.UpdateData;
import com.mc.tool.caesar.vpm.pages.update.UpdateLogger;
import com.mc.tool.caesar.vpm.util.update.UpdateConstant;
import com.mc.tool.caesar.vpm.util.update.UpdatePackageInfo;
import com.mc.tool.caesar.vpm.util.update.UpdateUtilities;
import com.mc.tool.framework.interfaces.DeviceControllable;
import com.mc.tool.framework.interfaces.ViewControllable;
import com.mc.tool.framework.utility.InjectorProvider;
import com.mc.tool.framework.utility.UndecoratedAlert;
import com.mc.tool.framework.utility.dialogues.FileDialogue;
import com.mc.tool.framework.utility.dialogues.FileDialogueFactory;
import java.beans.PropertyChangeListener;
import java.io.File;
import java.net.URL;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.ResourceBundle;
import javafx.application.Platform;
import javafx.beans.binding.BooleanBinding;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.beans.value.ObservableValue;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.event.ActionEvent;
import javafx.event.EventHandler;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.AlertEx.AlertExType;
import javafx.scene.control.Button;
import javafx.scene.control.CheckBox;
import javafx.scene.control.Label;
import javafx.scene.control.ListView;
import javafx.scene.control.ProgressBar;
import javafx.scene.control.Tooltip;
import javafx.scene.control.TreeItem;
import javafx.scene.control.TreeTableColumn;
import javafx.scene.control.TreeTableColumn.CellDataFeatures;
import javafx.scene.control.TreeTableView;
import javafx.stage.FileChooser.ExtensionFilter;
import javafx.stage.Window;
import javafx.util.Callback;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class HostUpdatePageController implements Initializable, ViewControllable {

  @FXML private TreeTableView<UpdateData> tableView;
  @FXML private Button updateButton;
  @FXML private Button loadButton;
  @FXML private Button cancelButton;
  @FXML private Button selectAllButton;
  @FXML private Label updateFilePathLabel;
  @FXML private ListView<String> loglist;
  @FXML private TreeTableColumn<UpdateData, String> nameCol;
  @FXML private TreeTableColumn<UpdateData, String> typeCol;
  @FXML private TreeTableColumn<UpdateData, String> portsCol;
  @FXML private TreeTableColumn<UpdateData, String> serialCol;
  @FXML private TreeTableColumn<UpdateData, String> hardwareVerCol;
  @FXML private TreeTableColumn<UpdateData, String> currentVersionCol;
  @FXML private TreeTableColumn<UpdateData, String> currentDateCol;
  @FXML private TreeTableColumn<UpdateData, String> updateVersionCol;
  @FXML private TreeTableColumn<UpdateData, String> updateDateCol;
  @FXML private TreeTableColumn<UpdateData, CheckBox> updateCol;
  @FXML private TreeTableColumn<UpdateData, ProgressBar> progressCol;

  private WeakAdapter weakAdapter = new WeakAdapter();

  private CaesarDeviceController deviceController = null;

  private List<UpdateData> updateDataList = new ArrayList<>();
  private ArrayList<UpdateData> bindGroup = new ArrayList<>();

  private UpdateHostUpdater updater;
  private ObservableList<String> logs = FXCollections.observableArrayList();
  private UpdateLogger logger = new UpdateLogger(logs);

  private SimpleBooleanProperty updating = new SimpleBooleanProperty(false);
  private SimpleBooleanProperty updateCanecelled = new SimpleBooleanProperty(false);
  private SimpleBooleanProperty selectedAll = new SimpleBooleanProperty(false);
  private List<TreeItem<UpdateData>> availableGroup = new ArrayList<>(); // 可行的typeItem
  protected final String[] updateData = {
    ModuleData.PROPERTY_PORTS,
    ModuleData.PROPERTY_STATUS,
    ModuleData.PROPERTY_VERSION,
    MatrixData.PROPERTY_STATUS,
    MatrixData.PROPERTY_HOSTADDRESS
  };

  @Override
  public void setDeviceController(DeviceControllable deviceController) {
    if (deviceController instanceof CaesarDeviceController) {
      this.deviceController = (CaesarDeviceController) deviceController;
    } else {
      log.warn("Error device controller type : {} !", deviceController.getClass().getName());
      return;
    }

    CaesarSwitchDataModel model = this.deviceController.getDataModel();
    model.addPropertyChangeListener(
        updateData,
        weakAdapter.wrap(
            (PropertyChangeListener)
                evt -> {
                  if (isUpdating()) {
                    return;
                  }
                  this.deviceController
                      .submitAsync(() -> Utilities.getMatrixModels(model))
                      .whenComplete(
                          (matricesModels, throwable) -> {
                            if (throwable != null) {
                              log.warn("Fail to get matrix models!", throwable);
                              return;
                            }
                            PlatformUtility.runInFxThread(
                                () -> {
                                  loadUpdateData(matricesModels);
                                  tableView.refresh();
                                });
                          });
                }));

    updateNodesImpl();
    updater = new UpdateHostUpdater(this.deviceController, logger, this);
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {
    BooleanBinding labelBinding = updateFilePathLabel.textProperty().isEmpty();
    updateButton.disableProperty().bind(updating.or(labelBinding));
    loadButton.disableProperty().bind(updating);
    cancelButton.disableProperty().bind(updating.not());
    selectAllButton.disableProperty().bind(updating.or(labelBinding));

    selectedAll.addListener(
        weakAdapter.wrap(
            (observable, oldValue, newValue) -> {
              boolean newv = newValue;
              if (newv) {
                selectAllButton.setText(
                    NbBundle.getMessage(
                        HostUpdatePageController.class,
                        "HostUpdate.seleteAllButton.text.opposite"));
              } else {
                selectAllButton.setText(
                    NbBundle.getMessage(
                        HostUpdatePageController.class, "HostUpdate.seleteAllButton.text"));
              }
              for (TreeItem<UpdateData> typeItem : availableGroup) {
                typeItem.getValue().setSelected(newv);
                if (newv) {
                  typeItem.getParent().setExpanded(true);
                }
              }
            }));
    loglist.setItems(logs);

    initTreeView();
    initUpdateButton();
    initLoadButton();
    initCancelButton();
    initSelectAllButton();
  }

  private void initTreeView() {
    if (null == tableView) {
      return;
    }

    nameCol.setCellValueFactory((item) -> item.getValue().getValue().getNameProperty());
    typeCol.setCellValueFactory((item) -> item.getValue().getValue().getTypeProperty());
    currentVersionCol.setCellValueFactory(
        (item) -> item.getValue().getValue().getCurrentVersionProperty());
    currentDateCol.setCellValueFactory(
        (item) -> item.getValue().getValue().getCurrentDateProperty());
    updateVersionCol.setCellValueFactory(
        (item) -> item.getValue().getValue().getUpdateVersionProperty());
    updateDateCol.setCellValueFactory((item) -> item.getValue().getValue().getUpdateDateProperty());
    portsCol.setCellValueFactory((item) -> item.getValue().getValue().getPortsProperty());
    serialCol.setCellValueFactory((item) -> item.getValue().getValue().getSerialProperty());
    hardwareVerCol.setCellValueFactory(
        (item) -> item.getValue().getValue().getHardwareVersionProperty());
    updateCol.setCellValueFactory(
        param -> {
          UpdateData updateData = param.getValue().getValue();
          CheckBox checkBox = updateData.getCheckBox();
          if (updateData.getType() == null || updateData.getCurrentVersion() == null) {
            checkBox.setVisible(false);
          }

          checkBox
              .selectedProperty()
              .addListener(
                  weakAdapter.wrap(
                      (obsv, oldv, newv) -> {
                        if (updater != null) {
                          if (selectedAll.get() && !newv) {
                            List<UpdateData> selectedGroup = new ArrayList<>();
                            for (UpdateData ud : updateDataList) {
                              if (ud.getSelected()) {
                                selectedGroup.add(ud);
                              }
                            }
                            selectedAll.set(false);
                            for (UpdateData ud : selectedGroup) {
                              ud.setSelected(true);
                            }
                          }
                          updateData.setSelected(newv);
                        }
                      }));
          checkBox.selectedProperty().bindBidirectional(updateData.getSelectedProperty());
          checkBox.disableProperty().bindBidirectional(updateData.getDisabledProperty());

          return new SimpleObjectProperty<>(checkBox);
        });

    progressCol.setCellValueFactory(new ProgressCallback());
  }

  private static class ProgressCallback
      implements Callback<CellDataFeatures<UpdateData, ProgressBar>, ObservableValue<ProgressBar>> {
    @Override
    public ObservableValue<ProgressBar> call(CellDataFeatures<UpdateData, ProgressBar> param) {
      UpdateData updateData = param.getValue().getValue();
      ProgressBar progressBar = updateData.getProgressBar();
      if (updateData.getType() == null || updateData.getCurrentVersion() == null) {
        progressBar.setVisible(false);
      }
      return new SimpleObjectProperty<>(progressBar);
    }
  }

  public void setUpdating(boolean value) {
    this.updating.set(value);
  }

  public boolean isUpdating() {
    return this.updating.get();
  }

  public void setCancelled(boolean value) {
    this.updateCanecelled.set(value);
  }

  /** . */
  public void initCancelButton() {
    cancelButton.setOnAction(
        weakAdapter.wrap(
            new EventHandler<ActionEvent>() {

              @Override
              public void handle(ActionEvent event) {
                updateCanecelled.set(true);
                updater.updateCancel();
              }
            }));
  }

  private void initSelectAllButton() {
    selectAllButton.setOnAction(
        weakAdapter.wrap(
            new EventHandler<ActionEvent>() {

              @Override
              public void handle(ActionEvent event) {
                if (!updateFilePathLabel.getText().isEmpty()) {
                  selectedAll.set(!selectedAll.get());
                }
              }
            }));
  }

  private void initUpdateButton() {
    if (null == updateButton) {
      return;
    }
    updateButton.setOnAction(
        weakAdapter.wrap(
            new EventHandler<ActionEvent>() {
              @Override
              public void handle(ActionEvent event) {
                updater.setFilePath(updateFilePathLabel.getText());
                updater.update();
              }
            }));
  }

  /** . */
  public void updateStart() {

    for (UpdateData updateData : updateDataList) {
      updateData.setDisabled(true);
    }
  }

  private void initLoadButton() {
    loadButton.setOnAction(
        weakAdapter.wrap(
            new EventHandler<ActionEvent>() {
              @Override
              public void handle(ActionEvent event) {

                FileDialogueFactory factory =
                    InjectorProvider.getInjector().getInstance(FileDialogueFactory.class);
                FileDialogue fileDialogue = factory.createFileDialogue();
                fileDialogue.addExtensionFilter(new ExtensionFilter("SWU", "*.swu"));

                File choosedfile = fileDialogue.showOpenDialog(loadButton.getScene().getWindow());
                if (null == choosedfile) {
                  return;
                }
                try {
                  log.info("Loading host update file {}.", choosedfile.getName());
                  selectedAll.set(false);
                  UpdatePackageInfo updatePackageInfo =
                      UpdateUtilities.getUpdatePackageInfo(choosedfile.getPath());
                  // 添加硬件版本不匹配的错误提示
                  // Get current module type
                  ModuleData module = deviceController.getDataModel().getSwitchModuleData().getModuleData(0);
                  CaesarConstants.Module.Type moduleType = module != null
                      ? CaesarConstants.Module.Type.valueOf(module.getType()) : null;
                  // Validate package compatibility
                  if (isPackageIncompatible(updatePackageInfo, moduleType)) {
                    showIncompatibilityWarning(updatePackageInfo, moduleType);
                  }
                  if (updater != null) {
                    updater.setUpdatePackageInfo(updatePackageInfo);
                  }

                  deviceController
                      .submitAsync(() -> getIpModels(deviceController.getDataModel()))
                      .whenComplete(
                          (data, throwable) -> {
                            if (throwable != null) {
                              log.warn("Fail to get ip models!", throwable);
                              return;
                            }
                            PlatformUtility.runInFxThread(
                                () ->
                                    updateFileVersion(
                                        data, updatePackageInfo, choosedfile.getPath()));
                          });
                } catch (Exception ex) {
                  log.warn("Fail to load update file!", ex);
                } finally {
                  tableView.refresh();
                }
              }
            }));
  }

  private Map<String, CaesarSwitchDataModel> getIpModels(CaesarSwitchDataModel model) {
    Map<String, CaesarSwitchDataModel> result = new HashMap<>();
    for (TreeItem<UpdateData> dkmItem : tableView.getRoot().getChildren()) {
      String address = dkmItem.getValue().getAddress();
      try {
        CaesarSwitchDataModel dkmModel = Utilities.getExternalModel(model, address);
        result.put(address, dkmModel);
      } catch (BusyException | ConfigException exception) {
        log.warn("Fail to get model for {}!", address);
      }
    }
    return result;
  }

  /** . */
  public void clearUpdateStatus() {
    for (UpdateData updateData : updateDataList) {
      updateData.setSelected(false);
      updateData.getProgressBar().progressProperty().unbind();
      updateData.getProgressBar().setProgress(0);
    }
  }

  /** . */
  public void updateNodes() {
    Platform.runLater(this::updateNodesImpl);
  }

  private void updateNodesImpl() {
    if (null == tableView) {
      return;
    }
    updateFilePathLabel.setText("");
    selectedAll.set(false);
    availableGroup.clear();

    if (tableView.getRoot() == null) {
      UpdateData rootdata = new UpdateData(-1, (byte) 0, "s0.0.0.0".substring(1));
      rootdata.setName("系统");
      rootdata.setType("主机");
      updateDataList.add(rootdata);

      TreeItem<UpdateData> rootNode = new TreeItem<>(rootdata);
      tableView.setRoot(rootNode);
      tableView.setShowRoot(false);
    }

    deviceController
        .submitAsync(() -> Utilities.getMatrixModels(deviceController.getDataModel()))
        .whenComplete(
            (matricesModels, throwable) -> {
              if (throwable != null) {
                log.warn("Fail to get matrix models!", throwable);
                return;
              }
              PlatformUtility.runInFxThread(
                  () -> {
                    loadUpdateData(matricesModels);
                    for (TreeItem<UpdateData> dkmItem : tableView.getRoot().getChildren()) {
                      dkmItem.setExpanded(true);
                    }
                    tableView.refresh();
                  });
            });
  }

  private void loadUpdateData(
      Collection<Pair<MatrixDefinitionData, CaesarSwitchDataModel>> matrices) {
    List<TreeItem<UpdateData>> dkmRetainItems = new ArrayList<>();
    for (Pair<MatrixDefinitionData, CaesarSwitchDataModel> pair : matrices) {
      MatrixDefinitionData mdd = pair.getKey();
      CaesarSwitchDataModel matModel = pair.getValue();
      if (null == mdd) {
        continue;
      }
      String address = mdd.getAddress();
      if (matModel == null) {
        log.warn("Fail to connect to {}.", address);
        continue;
      }
      TreeItem<UpdateData> dkmNode =
          createDkmNode(
              mdd.getDevice(),
              address,
              tableView.getRoot(),
              matModel.getSwitchModuleData().getModuleData(0).getSerial());
      dkmRetainItems.add(dkmNode);
      List<TreeItem<UpdateData>> retainItems = new ArrayList<>();
      for (ModuleData moduleData : matModel.getSwitchModuleData().getModuleDatas()) {
        if (moduleData == null) {
          continue;
        }
        if (!moduleData.isStatusActive()) {
          continue;
        }
        if (moduleData.getOid() == 0) {
          TreeItem<UpdateData> matrixNode =
              createMatrixNode("MATRIX", moduleData, address, dkmNode);
          matrixNode.getValue().setParentName(dkmNode.getValue().getName());
          retainItems.add(matrixNode);

          CaesarCpuTypeProperty property = CaesarCpuTypeProperty.getProperty(moduleData.getType());
          if (property != null && property.hasTrunk()) {
            TreeItem<UpdateData> slotItem = createSlotNode("TRUNK", moduleData, address, dkmNode);
            retainItems.add(slotItem);
          }
        } else {
          if (moduleData.isStatusAvailable()
              && mdd.getFirstModule() <= moduleData.getOid()
              && moduleData.getOid() <= mdd.getLastModule()) {
            TreeItem<UpdateData> slotItem =
                createSlotNode("IO BOARD" + moduleData.getOid(), moduleData, address, dkmNode);
            retainItems.add(slotItem);
          }
        }
      }

      dkmNode.getChildren().retainAll(retainItems);
      dkmNode
          .getChildren()
          .sort(
              Comparator.comparingInt(
                  (item) ->
                      (item.getValue().getLevel1() << 16) + (item.getValue().getLevel2() & 0xff)));
    }

    tableView.getRoot().getChildren().retainAll(dkmRetainItems);
  }

  /** . */
  public TreeItem<UpdateData> createDkmNode(
      String name, String address, TreeItem<UpdateData> parent, String serial) {
    final int level1 = -1;
    final int level2 = 0;
    for (TreeItem<UpdateData> item : parent.getChildren()) {
      if (item.getValue().getName().equals(name)) {
        return item;
      }
    }

    UpdateData dkmData = new UpdateData(level1, (byte) level2, address);
    dkmData.setName(name);
    dkmData.setType(UpdateConstant.UPDATE_HOST_TYPE);
    dkmData.setParentName(tableView.getRoot().getValue().getName());
    dkmData.setSerial(serial);

    updateDataList.add(dkmData);
    TreeItem<UpdateData> dkmTreeItem = new TreeItem<>(dkmData);
    parent.getChildren().add(dkmTreeItem);

    return dkmTreeItem;
  }

  private TreeItem<UpdateData> createMatrixNode(
      String name, ModuleData moduleData, String address, TreeItem<UpdateData> parent) {
    final int level1 = moduleData.getOid();
    final int level2 = 0;
    TreeItem<UpdateData> result = null;
    // 查找node
    for (TreeItem<UpdateData> item : parent.getChildren()) {
      if (item.getValue().getLevel1() == level1
          && item.getValue().getLevel2() == level2
          && item.getValue().getType().equals(UpdateConstant.UPDATE_CPU_TYPE)) {
        result = item;
        break;
      }
    }
    // 如果不存在，创建一个
    if (result == null) {
      UpdateData matrixData = new UpdateData(level1, (byte) level2, address);
      matrixData.setName(name);
      matrixData.setParentName(tableView.getRoot().getValue().getName());
      matrixData.setType(UpdateConstant.UPDATE_CPU_TYPE);
      matrixData.setHardwareVersion(moduleData.getVersion().getHwVersionDef());

      TreeItem<UpdateData> matrixItem = new TreeItem<>(matrixData);
      result = matrixItem;

      UpdateData appData = new UpdateData(level1, (byte) level2, address);
      appData.setName("");
      appData.setParentName(name);
      appData.setType(UpdateConstant.UPDATE_APP_TYPE);

      UpdateData fpgaData = new UpdateData(level1, (byte) level2, address);
      fpgaData.setName("");
      fpgaData.setParentName(name);
      fpgaData.setType(UpdateConstant.UPDATE_FPGA_TYPE);

      UpdateData systemData = new UpdateData(level1, (byte) level2, address);
      systemData.setName("");
      systemData.setParentName(name);
      systemData.setType(UpdateConstant.UPDATE_SYS_TYPE);

      updateDataList.add(matrixData);
      updateDataList.add(appData);
      updateDataList.add(systemData);
      updateDataList.add(fpgaData);

      TreeItem<UpdateData> appItem = new TreeItem<>(appData);
      TreeItem<UpdateData> systemItem = new TreeItem<>(systemData);
      TreeItem<UpdateData> fpgaItem = new TreeItem<>(fpgaData);
      matrixItem.getChildren().addAll(Arrays.asList(appItem, fpgaItem, systemItem));
      parent.getChildren().add(matrixItem);
    }

    // 更新数据
    result.getValue().setName(name);
    for (TreeItem<UpdateData> item : result.getChildren()) {
      UpdateData data = item.getValue();
      if (data.getType().equals(UpdateConstant.UPDATE_APP_TYPE)) {
        data.setCurrentVersion(moduleData.getVersion().getAppVersion());
      } else if (data.getType().equals(UpdateConstant.UPDATE_FPGA_TYPE)) {
        data.setCurrentVersion(moduleData.getVersion().getFpgaVersion());
      } else if (data.getType().equals(UpdateConstant.UPDATE_SYS_TYPE)) {
        data.setCurrentVersion(moduleData.getVersion().getSystemVersion());
      }
      if (data.getCurrentVersion() != null) {
        data.setCurrentDate(
            String.format(
                "%04d.%02d.%02d",
                data.getCurrentVersion().getYear() + 2000,
                data.getCurrentVersion().getMonth(),
                data.getCurrentVersion().getDay()));
      }
    }

    return result;
  }

  private TreeItem<UpdateData> createSlotNode(
      String name, ModuleData moduleData, String address, TreeItem<UpdateData> parent) {
    final int level1 = moduleData.getOid();
    final int level2 = level1 == 0 ? 255 : 0;

    TreeItem<UpdateData> result = null;
    // 查找
    for (TreeItem<UpdateData> item : parent.getChildren()) {
      UpdateData updateData = item.getValue();
      if (updateData.getLevel1() == level1
          && (updateData.getLevel2() & 0xff) == level2
          && updateData.getType().equals(UpdateConstant.UPDATE_IO_TYPE)) {
        result = item;
        break;
      }
    }
    // 如果没有，创建一个
    if (result == null) {
      UpdateData slotData = new UpdateData(level1, (byte) level2, address);
      slotData.setType(UpdateConstant.UPDATE_IO_TYPE);
      slotData.setParentName(tableView.getRoot().getValue().getName());

      UpdateData ioData = new UpdateData(level1, (byte) level2, address);
      ioData.setName("IO_FPGA");
      ioData.setParentName(name);
      ioData.setType(UpdateConstant.UPDATE_FPGA_TYPE);

      updateDataList.add(slotData);
      updateDataList.add(ioData);

      TreeItem<UpdateData> slotItem = new TreeItem<>(slotData);
      TreeItem<UpdateData> ioItem = new TreeItem<>(ioData);
      slotItem.getChildren().add(ioItem);

      parent.getChildren().add(slotItem);
      result = slotItem;
    }

    // 更新数据
    result.getValue().setPorts(String.valueOf(moduleData.getPorts()));
    result.getValue().setName(name);

    for (TreeItem<UpdateData> item : result.getChildren()) {
      UpdateData updateData = item.getValue();
      if (updateData.getType().equals(UpdateConstant.UPDATE_FPGA_TYPE)) {
        if (level1 == 0) {
          updateData.setCurrentVersion(moduleData.getVersion().getOtherVersion());
        } else {
          updateData.setCurrentVersion(moduleData.getVersion().getFpgaVersion());
        }
        updateData.setCurrentDate(
            String.format(
                "%04d.%02d.%02d",
                updateData.getCurrentVersion().getYear() + 2000,
                updateData.getCurrentVersion().getMonth(),
                updateData.getCurrentVersion().getDay()));
      }
    }
    return result;
  }

  /** . */
  public TreeItem<UpdateData> getSlotNodeByLevel1(int level) {
    for (TreeItem<UpdateData> dkmNode : tableView.getRoot().getChildren()) {
      for (TreeItem<UpdateData> slotNode : dkmNode.getChildren()) {
        if (slotNode.getValue().getLevel1() == level) {
          return slotNode;
        }
      }
    }
    return null;
  }

  public TreeTableView<UpdateData> getTreeView() {
    return this.tableView;
  }

  @Override
  public DeviceControllable getDeviceController() {
    return deviceController;
  }

  protected void updateFileVersion(
      Map<String, CaesarSwitchDataModel> ipModels,
      UpdatePackageInfo updatePackageInfo,
      String path) {
    Collection<String> targetType = updatePackageInfo.getTargetType();

    updateFilePathLabel.setText(path);
    updateFilePathLabel.setTooltip(new Tooltip(path));

    List<String> typelist = new ArrayList<>(updatePackageInfo.getSubUpdateType());

    clearUpdateStatus();
    // 清除绑定
    for (TreeItem<UpdateData> dkmItem : tableView.getRoot().getChildren()) {
      for (TreeItem<UpdateData> slotItem : dkmItem.getChildren()) {
        for (TreeItem<UpdateData> typeItem : slotItem.getChildren()) {
          bindGroup.add(typeItem.getValue());
        }
        if (bindGroup.size() == 3) {
          for (int i = 0; i < bindGroup.size() - 1; i++) {
            bindGroup
                .get(i)
                .getSelectedProperty()
                .unbindBidirectional(bindGroup.get(i + 1).getSelectedProperty());
          }
        }
        bindGroup.clear();
      }
    }
    // 更新版本
    for (TreeItem<UpdateData> dkmItem : tableView.getRoot().getChildren()) {
      for (TreeItem<UpdateData> slotItem : dkmItem.getChildren()) {
        MatrixDefinitionData mdd = null;
        CaesarSwitchDataModel matModel = ipModels.get(dkmItem.getValue().getAddress());
        if (matModel == null) {
          continue;
        }

        // 传level1
        ModuleData moduleData =
            matModel.getSwitchModuleData().getModuleData(slotItem.getValue().getLevel1());
        if (moduleData.isStatusActive()) {
          mdd = Utilities.getMatrixByModule(matModel, moduleData);
        }

        boolean ioSuitable =
            checkIoSuitable(
                updatePackageInfo,
                slotItem,
                matModel.getSwitchModuleData().getModuleData(0),
                moduleData);

        boolean cpuSuitable =
            checkCpuSuitable(updatePackageInfo, matModel.getSwitchModuleData().getModuleData(0));
        UpdateUtilities.updateSlotVersion(
            slotItem,
            updatePackageInfo,
            mdd,
            cpuSuitable,
            ioSuitable,
            matModel.isIoVirtual(),
            matModel.getSwitchModuleData().getModuleData(0).getType());
      }
    }
    // 更新绑定
    for (TreeItem<UpdateData> dkmItem : tableView.getRoot().getChildren()) {
      for (TreeItem<UpdateData> slotItem : dkmItem.getChildren()) {
        for (TreeItem<UpdateData> typeItem : slotItem.getChildren()) {
          if (typelist.contains(typeItem.getValue().getType())
              && targetType.contains(typeItem.getParent().getValue().getType())) {
            bindGroup.add(typeItem.getValue());
          }
        }
        if (bindGroup.size() == typelist.size()) {
          for (int i = 0; i < bindGroup.size() - 1; i++) {
            if (!(bindGroup.get(i).getDisabled() || bindGroup.get(i + 1).getDisabled())) {
              bindGroup
                  .get(i)
                  .getSelectedProperty()
                  .bindBidirectional(bindGroup.get(i + 1).getSelectedProperty());
            }
          }
        }
        bindGroup.clear();
      }
    }
    availableGroup.clear();
    for (TreeItem<UpdateData> dkmItem : tableView.getRoot().getChildren()) {
      for (TreeItem<UpdateData> extItem : dkmItem.getChildren()) {
        for (TreeItem<UpdateData> typeItem : extItem.getChildren()) {
          if (!typeItem.getValue().getDisabled()) {
            availableGroup.add(typeItem);
          }
        }
      }
    }
    updater.setAvailableGroup(availableGroup);
  }

  /** 判断IO升级文件与主机是否适配. */
  protected boolean checkIoSuitable(
      UpdatePackageInfo updatePackageInfo,
      TreeItem<UpdateData> slotItem,
      ModuleData cpuModule,
      ModuleData ioModule) {
    boolean ioSuitable = false;
    if (updatePackageInfo.getTargetType().contains(UpdateConstant.UPDATE_IO_TYPE)
        && slotItem.getValue().getType().equals(UpdateConstant.UPDATE_IO_TYPE)) {

      if (!ioModule.isStatusActive()) {
        ioSuitable = false;
      } else {
        ioSuitable = updatePackageInfo.isSuitableForIo(cpuModule, ioModule);
      }
    }
    return ioSuitable;
  }

  protected boolean checkCpuSuitable(UpdatePackageInfo updatePackageInfo, ModuleData cpuModule) {
    if (!updatePackageInfo.isCpuUpdate()) {
      return false;
    }

    return updatePackageInfo.isSuitableForMat(cpuModule);
  }

  /**
   * Checks if the update package is incompatible with the current system.
   */
  private boolean isPackageIncompatible(UpdatePackageInfo updatePackageInfo,
                                        CaesarConstants.Module.Type moduleType) {
    return !updatePackageInfo.isCpuUpdate() && !updatePackageInfo.isIoUpdate()
        || !updatePackageInfo.isVersionValid()
        || moduleType != null && !updatePackageInfo.isSupportatrixType(moduleType);
  }

  /**
   * Shows a warning dialog for incompatible update packages.
   */
  private void showIncompatibilityWarning(UpdatePackageInfo updatePackageInfo, CaesarConstants.Module.Type moduleType) {
    String message = "";
    if (!updatePackageInfo.isCpuUpdate() && !updatePackageInfo.isIoUpdate()) {
      message = NbBundle.getMessage(
          HostUpdatePageController.class, "HostUpdate.alert.warning.packageError.text");
    } else if (!updatePackageInfo.isVersionValid()) {
      message = NbBundle.getMessage(
          HostUpdatePageController.class, "HostUpdate.alert.warning.versionError.text");
    } else if (moduleType != null && !updatePackageInfo.isSupportatrixType(moduleType)) {
      // Must be a matrix type incompatibility
      message = NbBundle.getMessage(
          HostUpdatePageController.class, "HostUpdate.alert.warning.matrixError.text");
    }
    String title =  NbBundle.getMessage(
        HostUpdatePageController.class, "HostUpdate.alert.warning.title");
    UndecoratedAlert alert = warningAlert(tableView.getScene().getWindow(), title, message);
    alert.showAndWait();
  }

  /**
   * Creates a warning alert dialog.
   */
  private UndecoratedAlert warningAlert(Window window, String title, String message) {
    UndecoratedAlert alert = new UndecoratedAlert(AlertExType.WARNING);
    alert.initOwner(window);
    alert.setTitle(title);
    alert.setHeaderText(null);
    alert.setContentText(message);
    return alert;
  }
}
