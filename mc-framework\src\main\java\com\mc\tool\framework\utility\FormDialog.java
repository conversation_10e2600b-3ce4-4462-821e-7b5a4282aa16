package com.mc.tool.framework.utility;

import com.dooapp.fxform.FXForm;
import com.dooapp.fxform.builder.FXFormBuilder;
import com.dooapp.fxform.model.Element;
import com.dooapp.fxform.model.impl.BufferedElement;
import com.dooapp.fxform.view.FXFormNode;
import com.dooapp.fxform.view.FXFormSkin;
import java.util.Optional;
import java.util.ResourceBundle;
import javafx.beans.binding.BooleanBinding;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.scene.Node;
import javafx.scene.control.ButtonType;
import javafx.scene.control.Control;
import javafx.scene.control.ScrollPane;
import javafx.stage.Modality;
import javafx.stage.Window;

/**
 * FormDialog.
 */
public class FormDialog<B> extends UndecoratedDialog<ButtonType> {
  private final FXForm<B> fxForm;

  /**
   * Constructor.
   *
   * @param bean        bean
   * @param bundle      bundle
   * @param ownerWindow window
   */
  public FormDialog(B bean, ResourceBundle bundle, Window ownerWindow, String... excludes) {
    initOwner(ownerWindow);
    initModality(Modality.WINDOW_MODAL);

    fxForm = new FXFormBuilder().source(bean).buffered(true, false).resourceBundle(bundle)
        .exclude(excludes).build();

    getDialogPane().setContent(new ScrollPane(fxForm));
    getDialogPane().getButtonTypes().add(ButtonType.APPLY);
    getDialogPane().getButtonTypes().add(ButtonType.CANCEL);

    BooleanBinding validBinding = new BooleanBinding() {
      {
        super.bind(fxForm.getConstraintViolations());
      }

      @Override
      protected boolean computeValue() {
        return fxForm.getConstraintViolations().isEmpty();
      }
    };

    getDialogPane().lookupButton(ButtonType.APPLY).disableProperty().bind(validBinding.not());
  }

  /**
   * 获取项的控件.
   *
   * @param name 项名称
   * @param type 控件类型
   * @return 控件
   */
  public <T extends Control> T getElementControl(String name, Class<T> type) {
    final ObjectProperty<T> result = new SimpleObjectProperty<>();
    fxForm.getElements().forEach((item) -> {
      if (item.getName().equals(name)) {
        Node node = ((FXFormSkin) fxForm.getSkin()).getEditor(item).getNode();
        if (type.isInstance(node)) {
          result.set(type.cast(node));
        }
      }
    });
    return result.get();
  }

  /**
   * 获取项.
   *
   * @param name 项名称
   * @return 项
   */
  public <T> BufferedElement<T> getBufferedElement(String name) {
    final ObjectProperty<BufferedElement<T>> result = new SimpleObjectProperty<>();
    fxForm.getElements().forEach((item) -> {
      if (item.getName().equals(name)) {
        result.set((BufferedElement<T>) item);
      }
    });
    return result.get();
  }

  /**
   * 禁用dialog的所有项.
   *
   * @param disable 为true时禁用，为false时启用
   */
  public void setDisable(boolean disable) {
    if (fxForm.getSkin() instanceof FXFormSkin) {
      FXFormSkin skin = (FXFormSkin) fxForm.getSkin();
      for (Element<?> element : fxForm.getElements()) {
        FXFormNode<?> node = skin.getEditor(element);
        if (node != null) {
          node.getNode().setDisable(disable);
        }
      }
    }
  }

  /**
   * 禁用其中一项.
   *
   * @param name    禁用的项的名称
   * @param disable 禁用与否
   */
  public void setDisable(String name, boolean disable) {
    if (fxForm.getSkin() instanceof FXFormSkin) {
      FXFormSkin skin = (FXFormSkin) fxForm.getSkin();
      for (Element<?> element : fxForm.getElements()) {
        String elementName = element.getName();
        FXFormNode<?> node = skin.getEditor(element);
        if (elementName.equalsIgnoreCase(name) && node != null) {
          node.getNode().setDisable(disable);
          break;
        }
      }
    }
  }

  public FXForm<?> getFxform() {
    return fxForm;
  }

  /**
   * 显示对话框，如果用户点击了apply，那么刷新用户数据到bean中.
   *
   * @return 对话框结果.
   */
  public Optional<ButtonType> showAndWaitWithCommit() {
    Optional<ButtonType> result = super.showAndWait();
    if (result.isPresent() && result.get() != null && result.get() == ButtonType.APPLY) {
      fxForm.commit();
    }
    return result;
  }
}
