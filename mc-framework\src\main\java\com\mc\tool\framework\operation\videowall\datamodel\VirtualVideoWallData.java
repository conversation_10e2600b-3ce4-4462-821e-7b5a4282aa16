package com.mc.tool.framework.operation.videowall.datamodel;

import com.google.gson.annotations.Expose;
import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.StringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import lombok.Getter;

/**
 * .
 */
public class VirtualVideoWallData {
  @Expose
  @Getter
  private StringProperty name = new SimpleStringProperty();
  @Expose
  @Getter
  private VirtualLayoutData layout;
  @Expose
  @Getter
  private ObservableList<VirtualVideoData> videos = FXCollections.observableArrayList();


}
