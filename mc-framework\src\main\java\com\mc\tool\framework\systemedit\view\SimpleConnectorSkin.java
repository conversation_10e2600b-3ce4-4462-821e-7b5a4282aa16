package com.mc.tool.framework.systemedit.view;

import com.mc.graph.AbstractConnectorSkin;
import com.mc.graph.interfaces.Connector;
import com.mc.graph.util.LinkUtil;
import javafx.beans.binding.DoubleBinding;
import javafx.scene.Node;
import javafx.scene.Parent;

/**
 * .
 */
public class SimpleConnectorSkin extends AbstractConnectorSkin {
  private Node node;
  private boolean connectAtLeft = true;
  private Integer heightOffset = null;
  private final Parent cellRegion;

  /**
   * Contructor.
   *
   * @param connector group's connector
   * @param parent    skin's parent
   * @param container container
   * @param node      skin's node
   */
  public SimpleConnectorSkin(Connector connector, Parent parent, Parent cellRegion,
                             Parent container, Node node) {
    super(connector, parent, container);
    this.cellRegion = cellRegion;
    this.node = node;
    this.node.setUserData(this);
    this.linkableProperty.set(false);
    updatePosProperty();
  }

  /**
   * Contructor.
   *
   * @param connector             group's connector
   * @param parent                skin's parent
   * @param container             container
   * @param node                  skin's node
   * @param connectorHeightOffset height offset
   */
  public SimpleConnectorSkin(Connector connector, Parent parent, Parent cellRegion,
                             Parent container, Node node, int connectorHeightOffset) {
    super(connector, parent, container);
    this.cellRegion = cellRegion;
    this.node = node;
    this.node.setUserData(this);
    this.linkableProperty.set(false);
    heightOffset = connectorHeightOffset;
    updatePosProperty();
  }

  @Override
  public Node getNode() {
    return node;
  }

  @Override
  protected void initNode() {
  }

  @Override
  public void add() {
  }

  @Override
  public void remove() {
  }

  protected void updatePosProperty() {
    DoubleBinding xposBinding = new DoubleBinding() {
      {
        super.bind(getNode().parentProperty(), parent.layoutXProperty(),
            parent.translateXProperty(), getNode().layoutXProperty(),
            getNode().translateXProperty(), cellRegion.layoutXProperty(),
            cellRegion.translateXProperty());
      }

      @Override
      protected double computeValue() {
        return LinkUtil.localToGrandParent(parent, container,
            getNode().getLayoutX() + getNode().getTranslateX()
                + (connectAtLeft ? 0 : getNode().getBoundsInParent().getWidth()),
            true);
      }
    };

    DoubleBinding yposBinding = new DoubleBinding() {
      {
        super.bind(getNode().parentProperty(), parent.layoutYProperty(),
            parent.translateYProperty(), getNode().layoutYProperty(),
            getNode().translateYProperty(), cellRegion.layoutYProperty(),
            cellRegion.translateYProperty());

      }

      @Override
      protected double computeValue() {
        double offset = getNode().getBoundsInParent().getHeight() / 2;
        if (heightOffset != null) {
          offset = heightOffset;
        }
        return LinkUtil.localToGrandParent(parent, container,
            getNode().getLayoutY() + getNode().getTranslateY() + offset, false);
      }
    };

    containerXposProperty.bind(xposBinding);
    containerYposProperty.bind(yposBinding);
  }

  public void setConnectAtLeft(boolean connectAtLeft) {
    this.connectAtLeft = connectAtLeft;
  }
}
