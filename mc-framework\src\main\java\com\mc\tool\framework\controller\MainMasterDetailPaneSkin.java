package com.mc.tool.framework.controller;

import impl.org.controlsfx.skin.MasterDetailPaneSkin;
import javafx.scene.layout.Region;
import org.controlsfx.control.MasterDetailPane;

/**
 * .
 */
public class MainMasterDetailPaneSkin extends MasterDetailPaneSkin {

  /**
   * Constructor.
   */
  public MainMasterDetailPaneSkin(MasterDetailPane pane) {
    super(pane);

    // 左侧框拉伸范围
    if (getSkinnable().getDetailNode() instanceof Region) {
      ((Region) getSkinnable().getDetailNode()).setMinWidth(120);
      ((Region) getSkinnable().getDetailNode()).setMaxWidth(300);
    }
  }

}
