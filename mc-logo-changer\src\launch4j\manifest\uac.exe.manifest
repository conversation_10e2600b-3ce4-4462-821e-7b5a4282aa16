<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<assembly xmlns="urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">
    <trustInfo xmlns="urn:schemas-microsoft-com:asm.v3">
        <security>
            <requestedPrivileges>
                <requestedExecutionLevel level="asInvoker" uiAccess="false"/>
                <!-- <requestedExecutionLevel level="highestAvailable" uiAccess="false"/> -->
                <!-- <requestedExecutionLevel level="requireAdministrator" uiAccess="false"/> -->
            </requestedPrivileges>
        </security>
    </trustInfo>
</assembly>    