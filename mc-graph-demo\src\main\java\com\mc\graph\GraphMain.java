package com.mc.graph;


import java.net.URL;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.ResourceBundle;
import java.util.function.DoubleToLongFunction;
import org.controlsfx.control.PopOver;
import org.controlsfx.control.PopOver.ArrowLocation;
import com.mc.graph.handler.KeyboardHandler;
import com.mc.graph.interfaces.CellBindedObject;
import com.mc.graph.interfaces.CellSkin;
import com.mc.graph.interfaces.ConnectorIdentifier;
import com.mc.graph.interfaces.ExtraActionInfo;
import com.mc.graph.interfaces.SkinFactory;
import javafx.application.Application;
import javafx.beans.value.ChangeListener;
import javafx.beans.value.ObservableValue;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.event.ActionEvent;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.scene.control.Slider;
import javafx.scene.control.ToggleButton;
import javafx.scene.layout.HBox;
import javafx.scene.layout.Priority;
import javafx.scene.layout.Region;
import javafx.scene.layout.VBox;
import javafx.stage.Stage;

public class GraphMain extends Application implements Initializable {
  @FXML
  private VBox container;

  @FXML
  private Slider slider;

  @FXML
  private ToggleButton previewButton;

  private McGraph graph;
  private DefaultUndoMananger undoMananger = new DefaultUndoMananger();

  public static void main(String[] args) {
    launch(args);
  }

  @Override
  public void start(Stage stage) throws Exception {
    Scene scene = new Scene((Parent) FXMLLoader.load(getClass().getResource("main.fxml")));
    stage.setScene(scene);

    stage.show();
  }

  public void initialize(URL location, ResourceBundle resources) {
    graph = new DemoGraph();
    graph.init();
    container.getChildren().add(1, graph.getCanvas().getNode());
    VBox.setVgrow(graph.getCanvas().getNode(), Priority.ALWAYS);

    slider.valueProperty().bindBidirectional(graph.getCanvas().getScaleProperty());


    final PopOver popOver = new PopOver();
    popOver.setArrowLocation(ArrowLocation.BOTTOM_CENTER);
    popOver.setAutoHide(false);
    popOver.setCloseButtonEnabled(false);
    popOver.setHideOnEscape(false);

    popOver.setContentNode(graph.getOverviewCanvas());

    previewButton.selectedProperty().addListener(new ChangeListener<Boolean>() {

      public void changed(ObservableValue<? extends Boolean> observable, Boolean oldValue,
          Boolean newValue) {
        if (newValue) {
          popOver.show(previewButton);
        } else {
          popOver.hide();
        }
      }
    });

    KeyboardHandler handler = new KeyboardHandler(graph);
    handler.install();

    graph.getEventBus().register(undoMananger);
  }

  public void onAdd(ActionEvent event) {
    double xpos = Math.random() * 400;
    double ypos = Math.random() * 400;
    double width = 100 + Math.random() * 100;
    double height = 100 + Math.random() * 100;
    int connectorCount = (int) (Math.random() * 5) + 1;
    graph.insertCell("0", "0", xpos, ypos, width, height, SkinFactory.DEFAULT_CELL,
        new TestBindedObject(connectorCount));
  }

  public void onUndo(ActionEvent event) {
    undoMananger.undo();
  }

  public void onRedo(ActionEvent event) {
    undoMananger.redo();
  }

  public void onUp(ActionEvent event) {
    Collection<CellSkin> skins = graph.getSelectionModel().getSelectedCellSkin();
    if (skins.size() == 0) {
      return;
    }
    
    CellSkin skin = skins.iterator().next();
    int index = graph.getGraphModel().getObservableCells().indexOf(skin.getCell());
    if (index < graph.getGraphModel().getObservableCells().size() - 1) {
      graph.orderCell(skin.getCell(), index + 1);
    }
  }

  public void onDown(ActionEvent event) {
    Collection<CellSkin> skins = graph.getSelectionModel().getSelectedCellSkin();
    if (skins.size() == 0) {
      return;
    }
    
    CellSkin skin = skins.iterator().next();
    int index = graph.getGraphModel().getObservableCells().indexOf(skin.getCell());
    if (index > 0) {
      graph.orderCell(skin.getCell(), index - 1);
    }
  }

  static class TestBindedObject implements CellBindedObject {
    private int count;

    public TestBindedObject(int count) {
      this.count = count;
    }

    public ObservableList<ConnectorIdentifier> getConnectorId() {
      ObservableList<ConnectorIdentifier> connectors = FXCollections.observableArrayList();
      for (int i = 0; i < count; i++) {
        connectors.add(ConnectorIdentifier.getIdentifier(i));
      }
      return connectors;
    }

    public Collection<ExtraActionInfo> getExtraActions() {
      List<ExtraActionInfo> info = new ArrayList<ExtraActionInfo>();
      info.add(new ExtraActionInfo() {

        public String getActionName() {
          return "Delete";
        }

        public String getActionId() {
          return "delete";
        }
      });
      return info;
    }

  }
}
