package com.mc.tool.framework;

import com.mc.tool.framework.interfaces.Page;
import com.mc.tool.framework.operation.view.OperationPageView;
import com.mc.tool.framework.systemedit.datamodel.VisualEditModel;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.scene.layout.Pane;

/**
 * .
 */
public class OperationPage implements Page {
  private final OperationPageView view;
  private SimpleBooleanProperty visibilityProperty = new SimpleBooleanProperty(true);

  public OperationPage(VisualEditModel model) {
    view = new OperationPageView(model);
  }

  @Override
  public String getTitle() {
    return "Operation";
  }

  @Override
  public String getName() {
    return "operation";
  }

  @Override
  public Pane getView() {
    return view;
  }

  @Override
  public BooleanProperty getVisibleProperty() {
    return visibilityProperty;
  }

  @Override
  public void showObject(Object object) {
  }

  @Override
  public String getStyleClass() {
    // TODO Auto-generated method stub
    return null;
  }

  @Override
  public void close() {
    view.getControllable().close();
  }
}
