package com.mc.tool.framework.operation.office.graph;

import com.mc.graph.McGraph;
import com.mc.graph.interfaces.CellSkin;
import com.mc.graph.interfaces.SkinFactory;
import com.mc.tool.framework.office.graph.OsSkinfactory;
import com.mc.tool.framework.operation.controller.OperationPageControllable;
import com.mc.tool.framework.operation.office.behavior.SelectFunctionBehavior;

/**
 * .
 */
public class OfficeSceneGraph extends McGraph {
  private final OperationPageControllable controllable;

  public OfficeSceneGraph(OperationPageControllable controllable) {
    this.controllable = controllable;
  }

  @Override
  protected SkinFactory createSkinFactory() {
    return new OsSkinfactory();
  }

  @Override
  public void createCellBehavior(CellSkin cellSkin) {
    super.createCellBehavior(cellSkin);
    cellSkin.addCellBehavior(new SelectFunctionBehavior(controllable, cellSkin));
  }

  @Override
  public boolean isCellMovable() {
    return false;
  }

  @Override
  public boolean isCellDeletable() {
    return false;
  }

  @Override
  public boolean isCellRotatable() {
    return false;
  }
}
