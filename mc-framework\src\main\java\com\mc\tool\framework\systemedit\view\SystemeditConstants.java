package com.mc.tool.framework.systemedit.view;

import javafx.scene.paint.Color;

/**
 * .
 */
public class SystemeditConstants {
  public static final String RESOURCE_PATH = "/com/mc/tool/framework/systemedit/";
  public static final String RESOURCE_PATH_SHORT = RESOURCE_PATH.substring(1);
  public static final String DVD_ONLINE_LOGO = "target_type_dvd_online.png";
  public static final String COMPUTER_ONLINE_LOGO = "target_type_computer_online.png";
  public static final String MONITOR_ONLINE_LOGO = "target_type_monitor_online.png";
  public static final String PROJECTOR_ONLINE_LOGO = "target_type_projector_online.png";
  public static final String GRID_LINE_N_ONLINE_LOGO = "target_type_grid_line_n_online.png";
  public static final String GRID_LINE_T_ONLINE_LOGO = "target_type_grid_line_t_online.png";
  public static final String GRID_LINE_R_ONLINE_LOGO = "target_type_grid_line_r_online.png";
  public static final String GRID_LINE_DUAL_ONLINE_LOGO = "target_type_grid_line_dual_online.png";

  public static final String HDMI_LOGO = "interface_hdmi_logo.png";
  public static final String DP_LOGO = "interface_dp_logo.png";
  public static final String DVI_LOGO = "interface_dvi_logo.png";
  public static final String USB_LOGO = "interface_usb_logo.png";
  public static final String GRIDLINE_LOGO = "interface_grid_line_logo.png";
  public static final String MATRIX_ONLINE_LOGO = "matrix_online.png";
  public static final String MATRIX_OFFLINE_LOGO = "matrix_offline.png";
  public static final Color SELECTED_COLOR = Color.rgb(0xf0, 0x85, 0x19);

  public static final double MIN_SCALE = 0.1;
  public static final double MAX_SCALE = 5;
  public static final double SCALE_FACTOR = 1.1;
}
