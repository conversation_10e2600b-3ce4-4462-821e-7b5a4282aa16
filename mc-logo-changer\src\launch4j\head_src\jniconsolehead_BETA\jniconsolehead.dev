[Project]
FileName=jniconsolehead.dev
Name=jniconsolehead
UnitCount=6
Type=1
Ver=2
ObjFiles=
Includes="C:\Program Files (x86)\Java\jdk 1.4\include";"C:\Program Files (x86)\Java\jdk 1.4\include\win32"
Libs=
PrivateResource=
ResourceIncludes=
MakeIncludes=
Compiler=
CppCompiler=
Linker=-n_@@_
IsCpp=0
Icon=
ExeOutput=
ObjectOutput=..\..\head_jni_BETA
OverrideOutput=0
OverrideOutputName=jniconsolehead.exe
HostApplication=
Folders=
CommandLine=
UseCustomMakefile=0
CustomMakefile=Makefile.win
IncludeVersionInfo=0
SupportXPThemes=0
CompilerSet=0
CompilerSettings=000000d000000000000001000
LogOutput=
LogOutputEnabled=0

[Unit1]
FileName=jniconsolehead.c
CompileCpp=0
Folder=jniconsolehead
Compile=1
Link=1
Priority=1000
OverrideBuildCmd=0
BuildCmd=

[VersionInfo]
Major=0
Minor=1
Release=1
Build=1
LanguageID=1033
CharsetID=1252
CompanyName=
FileVersion=*******
FileDescription=Developed using the Dev-C++ IDE
InternalName=
LegalCopyright=
LegalTrademarks=
OriginalFilename=
ProductName=
ProductVersion=
AutoIncBuildNr=0
SyncProduct=0

[Unit2]
FileName=..\resource.h
CompileCpp=0
Folder=jniconsolehead
Compile=1
Link=1
Priority=1000
OverrideBuildCmd=0
BuildCmd=

[Unit3]
FileName=..\head.c
CompileCpp=0
Folder=jniconsolehead
Compile=1
Link=1
Priority=1000
OverrideBuildCmd=0
BuildCmd=

[Unit4]
FileName=..\head.h
CompileCpp=0
Folder=jniconsolehead
Compile=1
Link=1
Priority=1000
OverrideBuildCmd=0
BuildCmd=

[Unit5]
FileName=..\jnihead.h
Folder=jniconsolehead
Compile=1
Link=1
Priority=1000
OverrideBuildCmd=0
BuildCmd=
CompileCpp=0

[Unit6]
FileName=..\jnihead.c
CompileCpp=0
Folder=jniconsolehead
Compile=1
Link=1
Priority=1000
OverrideBuildCmd=0
BuildCmd=

