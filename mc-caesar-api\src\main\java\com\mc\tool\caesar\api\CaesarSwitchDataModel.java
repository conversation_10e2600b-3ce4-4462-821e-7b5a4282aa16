package com.mc.tool.caesar.api;

import com.google.gson.annotations.Expose;
import com.mc.common.util.PlatformUtility;
import com.mc.tool.caesar.api.CaesarConstants.User;
import com.mc.tool.caesar.api.communication.BasicController;
import com.mc.tool.caesar.api.communication.ConnectionListener;
import com.mc.tool.caesar.api.datamodel.AbstractData;
import com.mc.tool.caesar.api.datamodel.ConsoleData;
import com.mc.tool.caesar.api.datamodel.CpuData;
import com.mc.tool.caesar.api.datamodel.ExtenderData;
import com.mc.tool.caesar.api.datamodel.ExtenderInfo;
import com.mc.tool.caesar.api.datamodel.ExtenderNetworkInfo;
import com.mc.tool.caesar.api.datamodel.ExtenderStatusInfo;
import com.mc.tool.caesar.api.datamodel.FunctionKeyData;
import com.mc.tool.caesar.api.datamodel.LicenseData;
import com.mc.tool.caesar.api.datamodel.LoginResponse;
import com.mc.tool.caesar.api.datamodel.MatrixData;
import com.mc.tool.caesar.api.datamodel.MatrixDefinitionData;
import com.mc.tool.caesar.api.datamodel.MatrixStatus;
import com.mc.tool.caesar.api.datamodel.ModuleData;
import com.mc.tool.caesar.api.datamodel.MultiScreenData;
import com.mc.tool.caesar.api.datamodel.MultiviewData;
import com.mc.tool.caesar.api.datamodel.MultiviewLayoutType;
import com.mc.tool.caesar.api.datamodel.MultiviewOutputMode;
import com.mc.tool.caesar.api.datamodel.NetworkData;
import com.mc.tool.caesar.api.datamodel.OpticalModuleInfo;
import com.mc.tool.caesar.api.datamodel.PortData;
import com.mc.tool.caesar.api.datamodel.ResolutionData;
import com.mc.tool.caesar.api.datamodel.SourceCropData;
import com.mc.tool.caesar.api.datamodel.SystemTimeData;
import com.mc.tool.caesar.api.datamodel.TxRxGroupData;
import com.mc.tool.caesar.api.datamodel.UserData;
import com.mc.tool.caesar.api.datamodel.UserGroupData;
import com.mc.tool.caesar.api.datamodel.VersionSet;
import com.mc.tool.caesar.api.datamodel.extargs.ExtArgObject;
import com.mc.tool.caesar.api.datamodel.extargs.ExtenderAnalogAudioInput;
import com.mc.tool.caesar.api.datamodel.vp.Vp6OutputData;
import com.mc.tool.caesar.api.datamodel.vp.VpConConfigData;
import com.mc.tool.caesar.api.datamodel.vp.VpConsoleData;
import com.mc.tool.caesar.api.datamodel.vp.VpConsoleData.VpResolution;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.exception.DeviceConnectionException;
import com.mc.tool.caesar.api.interfaces.CommitRollback;
import com.mc.tool.caesar.api.interfaces.ConfigDataModel;
import com.mc.tool.caesar.api.interfaces.DataObject;
import com.mc.tool.caesar.api.interfaces.Resetable;
import com.mc.tool.caesar.api.interfaces.ServiceModeFeature;
import com.mc.tool.caesar.api.interfaces.SwitchDataModel;
import com.mc.tool.caesar.api.interfaces.SwitchModuleData;
import com.mc.tool.caesar.api.interfaces.Threshold;
import com.mc.tool.caesar.api.utils.CfgError;
import com.mc.tool.caesar.api.utils.DataChecker;
import com.mc.tool.caesar.api.utils.IpUtil;
import com.mc.tool.caesar.api.utils.UniqueKeyGenerator;
import com.mc.tool.caesar.api.utils.Utilities;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javax.xml.bind.DatatypeConverter;

/**
 * .
 */
public class CaesarSwitchDataModel extends CaesarConfigDataModel
    implements SwitchDataModel, Resetable, ServiceModeFeature {

  private static final Logger LOG = Logger.getLogger(CaesarSwitchDataModel.class.getName());
  public static final String PROPERTY_IO_CAPABLE = "SwitchDataModel.ioCapable";
  public static final String PROPERTY_RECERTIFICATION = "SwitchDataModel.recertification";
  private static final int RELOAD_DELAY = 2000;
  @Expose
  private SwitchModuleData switchModuleData; // 模块与端口数据
  protected final ReentrantLock lock = new ReentrantLock(true);
  private final CaesarController controller = new CaesarController(this);
  @Expose
  private final long uniqueId;

  @Expose
  private final boolean onlyConfig; //只有配置文件的信息

  protected BooleanProperty connected = new SimpleBooleanProperty();
  private ConnectionListener connectionListener;
  private final Map<String, byte[]> serialCache = new HashMap<>();
  @Expose
  private final LicenseData licenseData = new LicenseData();
  @Expose
  private VpDataModel vpDataModel;

  @Expose
  private DbDataModel dbDataModel;

  private ConnectivityChecker connectivityChecker = null;

  protected boolean saveRequired;
  private long lastReloadConfigTimestamp = 0L;
  private long lastReloadSystemTimestamp = 0L;

  private AtomicInteger updateCount = new AtomicInteger(0);

  public void beginUpdate() {
    updateCount.incrementAndGet();
  }

  public void endUpdate() {
    updateCount.decrementAndGet();
  }

  public boolean isUpdating() {
    return updateCount.get() != 0;
  }

  public boolean isOnlyConfig() {
    return onlyConfig;
  }

  public boolean isDemo() {
    return false;
  }

  /**
   * .
   */
  public CaesarSwitchDataModel(boolean onlyConfig) {
    this.uniqueId = UniqueKeyGenerator.getInstance().generateId();
    this.controller.addPropertyChangeListener(BasicController.PROPERTY_IO_CAPABLE,
        pce -> this.getChangeSupport()
            .firePropertyChange(CaesarSwitchDataModel.PROPERTY_IO_CAPABLE, pce.getOldValue(),
                pce.getNewValue()));
    this.saveRequired = false;
    this.onlyConfig = onlyConfig;
  }

  public CaesarSwitchDataModel() {
    this(false);
  }


  /**
   * .
   *
   * @return id
   * @brief 获取model的id，每个model的id都不一样的
   */
  @Override
  public long getIdentifier() {
    return this.uniqueId;
  }

  public String getLocalHost() {
    return IpUtil.getAddressString(
        getConfigData().getSystemConfigData().getNetworkDataCurrent1().getAddress());
  }

  @Override
  protected Collection<? extends CommitRollback> getDependentCommitRollbacks() {
    Collection<CommitRollback> crs = new ArrayList<>(super.getDependentCommitRollbacks());

    crs.add(this.switchModuleData);

    return crs;
  }

  @Override
  public void initDefaults() {
    super.initDefaults();

    this.getSwitchModuleData().initDefaults();
  }

  /**
   * .
   */
  public void clearSerialCache() {
    if (!isDemo()) {
      this.serialCache.clear();
    }
  }

  public Map<String, byte[]> getSerialCache() {
    return this.serialCache;
  }

  /**
   * .
   *
   * @brief 获取许可数据
   */
  public LicenseData getLicenseData() {
    return this.licenseData;
  }

  protected VpDataModel createVpDataModel() {
    return new VpDataModel(getChangeSupport(), this, lock, () -> saveRequired = true);
  }

  /**
   * .
   */
  public VpDataModel getVpDataModel() {
    if (vpDataModel == null) {
      vpDataModel = createVpDataModel();
    }
    return vpDataModel;
  }

  /**
   * 获取数据库数据.
   */
  public DbDataModel getDbDataModel() {
    if (dbDataModel == null) {
      dbDataModel = new DbDataModel();
    }
    return dbDataModel;
  }

  public void beginUpdateVideoWall()
      throws DeviceConnectionException, BusyException, ConfigException {
    getController().beginUpdateVideoWall();
  }

  public void endUpdateVideoWall()
      throws DeviceConnectionException, BusyException, ConfigException {
    getController().endUpdateVideoWall();
  }

  /**
   * .
   *
   * @brief 向设备发送系统数据
   */
  public void sendSystemData() throws DeviceConnectionException, BusyException {
    try {
      getController().setSystemData();
      this.saveRequired = true;
    } catch (ConfigException ex) {
      LOG.log(Level.WARNING, null, ex);
    }
  }

  /**
   * .
   *
   * @brief 向设备发送矩阵数据
   */
  public void sendMatrixData(Iterable<MatrixData> matrixDatas)
      throws DeviceConnectionException, BusyException {
    try {
      getController().setMatData(matrixDatas);
      this.saveRequired = true;
    } catch (ConfigException ex) {
      LOG.log(Level.WARNING, null, ex);
    }
  }

  /**
   * .
   *
   * @brief 向设备发送用户数据
   */
  public void sendUserData(Iterable<UserData> userDatas)
      throws DeviceConnectionException, BusyException {
    try {
      getController().setUserData(userDatas);
      this.saveRequired = true;
    } catch (ConfigException ex) {
      LOG.log(Level.WARNING, null, ex);
    }
  }

  /**
   * .
   */
  public void sendUserGroupData(Iterable<UserGroupData> userGroupDatas)
      throws DeviceConnectionException, BusyException {
    try {
      getController().setUserGroupData(userGroupDatas);
      this.saveRequired = true;
    } catch (ConfigException ex) {
      LOG.log(Level.WARNING, null, ex);
    }
  }

  /**
   * .
   *
   * @brief 向设备发送extender数据
   */
  public void sendExtenderData(Iterable<ExtenderData> extenderDatas)
      throws DeviceConnectionException, BusyException {
    try {
      getController().setExtenderData(extenderDatas);
      this.saveRequired = true;
    } catch (ConfigException ex) {
      LOG.log(Level.WARNING, null, ex);
    }
  }

  /**
   * .
   *
   * @brief 向设备发送cpu数据
   */
  public void sendCpuData(Iterable<CpuData> cpuDatas)
      throws DeviceConnectionException, BusyException {
    try {
      getController().setCpuData(cpuDatas);
      this.saveRequired = true;
    } catch (ConfigException ex) {
      LOG.log(Level.WARNING, null, ex);
    }
  }

  /**
   * .
   *
   * @brief 向设备发送console数据
   */
  public void sendConsoleData(Iterable<ConsoleData> consoleDatas)
      throws DeviceConnectionException, BusyException {
    try {
      getController().setConsoleData(consoleDatas);
      this.saveRequired = true;
    } catch (ConfigException ex) {
      LOG.log(Level.WARNING, null, ex);
    }
  }

  /**
   * .
   */
  public void sendMultiscreenData(Iterable<MultiScreenData> datas)
      throws DeviceConnectionException, BusyException {
    try {
      getController().setMultiScreenData(datas);
      this.saveRequired = true;
    } catch (ConfigException ex) {
      LOG.log(Level.WARNING, null, ex);
    }
  }

  /**
   * 向设备发送Multiview数据.
   */
  public void sendMultiviewData(Iterable<MultiviewData> datas)
      throws DeviceConnectionException, BusyException {
    try {
      getController().setMultiviewData(datas);
      this.saveRequired = true;
    } catch (ConfigException ex) {
      LOG.log(Level.WARNING, null, ex);
    }
  }

  /**
   * 向设备发送source crop数据.
   */
  public void sendSourceCropData(Iterable<SourceCropData> sourceCropDatas)
      throws DeviceConnectionException, BusyException {
    try {
      getController().setSourceCropData(sourceCropDatas);
      this.saveRequired = true;
    } catch (ConfigException ex) {
      LOG.log(Level.WARNING, null, ex);
    }
  }

  /**
   * 设置多画面布局.
   */
  public void setMultiviewLayout(int id, MultiviewLayoutType layout)
      throws DeviceConnectionException, BusyException {
    try {
      getController().setMultiviewLayout(id, layout);
      this.saveRequired = true;
    } catch (ConfigException ex) {
      LOG.log(Level.WARNING, null, ex);
    }
  }

  /**
   * 设置多画面输出模式.
   */
  public void setMultiviewOutputMode(int id, MultiviewOutputMode outputMode)
      throws DeviceConnectionException, BusyException {
    try {
      getController().setMultiviewOutputMode(id, outputMode);
      this.saveRequired = true;
    } catch (ConfigException ex) {
      LOG.log(Level.WARNING, null, ex);
    }
  }



  /**
   * .
   */
  public void sendTxRxGroupData(Iterable<TxRxGroupData> datas)
      throws DeviceConnectionException, BusyException {
    try {
      getController().setTxRxGroupData(datas);
      this.saveRequired = true;
    } catch (ConfigException ex) {
      LOG.log(Level.WARNING, null, ex);
    }
  }

  /**
   * 电源pdu控制指令.
   */
  public void switchPowerControl(CpuData cpuData, int mode)
      throws DeviceConnectionException, BusyException {
    try {
      getController().switchPowerControl(cpuData, mode);
      this.saveRequired = true;
    } catch (ConfigException ex) {
      LOG.log(Level.WARNING, null, ex);
    }
  }

  /**
   * .
   *
   * @brief 向设备写入键盘的macro
   */
  public void sendFunctionKeyData(Iterable<FunctionKeyData> functionKeyDatas)
      throws DeviceConnectionException, BusyException {
    try {
      getController().setFunctionKeyData(functionKeyDatas);
      this.saveRequired = true;
    } catch (ConfigException ex) {
      LOG.log(Level.WARNING, null, ex);
    }
  }

  /**
   * .
   */
  public void sendPortData(Iterable<PortData> portData)
      throws DeviceConnectionException, BusyException {
    try {
      getController().setPortData(portData);
      this.saveRequired = true;
    } catch (ConfigException ex) {
      LOG.log(Level.WARNING, null, ex);
    }
  }

  /**
   * 发送vpcon的自定义输出数据.
   *
   * @param consoleData  vpcon
   * @param vp6OutputData 输出数据.
   * @throws BusyException 设备正忙
   */
  public void sendVpconOutputData(VpConsoleData consoleData, Vp6OutputData vp6OutputData)
      throws BusyException {
    if (!consoleData.isStatusOnline()) {
      LOG.log(Level.WARNING,
          "Fail to send vpcon output data for " + consoleData.getName() + " offline");
      return;
    }
    try {
      ConsoleData readConsoleData = consoleData.getInPort(0);
      if (readConsoleData == null) {
        LOG.log(Level.SEVERE, "sendVpConsoleData:ConsoleData Error", new Object());
        return;
      }
      ExtenderData extenderData = readConsoleData.getExtenderData(0);
      if (extenderData == null) {
        LOG.log(Level.SEVERE, "sendVpConsoleData:ExtenderData Error", new Object());
        return;
      }

      VpConConfigData vpConConfigData = consoleData.getConfigData();
      if (vpConConfigData == null) {
        LOG.log(Level.SEVERE, "sendVpConsoleData:Empty config data", new Object());
        return;
      }

      byte level1 = (byte) Utilities.getLevel1(this, extenderData);
      byte level2 = (byte) Utilities.getLevel2(this, extenderData);
      getExternalController(level1).setVpconOutputData(level1, level2, vp6OutputData);

      saveRequired = true;
    } catch (ConfigException | IOException | RuntimeException ex) {
      LOG.log(Level.WARNING, "Fail to send vpcon output data for " + consoleData.getName(), ex);
    }
  }

  /**
   * 发送VPCON的配置数据.
   */
  public void sendVpconConfigData(VpConsoleData consoleData)
      throws DeviceConnectionException, BusyException {
    if (!consoleData.isStatusOnline()) {
      LOG.log(Level.WARNING,
          "Fail to send vpcon config data for " + consoleData.getName() + " offline");
      return;
    }
    try {
      ConsoleData readConsoleData = consoleData.getInPort(0);
      if (readConsoleData == null) {
        LOG.log(Level.SEVERE, "sendVpConsoleData:ConsoleData Error", new Object());
        return;
      }
      ExtenderData extenderData = readConsoleData.getExtenderData(0);
      if (extenderData == null) {
        LOG.log(Level.SEVERE, "sendVpConsoleData:ExtenderData Error", new Object());
        return;
      }

      VpConConfigData vpConConfigData = consoleData.getConfigData();
      if (vpConConfigData == null) {
        LOG.log(Level.SEVERE, "sendVpConsoleData:Empty config data", new Object());
        return;
      }

      LOG.log(Level.INFO, "vpcon " + consoleData.getName() + " data:");
      vpConConfigData.print();
      byte level1 = (byte) Utilities.getLevel1(this, extenderData);
      byte level2 = (byte) Utilities.getLevel2(this, extenderData);
      getExternalController(level1).setVpconConfigData(level1, level2, vpConConfigData);

      saveRequired = true;
    } catch (ConfigException | IOException | RuntimeException ex) {
      LOG.log(Level.WARNING,
          "Fail to send vpcon config data for " + consoleData.getName(), ex);
    }
  }

  /**
   * 获取vpcon的配置数据.
   */
  public VpConConfigData getVpconConfigData(VpConsoleData vpConsoleData) throws BusyException {
    try {
      ConsoleData readConsoleData = vpConsoleData.getInPort(0);
      if (readConsoleData == null) {
        LOG.log(Level.SEVERE, "getVpconConfigData Error", new Object());
        return null;
      }
      ExtenderData extenderData = readConsoleData.getExtenderData(0);
      if (extenderData == null) {
        LOG.log(Level.SEVERE, "getVpconConfigData Error", new Object());
        return null;
      }

      byte level1 = (byte) Utilities.getLevel1(this, extenderData);
      byte level2 = (byte) Utilities.getLevel2(this, extenderData);

      VpConConfigData result = getExternalController(level1)
          .getVpconConfigDataReal(level1, level2, vpConsoleData.getType());

      vpConsoleData.setConfigData(result);
      return result;
    } catch (ConfigException | IOException | RuntimeException ex) {
      LOG.log(Level.WARNING, "Fail to get vp config data for " + vpConsoleData.getName(), ex);
      return null;
    }
  }

  /**
   * 获取分辨率.
   *
   * @param extenderData 外设
   */
  public boolean getExtResolution(ExtenderData extenderData) {
    if (extenderData == null) {
      return false;
    }
    if (extenderData.getPort() == 0 && extenderData.getRdPort() == 0) {
      return true;
    }
    byte level1 = (byte) Utilities.getLevel1(this, extenderData);
    byte level2 = (byte) Utilities.getLevel2(this, extenderData);
    for (int i = 0; i < extenderData.getExtenderStatusInfo().getInterfaceCount(); i++) {
      try {
        ResolutionData resolutionData = getExternalController(level1)
            .getExtResolution(level1, level2, i);
        if (resolutionData != null) {
          extenderData.setResolution(i, resolutionData);
        }
      } catch (DeviceConnectionException | ConfigException | BusyException exception) {
        LOG.warning(
            "Fail to read EXT resolution. Level1:" + level1 + ", Level2:" + level2 + ", index: "
                + i);
      }
    }
    return true;
  }

  /**
   * 获取vpcon的分辨率.
   */
  public void getVpconResolution(VpConsoleData vpConsoleData)
      throws BusyException, ConfigException, DeviceConnectionException {
    ConsoleData readConsoleData = vpConsoleData.getInPort(0);
    if (readConsoleData == null) {
      LOG.log(Level.SEVERE, "getVpconResolution Error", new Object());
      return;
    }
    ExtenderData extenderData = readConsoleData.getExtenderData(0);
    if (extenderData == null) {
      LOG.log(Level.SEVERE, "getVpconResolution Error", new Object());
      return;
    }

    final byte level1 = (byte) Utilities.getLevel1(this, extenderData);
    final byte level2 = (byte) Utilities.getLevel2(this, extenderData);

    VpResolution[] result = getExternalController(level1).getVpResolution(level1, level2);
    vpConsoleData.setResolutions(result);
  }

  /**
   * 获取vpcon的分辨率.
   */
  public VpResolution[] getVpconResolution(ExtenderData extenderData)
      throws BusyException, ConfigException, DeviceConnectionException {
    if (extenderData == null) {
      LOG.log(Level.SEVERE, "getVpconResolution Error", new Object());
      return new VpResolution[0];
    }

    final byte level1 = (byte) Utilities.getLevel1(this, extenderData);
    final byte level2 = (byte) Utilities.getLevel2(this, extenderData);

    return getExternalController(level1).getVpResolution(level1, level2);
  }

  /**
   * .
   */
  public void reloadVpconResolution() {
    int count = getConfigMetaData().getVpconsoleCount();

    for (int i = 0; i < count; i++) {
      VpConsoleData vpConsoleData = getConfigData().getVpConsoleData(i);
      if (!vpConsoleData.isStatusActive() || !vpConsoleData.isStatusOnline()) {
        continue;
      }
      try {
        getVpconResolution(vpConsoleData);
      } catch (BusyException ex) {
        LOG.log(Level.WARNING, "reload vpcon resolution fail", ex);
      } catch (ConfigException ex) {
        LOG.log(Level.WARNING, "reload vpcon resolution fail", ex);
        if (ex.getError() != CfgError.NOT_FOUND) {
          return;
        }
      } catch (DeviceConnectionException ex) {
        LOG.log(Level.WARNING, "reload vpcon resolution fail", ex);
        return;
      }
    }

  }

  /**
   * 获取外设网络信息.
   */
  public ExtenderNetworkInfo getExtNetworkInfo(ExtenderData extenderData)
      throws BusyException, ConfigException, DeviceConnectionException {
    if (extenderData == null) {
      LOG.log(Level.SEVERE, "getVpconResolution Error", new Object());
      return null;
    }
    final byte level1 = (byte) Utilities.getLevel1(this, extenderData);
    final byte level2 = (byte) Utilities.getLevel2(this, extenderData);
    return getExternalController(level1).getExtNetworkInfo(level1, level2);
  }

  /**
   * 设置外设网络信息.
   */
  public void setExtNetworkInfo(ExtenderData extenderData, ExtenderNetworkInfo info)
      throws BusyException, ConfigException, DeviceConnectionException {
    if (extenderData == null) {
      LOG.log(Level.SEVERE, "getVpconResolution Error", new Object());
      return;
    }
    final byte level1 = (byte) Utilities.getLevel1(this, extenderData);
    final byte level2 = (byte) Utilities.getLevel2(this, extenderData);
    getExternalController(level1).setExtNetworkInfo(level1, level2, info);
  }


  /**
   * 获取所有的模块的数据，如果是级联的，所有的设备的模块都会获取得到.
   */
  protected SwitchModuleData createSwitchModuleData() {
    return new CaesarSwitchModuleData(getChangeSupport(), this);
  }

  /**
   * .
   */
  public SwitchModuleData getSwitchModuleData() {
    if (this.switchModuleData == null) {
      this.switchModuleData = createSwitchModuleData();
    }
    return this.switchModuleData;
  }

  /**
   * .
   *
   * @brief 获取设备时间
   */
  public SystemTimeData getTime() throws ConfigException, BusyException {
    this.lock.lock();
    try {
      return this.controller.getTime();
    } catch (DeviceConnectionException | ConfigException dce) {
      throw new ConfigException(CfgError.CONNECTION_ERROR, dce);
    } finally {
      this.lock.unlock();
    }
  }

  /**
   * .
   *
   * @brief 设置设备时间
   */
  public void setTime(LocalDateTime date) throws ConfigException, BusyException {
    this.lock.lock();
    try {
      this.controller.setTime(date);
    } catch (DeviceConnectionException | ConfigException ex) {
      throw new ConfigException(CfgError.CONNECTION_ERROR, ex);
    } finally {
      this.lock.unlock();
    }
  }

  /**
   * 发送获取日志请求.
   */
  public void startRecSyslog(final byte slot, final byte port)
      throws ConfigException, BusyException {
    this.lock.lock();
    try {
      getController().startReceiveSyslog(slot, port);
    } catch (DeviceConnectionException | ConfigException dce) {
      LOG.log(Level.SEVERE, "RecSyslog: " + slot + "_" + port, dce);
      throw new ConfigException(CfgError.CONNECTION_ERROR, dce);
    } finally {
      this.lock.unlock();
    }
  }

  /**
   * .
   */
  public void reloadNetworkData() throws ConfigException, BusyException {
    this.lock.lock();
    try {
      if (this.lastReloadConfigTimestamp + RELOAD_DELAY < System.currentTimeMillis()) {
        getController().getSystemData();
      }
      NetworkData networkData1 = getConfigData().getSystemConfigData().getNetworkDataCurrent1();
      networkData1.setMacAddress(getController().readMacAddress());
      getConfigData().getSystemConfigData().commit(Threshold.ALL);
    } catch (DeviceConnectionException | ConfigException dce) {
      throw new ConfigException(CfgError.CONNECTION_ERROR, dce);
    } finally {
      this.lock.unlock();
    }
  }

  String getMacAddress() throws ConfigException, BusyException, DeviceConnectionException {
    return getController().readMacAddress();
  }

  /**
   * 获取osd MD5.
   *
   * @param extenderData 外设数据
   * @return MD5, 如果获取失败，返回空字符串.
   * @throws BusyException             .
   * @throws ConfigException           .
   * @throws DeviceConnectionException .
   */
  public String getOsdMd5(ExtenderData extenderData)
      throws DeviceConnectionException, ConfigException, BusyException {
    int level1 = Utilities.getLevel1(this, extenderData);
    int level2 = Utilities.getLevel2(this, extenderData);
    CaesarController controller = getExternalController(level1);
    if (controller != null) {
      return controller.getOsdMd5((byte) level1, (byte) level2);
    } else {
      return "";
    }
  }

  /**
   * 获取EDID.
   */
  public void getEdid(ExtenderData extenderData, int index) throws BusyException, ConfigException {
    extenderData.setEdid(getEdidRet(extenderData, index), index);
  }

  /**
   * 获取edid.
   */
  public byte[] getEdidRet(ExtenderData extenderData, int index)
      throws BusyException, ConfigException {
    int level1 = Utilities.getLevel1(this, extenderData);
    int level2 = Utilities.getLevel2(this, extenderData);

    try {
      byte[] data = getExternalController(level1).getEdidData(
          (byte) level1, (byte) level2, (byte) index);
      return data;
    } catch (DeviceConnectionException ex) {
      LOG.log(Level.WARNING,
          String.format("Fail to get edid for %d:%d %d!", level1, level2, index));
      throw new ConfigException(CfgError.CONNECTION_ERROR, ex);
    } catch (RuntimeException exception) {
      LOG.log(Level.WARNING,
          String.format("Fail to get edid for %d:%d %d!", level1, level2, index));
      throw new ConfigException(CfgError.NOT_FOUND, exception);
    }
  }

  /**
   * 设置EDID.
   */
  public void setEdid(ExtenderData extenderData, int index, byte[] edid)
      throws BusyException, ConfigException {
    int level1 = Utilities.getLevel1(this, extenderData);
    int level2 = Utilities.getLevel2(this, extenderData);

    try {
      getExternalController(level1).setEdidData((byte) level1, (byte) level2, (byte) index, edid);
    } catch (DeviceConnectionException | ConfigException | RuntimeException ex) {
      LOG.log(Level.WARNING, "Fail to set edid for " + extenderData.getName(), ex);
      throw new ConfigException(CfgError.CONNECTION_ERROR, ex);
    }
  }

  /**
   * extender info.
   */
  public void getExtInfo(ExtenderData extenderData) throws BusyException, ConfigException {
    int level1 = Utilities.getLevel1(this, extenderData);
    int level2 = Utilities.getLevel2(this, extenderData);

    try {
      ExtenderStatusInfo extInfo = getExternalController(level1).getExtInfo(
          (byte) level1, (byte) level2);
      extenderData.getExtenderStatusInfo().readData(extInfo);
    } catch (DeviceConnectionException | ConfigException ex) {
      LOG.log(Level.WARNING, String.format("Fail to get extinfo for %d:%d!", level1, level2));
      throw new ConfigException(CfgError.CONNECTION_ERROR, ex);
    } catch (RuntimeException exception) {
      LOG.log(Level.WARNING, String.format("Fail to get extinfo for %d:%d!", level1, level2));
      throw new ConfigException(CfgError.NOT_FOUND, exception);
    }
  }

  /**
   * 获取外设参数.
   *
   * @param extenderData 要获取信息的外设
   * @throws ConfigException           配置数据错误
   * @throws BusyException             设备忙
   * @throws DeviceConnectionException 设备连接异常
   */
  public void getAllExtArgs(ExtenderData extenderData)
      throws ConfigException, BusyException, DeviceConnectionException {
    Version version = getConfigMetaData().getUtilVersion();
    List<CaesarExtArg> extArgs = CaesarExtArgUtility.getExtArgs(version, extenderData);

    int level1 = Utilities.getLevel1(this, extenderData);
    int level2 = Utilities.getLevel2(this, extenderData);
    Map<Integer, byte[]> result =
        getExternalController(level1).getExtArgs((byte) level1, (byte) level2,
            extArgs.stream().map(CaesarExtArg::getValue).collect(Collectors.toList()));

    extArgs.forEach((extArg) -> {
      CaesarExtArgUtility.CaesarExtArgReader reader = CaesarExtArgUtility.READERS.get(extArg);
      if (reader == null) {
        LOG.warning("Fail to get reader for " + extArg);
      } else {
        reader.apply(extenderData, level1, level2, result);
      }
    });
  }

  /**
   * 设置外设参数.
   *
   * @param extenderData 外设数据
   * @param extArgId     参数id
   * @param arg          参数值
   */
  public void setExtArg(ExtenderData extenderData, int extArgId, ExtArgObject arg)
      throws ConfigException, BusyException, DeviceConnectionException {
    int level1 = Utilities.getLevel1(this, extenderData);
    int level2 = Utilities.getLevel2(this, extenderData);
    Map<Integer, byte[]> map = new HashMap<>();
    map.put(extArgId, arg.toBytes());
    getExternalController(level1).setExtArgs((byte) level1, (byte) level2, map);
  }

  /**
   * 设置外设布尔值参数.
   *
   * @param extenderData 外设数据
   * @param extArgId     参数id
   * @param enable       参数值
   */
  public void setExtBooleanArg(ExtenderData extenderData, int extArgId, boolean enable)
      throws ConfigException, BusyException, DeviceConnectionException {
    int level1 = Utilities.getLevel1(this, extenderData);
    int level2 = Utilities.getLevel2(this, extenderData);
    Map<Integer, byte[]> map = new HashMap<>();
    map.put(extArgId, new byte[] {(byte) (enable ? 1 : 0)});
    getExternalController(level1).setExtArgs((byte) level1, (byte) level2, map);
  }

  /**
   * 设置外设整型值参数.
   *
   * @param extenderData 外设数据
   * @param extArgId     参数id
   * @param arg          参数值
   */
  public void setExtNumberArg(ExtenderData extenderData, int extArgId, int arg)
      throws ConfigException, BusyException, DeviceConnectionException {
    int level1 = Utilities.getLevel1(this, extenderData);
    int level2 = Utilities.getLevel2(this, extenderData);
    Map<Integer, byte[]> map = new HashMap<>(2);
    map.put(extArgId, new byte[] {(byte) (arg & 0xFF), (byte) (arg >> 8 & 0xFF)});
    getExternalController(level1).setExtArgs((byte) level1, (byte) level2, map);
  }

  /**
   * 设置模拟音频输入.
   *
   * @param extenderData 要设置的外设
   * @param input        输入值
   * @throws ConfigException           配置数据有误
   * @throws BusyException             设备忙
   * @throws DeviceConnectionException 设备连接异常
   */
  public void setAnalogAudioInput(ExtenderData extenderData, ExtenderAnalogAudioInput input)
      throws ConfigException, BusyException, DeviceConnectionException {
    int level1 = Utilities.getLevel1(this, extenderData);
    int level2 = Utilities.getLevel2(this, extenderData);
    Map<Integer, byte[]> map = new HashMap<>();
    map.put((int) CaesarExtArg.EXT_ARG_ANALOG_INPUT_ID.getValue(), input.toBytes());
    getExternalController(level1).setExtArgs((byte) level1, (byte) level2, map);
  }

  /**
   * USB使能.
   */
  public void setExtUsbDiskEnable(ExtenderData data, boolean enable)
      throws BusyException, ConfigException {
    if (data == null) {
      LOG.warning("ExtenderData is null!");
      return;
    }
    int level1 = Utilities.getLevel1(this, data);
    int level2 = Utilities.getLevel2(this, data);
    Map<Integer, byte[]> args = new HashMap<>();
    args.put(CaesarExtArg.EXT_ARG_USB_ENABLE_ID.getValue(),
        new byte[] {(byte) (enable ? 1 : 0)});
    try {
      getExternalController(level1).setExtArgs((byte) level1, (byte) level2, args);
    } catch (DeviceConnectionException | ConfigException | RuntimeException dce) {
      LOG.log(Level.SEVERE, "Fail to set ext usb disk enable for " + data.getName(), dce);
      throw new ConfigException(CfgError.CONNECTION_ERROR, dce);
    }
  }

  /**
   * 触摸屏使能.
   */
  public void setExtTouchEnable(ExtenderData data, boolean enable)
      throws BusyException, ConfigException {
    if (data == null) {
      LOG.warning("ExtenderData is null!");
      return;
    }
    int level1 = Utilities.getLevel1(this, data);
    int level2 = Utilities.getLevel2(this, data);
    Map<Integer, byte[]> args = new HashMap<>();
    args.put((int) CaesarExtArg.EXT_ARG_TOUCHING_SCREEN_ID.getValue(),
        new byte[] {(byte) (enable ? 1 : 0)});
    try {
      getExternalController(level1).setExtArgs((byte) level1, (byte) level2, args);
    } catch (DeviceConnectionException | ConfigException | RuntimeException dce) {
      LOG.log(Level.SEVERE, "Fail to set ext touching screen enable for " + data.getName(), dce);
      throw new ConfigException(CfgError.CONNECTION_ERROR, dce);
    }
  }

  /**
   * getVersion.
   */
  public VersionSet getVersion(byte level1, byte level2) throws ConfigException, BusyException {
    VersionSet version;
    if ((level1 & 0xff) == 255) {
      new Exception("level1:" + level1 + "; level2:" + level2).printStackTrace();
    }
    if (!isDemo()) {
      try {
        this.lock.lock();
        version = getExternalController(level1).getVersion(level1, level2);
        return version;
      } catch (DeviceConnectionException dce) {
        LOG.log(Level.SEVERE, String.format("Level %d_%d: %s", level1, level2, dce.getMessage()));
        throw new ConfigException(CfgError.CONNECTION_ERROR, dce);
      } catch (ConfigException ce) {
        LOG.log(Level.SEVERE, "Level: " + level1 + "_" + level2, ce);
        if (ce.getError() == CfgError.NOT_FOUND) {
          return new VersionSet();
        } else {
          throw ce;
        }
      } catch (RuntimeException ce) {
        LOG.log(Level.SEVERE, "Level: " + level1 + "_" + level2, ce);
        return new VersionSet();
      } finally {
        this.lock.unlock();
      }
    }
    return new VersionSet();
  }

  /**
   * 获取双主控状态.
   */
  public MatrixStatus getMatrixActiveState() throws ConfigException, BusyException {
    this.lock.lock();
    try {
      return this.controller.getMatrixActiveState();
    } catch (DeviceConnectionException | ConfigException dce) {
      throw new ConfigException(CfgError.CONNECTION_ERROR, dce);
    } finally {
      this.lock.unlock();
    }
  }

  /**
   * .
   *
   * @brief 从设备读取系统数据
   */
  public void reloadSystemData() throws ConfigException, BusyException {
    this.lock.lock();
    try {
      if (this.lastReloadConfigTimestamp + RELOAD_DELAY > System.currentTimeMillis()) {
        return;
      }
      if (this.lastReloadSystemTimestamp + RELOAD_DELAY > System.currentTimeMillis()) {
        return;
      }
      getController().getSystemData();
      getConfigData().getSystemConfigData().commit(Threshold.ALL);
      this.lastReloadSystemTimestamp = System.currentTimeMillis();
    } catch (DeviceConnectionException dce) {
      throw new ConfigException(CfgError.CONNECTION_ERROR, dce);
    } finally {
      this.lock.unlock();
    }
  }

  /**
   * .
   *
   * @return 版本号
   * @brief 获取设备的配置版本
   */
  @Override
  public int getConfigVersion() throws ConfigException, BusyException {
    this.lock.lock();

    int version;
    try {
      version = getController().getConfigVersion();
    } catch (DeviceConnectionException dce) {
      throw new ConfigException(CfgError.CONNECTION_ERROR, dce);
    } finally {
      this.lock.unlock();
    }
    return version;
  }

  /**
   * 检查配置数据是否有错误.
   *
   * @return 错误的数量.
   */
  private int checkErrors() {
    List<PortData> errorPortList = new ArrayList<>();
    List<ExtenderData> errorExtenderList = new ArrayList<>();
    List<CpuData> errorCpuList = new ArrayList<>();
    List<ConsoleData> errorConsoleList = new ArrayList<>();
    return DataChecker
        .checkErrors(this, errorPortList, errorExtenderList, errorCpuList, errorConsoleList, false);
  }

  /**
   * 加载全部.
   *
   * @throws ConfigException 配置有错
   * @throws BusyException   设备正忙
   */
  public void reloadAll() throws ConfigException, BusyException {
    int reloadCount = 0;
    boolean checkPass = false;
    // 加载数据，如果监测到有错误，重新加载
    beginUpdate();
    try {
      while (!checkPass) {
        reloadConfigData();
        getSwitchModuleData().reloadModules();
        getSwitchModuleData().requestPorts();
        reloadCount++;
        checkPass = checkErrors() == 0;
        if (checkPass) {
          LOG.log(Level.INFO, "Reload config data check pass.");
        } else if (reloadCount >= 3) {
          break;
        } else {
          LOG.log(Level.WARNING, "Reload config data check fail. Reload again.");
          try {
            Thread.sleep(2000);
          } catch (InterruptedException exp) {
            LOG.log(Level.WARNING, "Sleep is interrupted!", exp);
          }
        }
      }

      // 加载级联矩阵中其他主机的配置
      String currentIp = Utilities.getIpFromModel(this);
      for (MatrixDefinitionData data : Utilities.getActiveMatrices(this)) {
        try {
          CaesarSwitchDataModelManager.getInstance()
              .bind(currentIp, data.getAddress());
          CaesarSwitchDataModel model = Utilities.getExternalModel(this, data.getAddress());
          if (model != this) {
            model.reloadConfigData();
            model.getSwitchModuleData().reloadModules();
            model.reloadCpuSerial();
          }
        } catch (ConfigException | BusyException exception) {
          LOG.warning("Fail to get model for " + data.getAddress() + "!");
        }
      }

      Version version = getConfigMetaData().getUtilVersion();
      reloadNetworkData();
      reloadExtenderVersion(version.hasExtVersion());

      if (needToReloadExtData()) {
        reloadExtInfo(version.hasExtenderStatusInfo());
        reloadExtArgs();
        reloadEdid();
      }
      reloadVpconResolution();
      reloadExtResolution(version.hasExtResolution());
      reloadSerial(version.hasExtSerial());

      getVpDataModel().reloadVideoWallGroup();
      getVpDataModel().reloadScenarioDatas();
      getDbDataModel().reloadAll();
    } finally {
      endUpdate();
    }
  }

  private boolean needToReloadExtData()
      throws ConfigException, BusyException {
    // 双主控且未激活的状态，不获取外设信息,参数和EDID
    if (getConfigMetaData().getUtilVersion().hasDoubleBackupMatrix()) {
      MatrixStatus matrixStatus = getMatrixActiveState();
      return !matrixStatus.isDoubleBackup() || matrixStatus.isActive();
    }
    return true;
  }

  /**
   * 加载外设数据.
   */
  public void reloadExtData(ExtenderData extenderData) {
    try {
      if (needToReloadExtData()) {
        reloadSingleExtInfo(extenderData,
            getConfigMetaData().getUtilVersion().hasExtenderStatusInfo());
        reloadSingleExtArgs(extenderData);
        if (extenderData.isVpConType()) {
          reloadSingleVpconEdid(extenderData);
        } else {
          reloadSingleExtEdid(extenderData);
        }
      }
      reloadSingleExtSerial(extenderData, getConfigMetaData().getUtilVersion().hasExtSerial());
    } catch (ConfigException | DeviceConnectionException | BusyException exception) {
      LOG.log(Level.WARNING, "Fail to load ext info !", exception);
    }
  }

  /**
   * 加载序列号.
   */
  public void reloadSerial(boolean onlyOldVersion) {

    // 加载外设序列号
    for (ExtenderData extenderData : getConfigDataManager().getActiveExtenders()) {
      try {
        reloadSingleExtSerial(extenderData, onlyOldVersion);
      } catch (BusyException exception) {
        LOG.log(Level.WARNING, "Fail to load serial !", exception);
      } catch (ConfigException exception) {
        LOG.log(Level.WARNING, "Fail to load serial !", exception);
        if (exception.getError() != CfgError.NOT_FOUND) {
          return;
        }
      }
    }

    try {
      reloadCpuSerial();
    } catch (BusyException | ConfigException exception) {
      LOG.log(Level.WARNING, "Fail to load cpu serial !", exception);
    }
  }

  /**
   * 加载单个外设序列号.
   */
  private void reloadSingleExtSerial(ExtenderData extenderData, boolean onlyOldVersion)
      throws ConfigException, BusyException {
    if (extenderData.getPort() == 0 && extenderData.getRdPort() == 0) {
      return;
    }
    if (extenderData.isUsbConType() || extenderData.isUsbCpuType()) {
      return;
    }
    if (onlyOldVersion && extenderData.isStatusNewVersion()) {
      return;
    }
    int level1 = Utilities.getLevel1(this, extenderData);
    int level2 = Utilities.getLevel2(this, extenderData);
    String serial = getSerial((byte) level1, (byte) level2);
    if (serial != null && !serial.isEmpty()) {
      extenderData.setSerial(serial);
    }
  }

  /**
   * 加载主控板序列号.
   *
   * @throws ConfigException 配置异常
   * @throws BusyException   设备正忙
   */
  public void reloadCpuSerial() throws ConfigException, BusyException {
    // 加载主板序列号
    String serial = getSerial(0, (byte) 0);
    getSwitchModuleData().getModuleData(0).setSerial(serial);
  }

  /**
   * 加载外设版本.
   *
   * @param onlyOldVersion 只加载旧版本外设的信息
   */
  public void reloadExtenderVersion(boolean onlyOldVersion) {
    try {
      // 加载非vpcon的外设的版本
      for (ExtenderData extenderData : getConfigDataManager().getActiveExtenders()) {
        if (extenderData.getPort() == 0 && extenderData.getRdPort() == 0) {
          continue;
        }

        if (extenderData.isVpConType()) {
          continue;
        }
        if (extenderData.isUsbConType() || extenderData.isUsbCpuType()) {
          continue;
        }
        if (onlyOldVersion && extenderData.isStatusNewVersion()) {
          continue;
        }

        int level1 = Utilities.getLevel1(this, extenderData);
        int level2 = Utilities.getLevel2(this, extenderData);
        VersionSet versionSet = getVersion((byte) level1, (byte) level2);
        extenderData.setVersion(versionSet);
      }
      // 加载vpcon的版本
      for (VpConsoleData vpConsoleData : getConfigDataManager().getActiveVpconsolses()) {
        if (!vpConsoleData.isStatusOnline()) {
          continue;
        }
        ConsoleData consoleData = vpConsoleData.getInPort(0);
        if (consoleData == null) {
          continue;
        }
        ExtenderData extenderData = consoleData.getExtenderData(0);
        if (extenderData == null) {
          continue;
        }
        if (extenderData.getPort() == 0 && extenderData.getRdPort() == 0) {
          continue;
        }
        int level1 = Utilities.getLevel1(this, extenderData);
        int level2 = Utilities.getLevel2(this, extenderData);
        VersionSet versionSet = getVersion((byte) level1, (byte) level2);
        extenderData.setVersion(versionSet);
      }
    } catch (ConfigException | BusyException exception) {
      LOG.log(Level.WARNING, "Fail to load version !", exception);
    }

  }

  protected void reloadEdid() {
    // 加载edid
    for (ExtenderData extenderData : getConfigDataManager().getActiveExtenders()) {
      try {
        reloadSingleExtEdid(extenderData);
      } catch (BusyException exception) {
        LOG.log(Level.WARNING, "Fail to load edid for " + extenderData.getName(), exception);
      } catch (ConfigException exception) {
        LOG.log(Level.WARNING, "Fail to load edid for " + extenderData.getName(), exception);
        if (exception.getError() != CfgError.NOT_FOUND) {
          return;
        }
      }
    }

    // vpcon的edid只能从第0个端口读取
    for (VpConsoleData vpConsoleData : getConfigDataManager().getActiveVpconsolses()) {
      if (!vpConsoleData.isStatusOnline()) {
        continue;
      }
      ConsoleData consoleData = vpConsoleData.getInPort(0);
      if (consoleData == null) {
        continue;
      }
      ExtenderData extenderData = consoleData.getExtenderData(0);
      if (extenderData == null || !extenderData.isStatusActive()) {
        continue;
      }
      if (extenderData.getPort() == 0 && extenderData.getRdPort() == 0) {
        continue;
      }

      for (int i = 0; i < vpConsoleData.getInPortCount(); i++) {
        ConsoleData innerConsole = vpConsoleData.getInPort(i);
        if (innerConsole != null && innerConsole.getExtenderData(0) != null) {
          try {
            byte[] data = getEdidRet(extenderData, i);
            innerConsole.getExtenderData(0).setEdid(data, 0);
          } catch (BusyException exception) {
            LOG.log(Level.WARNING,
                String.format("Fail to load edid for %s[%d]!", vpConsoleData.getName(), i + 1),
                exception);
          } catch (ConfigException exception) {
            LOG.log(Level.WARNING,
                String.format("Fail to load edid for %s[%d]!", vpConsoleData.getName(), i + 1),
                exception);
            if (exception.getError() != CfgError.NOT_FOUND) {
              return;
            }
          }
        }
      }
    }
  }

  /**
   * 加载单个外设的edid.
   */
  private void reloadSingleExtEdid(ExtenderData extenderData)
      throws BusyException, ConfigException {
    if (extenderData.getPort() == 0 && extenderData.getRdPort() == 0) {
      return;
    }
    if (extenderData.isUsbConType() || extenderData.isUsbCpuType() || extenderData
        .isVpConType()) {
      return;
    }
    for (int i = 0; i < extenderData.getExtenderStatusInfo().getInterfaceCount(); i++) {
      getEdid(extenderData, i);
    }
  }

  private void reloadSingleVpconEdid(ExtenderData extenderData)
      throws BusyException, ConfigException {
    for (VpConsoleData vpConsoleData : getConfigDataManager().getActiveVpconsolses()) {
      if (!vpConsoleData.isStatusOnline()) {
        continue;
      }
      ConsoleData consoleData = vpConsoleData.getInPort(0);
      if (consoleData == null) {
        continue;
      }
      ExtenderData ext = consoleData.getExtenderData(0);
      if (ext == null || !ext.isStatusActive()) {
        continue;
      }
      if (ext.getPort() == 0 && ext.getRdPort() == 0) {
        continue;
      }
      for (int i = 0; i < vpConsoleData.getInPortCount(); i++) {
        if (vpConsoleData.getInPort(i) != null
            && vpConsoleData.getInPort(i).getExtenderData(0) != null
            && vpConsoleData.getInPort(i).getExtenderData(0).getId() == extenderData.getId()) {
          byte[] data = getEdidRet(ext, i);
          extenderData.setEdid(data, 0);
          return;
        }
      }
    }
  }

  protected void reloadExtInfo(boolean onlyOldVersion) {
    // 加载外设Info
    for (ExtenderData extenderData : getConfigDataManager().getActiveExtenders()) {
      try {
        reloadSingleExtInfo(extenderData, onlyOldVersion);
      } catch (BusyException exception) {
        LOG.log(Level.WARNING, "Fail to load ext info !", exception);
      } catch (ConfigException exception) {
        LOG.log(Level.WARNING, "Fail to load ext info !", exception);
        if (exception.getError() != CfgError.NOT_FOUND) {
          return;
        }
      }
    }
  }

  /**
   * 加载单个外设的Info.
   */
  private void reloadSingleExtInfo(ExtenderData extenderData, boolean onlyOldVersion)
      throws BusyException, ConfigException {
    if (extenderData.getPort() == 0 && extenderData.getRdPort() == 0) {
      return;
    }
    if (extenderData.isUsbConType() || extenderData.isUsbCpuType()) {
      return;
    }
    if (!extenderData.isVpConType() && extenderData.isStatusNewVersion() && onlyOldVersion) {
      return;
    }
    getExtInfo(extenderData);
  }

  protected void reloadExtArgs() {
    for (ExtenderData extenderData : getConfigDataManager().getActiveExtenders()) {
      try {
        reloadSingleExtArgs(extenderData);
      } catch (BusyException exception) {
        LOG.log(Level.WARNING,
            String.format("Fail to load ext args for %s!", extenderData.getName()), exception);
      } catch (ConfigException exception) {
        LOG.log(Level.WARNING,
            String.format("Fail to load ext args for %s!", extenderData.getName()), exception);
        if (exception.getError() != CfgError.NOT_FOUND) {
          return;
        }
      } catch (DeviceConnectionException exception) {
        LOG.log(Level.WARNING,
            String.format("Fail to load ext args for %s!", extenderData.getName()), exception);
        return;
      }
    }
  }

  /**
   * 加载单个外设参数.
   */
  private void reloadSingleExtArgs(ExtenderData extenderData)
      throws ConfigException, BusyException, DeviceConnectionException {
    if (extenderData.getPort() == 0 && extenderData.getRdPort() == 0) {
      return;
    }
    if (extenderData.isUsbConType() || extenderData.isUsbCpuType() || extenderData
        .isVpConType()) {
      return;
    }
    getAllExtArgs(extenderData);
  }

  protected void reloadExtResolution(boolean onlyOldVersion) {
    for (ExtenderData extenderData : getConfigDataManager().getActiveExtenders()) {
      if (extenderData.getPort() == 0 && extenderData.getRdPort() == 0) {
        continue;
      }
      if (extenderData.isUsbConType() || extenderData.isUsbCpuType() || extenderData
          .isVpConType()) {
        continue;
      }
      if (onlyOldVersion && extenderData.isStatusNewVersion()) {
        continue;
      }
      getExtResolution(extenderData);
    }
  }

  @Override
  public void reloadConfigData() throws ConfigException, BusyException {
    this.lock.lock();
    try {
      if (this.lastReloadConfigTimestamp + RELOAD_DELAY > System.currentTimeMillis()) {
        return;
      }
      getConfigData().setMatrixStatus(getMatrixActiveState());
      getController().getVersion();
      getController().getMegaData(getConfigMetaData().getUtilVersion());
      if (getConfigMetaData().getUtilVersion().isVersionTooNew()) {
        throw new ConfigException(CfgError.VERSION, String
            .format("%x.%x", getConfigMetaData().getVersion() >> 16,
                getConfigMetaData().getVersion() & 0xffff));
      }
      if (getConfigMetaData().getUtilVersion().isOnlyReadValidData()) {
        getController().getSystemData();
        if (!getConfigData().isInitialized()) {
          getConfigData().initArrays();
        } else if (!getConfigData().checkArrays()) {
          getConfigData().initArrays();
        }
        getActiveExt();
        getActiveTx();
        getActiveRx();
        getActiveUser();
        getActiveUserGroup();
        getActiveMultiScreen();
        getActiveFunctionKey();
        getActiveMatrix();
        getActiveTxRxGroup();
        if (getConfigMetaData().getUtilVersion().hasBranchData()) {
          reloadBranchData();
        }
        if (getConfigMetaData().getUtilVersion().hasMultiviewData()) {
          reloadMultiviewData();
        }
        if (getConfigMetaData().getUtilVersion().hasSourceCropData()) {
          reloadSourceCropData();
        }
      } else {
        getController().getConfigData();
      }
      getController().getGridInfo(Utilities.getMatrixOffset(this));
      this.lastReloadConfigTimestamp = System.currentTimeMillis();
    } catch (DeviceConnectionException dce) {
      throw new ConfigException(CfgError.CONNECTION_ERROR, dce);
    } finally {
      this.lock.unlock();
    }
  }

  /**
   * 获取有效tx.
   */
  public void getActiveTx() throws ConfigException, BusyException, DeviceConnectionException {
    Map<Integer, Boolean> activeTxMap = getController().getActiveTx();
    List<CpuData> requiredTx = new ArrayList<>();
    for (Map.Entry<Integer, Boolean> entry : activeTxMap.entrySet()) {
      if (entry.getValue()) {
        requiredTx.add(getConfigData().getCpuData(entry.getKey()));
      } else {
        getConfigData().getCpuData(entry.getKey()).initDefaults();
      }
    }
    getController().getCpuData(requiredTx);
  }

  /**
   * 获取有效rx.
   */
  public void getActiveRx() throws ConfigException, BusyException, DeviceConnectionException {
    Map<Integer, Boolean> activeRxMap = getController().getActiveRx();
    List<ConsoleData> requiredRx = new ArrayList<>();
    for (Map.Entry<Integer, Boolean> entry : activeRxMap.entrySet()) {
      if (entry.getValue()) {
        requiredRx.add(getConfigData().getConsoleData(entry.getKey()));
      } else {
        getConfigData().getConsoleData(entry.getKey()).initDefaults();
      }
    }
    getController().getConsoleData(requiredRx);
    getConfigData().readVpcon();
  }

  private void getActiveExt() throws ConfigException, BusyException, DeviceConnectionException {
    Map<Integer, Boolean> activeExtMap = getController().getActiveExt();
    List<ExtenderData> requiredExt = new ArrayList<>();
    for (Map.Entry<Integer, Boolean> entry : activeExtMap.entrySet()) {
      if (entry.getValue()) {
        requiredExt.add(getConfigData().getExtenderData(entry.getKey()));
      } else {
        getConfigData().getExtenderData(entry.getKey()).initDefaults();
      }
    }
    getController().getExtenderData(requiredExt);
  }

  private void getActiveUser() throws ConfigException, BusyException, DeviceConnectionException {
    Map<Integer, Boolean> activeUserMap = getController().getActiveUser();
    List<UserData> requiredUser = new ArrayList<>();
    for (Map.Entry<Integer, Boolean> entry : activeUserMap.entrySet()) {
      if (entry.getValue()) {
        requiredUser.add(getConfigData().getUserData(entry.getKey()));
      } else {
        getConfigData().getUserData(entry.getKey()).initDefaults();
      }
    }
    getController().getUserData(requiredUser);
  }

  private void getActiveUserGroup()
      throws ConfigException, BusyException, DeviceConnectionException {
    Map<Integer, Boolean> activeUserGroupMap = getController().getActiveUserGroup();
    List<UserGroupData> requiredUserGroup = new ArrayList<>();
    for (Map.Entry<Integer, Boolean> entry : activeUserGroupMap.entrySet()) {
      if (entry.getValue()) {
        requiredUserGroup.add(getConfigData().getUserGroupData(entry.getKey()));
      } else {
        getConfigData().getUserGroupData(entry.getKey()).initDefaults();
      }
    }
    getController().getUserGroupData(requiredUserGroup);
  }

  private void getActiveMultiScreen()
      throws ConfigException, BusyException, DeviceConnectionException {
    Map<Integer, Boolean> activeMultiScreenMap = getController().getActiveMultiscreen();
    List<MultiScreenData> requiredMultiScreen = new ArrayList<>();
    for (Map.Entry<Integer, Boolean> entry : activeMultiScreenMap.entrySet()) {
      if (entry.getValue()) {
        requiredMultiScreen.add(getConfigData().getMultiScreenData(entry.getKey()));
      } else {
        getConfigData().getMultiScreenData(entry.getKey()).initDefaults();
      }
    }
    getController().getMultiScreenData(requiredMultiScreen);
  }

  private void getActiveFunctionKey()
      throws ConfigException, BusyException, DeviceConnectionException {
    Map<Integer, Boolean> activeMacroMap = getController().getActiveMacro();
    List<FunctionKeyData> requiredMacro = new ArrayList<>();
    for (Map.Entry<Integer, Boolean> entry : activeMacroMap.entrySet()) {
      if (entry.getValue()) {
        requiredMacro.add(getConfigData().getFunctionKeyData(entry.getKey()));
      } else {
        getConfigData().getFunctionKeyData(entry.getKey()).initDefaults();
      }
    }
    getController().getFunctionKeyData(requiredMacro);
  }

  private void getActiveMatrix() throws ConfigException, BusyException, DeviceConnectionException {
    Map<Integer, Boolean> activeGridMap = getController().getActiveGrid();
    for (Map.Entry<Integer, Boolean> entry : activeGridMap.entrySet()) {
      if (entry.getValue()) {
        getController().getGridInfoByIndex(entry.getKey());
      } else {
        getConfigData().getMatrixData(entry.getKey()).initDefaults();
      }
    }
  }

  private void getActiveTxRxGroup() throws ConfigException, BusyException, DeviceConnectionException {
    Map<Integer, Boolean> activeCpuGroupMap = getController().getActiveTxRxGroup();
    List<TxRxGroupData> requiredCpuGroup = new ArrayList<>();
    for (Map.Entry<Integer, Boolean> entry : activeCpuGroupMap.entrySet()) {
      if (entry.getValue()) {
        requiredCpuGroup.add(getConfigData().getTxRxGroupData(entry.getKey()));
      } else {
        getConfigData().getTxRxGroupData(entry.getKey()).initDefaults();
      }
    }
    getController().getTxRxGroupData(requiredCpuGroup);
  }

  private void reloadBranchData()
      throws ConfigException, BusyException, DeviceConnectionException {
    getController().getBranchData(getConfigData().getBranchDatas());
  }

  /**
   * 从设备加载multiview数据.
   */
  public void reloadMultiviewData()
      throws ConfigException, BusyException, DeviceConnectionException {
    getController().getMultiviewData(getConfigData().getMultiviewDatas());
  }

  private void reloadSourceCropData()
      throws ConfigException, BusyException, DeviceConnectionException {
    getController().getSourceCropData(getConfigData().getSourceCropDatas());
  }

  /**
   * 从设备加载extender数据.
   */
  public void reloadExtenderData() throws ConfigException, BusyException {
    getConfigData().isChanged(AbstractData.THRESHOLD_UI_LOCAL_CHANGES);

    reloadExtenderData(getConfigData().getExtenderDatas());
  }

  /**
   * 从设备加载extender数据.
   */
  public void reloadExtenderData(Iterable<ExtenderData> requestedDatas)
      throws ConfigException, BusyException {
    this.lock.lock();
    try {
      if (this.lastReloadConfigTimestamp + RELOAD_DELAY > System.currentTimeMillis()) {
        return;
      }
      getController().getExtenderData(requestedDatas);
      for (ExtenderData extenderData : getConfigData().getExtenderDatas()) {
        if (extenderData.isChanged()) {
          extenderData.commit();
        }
      }
    } catch (DeviceConnectionException dce) {
      throw new ConfigException(CfgError.CONNECTION_ERROR, dce);
    } finally {
      this.lock.unlock();
    }
  }

  /**
   * .
   *
   * @brief 从设备加载cpu数据
   */
  public void reloadCpuData() throws ConfigException, BusyException {
    reloadCpuData(getConfigData().getCpuDatas());
  }

  /**
   * .
   *
   * @brief 从设备加载cpu数据
   */
  public void reloadCpuData(Iterable<CpuData> requestedDatas)
      throws ConfigException, BusyException {
    this.lock.lock();
    try {
      if (this.lastReloadConfigTimestamp + RELOAD_DELAY > System.currentTimeMillis()) {
        return;
      }
      getController().getCpuData(requestedDatas);
      for (CpuData cpuData : getConfigData().getCpuDatas()) {
        if (cpuData.isChanged()) {
          cpuData.commit();
        }
      }
    } catch (DeviceConnectionException dce) {
      throw new ConfigException(CfgError.CONNECTION_ERROR, dce);
    } finally {
      this.lock.unlock();
    }
  }

  /**
   * .
   *
   * @brief 从设备加载console数据
   */
  public void reloadConsoleData() throws ConfigException, BusyException {
    reloadConsoleData(getConfigData().getConsoleDatas());
    getConfigData().readVpcon();
  }

  /**
   * .
   */
  public void reloadConsoleData(Iterable<ConsoleData> requestedDatas)
      throws ConfigException, BusyException {
    this.lock.lock();
    try {
      if (this.lastReloadConfigTimestamp + RELOAD_DELAY > System.currentTimeMillis()) {
        return;
      }
      getController().getConsoleData(requestedDatas);
      for (ConsoleData consoleData : getConfigData().getConsoleDatas()) {
        if (consoleData.isChanged()) {
          consoleData.commit();
        }
      }
    } catch (DeviceConnectionException dce) {
      throw new ConfigException(CfgError.CONNECTION_ERROR, dce);
    } finally {
      this.lock.unlock();
    }
  }

  /**
   * .
   *
   * @brief 从设备加载con的macro数据
   */
  public void reloadFunctionKeyData() throws ConfigException, BusyException {
    reloadFunctionKeyData(getConfigData().getFunctionKeyDatas());
  }

  /**
   * .
   */
  public void reloadFunctionKeyData(Iterable<FunctionKeyData> requestedDatas)
      throws ConfigException, BusyException {
    this.lock.lock();
    try {
      if (this.lastReloadConfigTimestamp + RELOAD_DELAY > System.currentTimeMillis()) {
        return;
      }
      getController().getFunctionKeyData(requestedDatas);
      for (FunctionKeyData fkd : getConfigData().getFunctionKeyDatas()) {
        if (fkd.isChanged()) {
          fkd.commit();
        }
      }
    } catch (DeviceConnectionException dce) {
      throw new ConfigException(CfgError.CONNECTION_ERROR, dce);
    } finally {
      this.lock.unlock();
    }
  }

  /**
   * .
   */
  public void reloadUserData() throws ConfigException, BusyException {
    this.lock.lock();
    try {
      getController().getUserData(getConfigData().getUserDatas());
      for (UserData userData : getConfigData().getUserDatas()) {
        if (userData.isChanged()) {
          userData.commit();
        }
      }
    } catch (DeviceConnectionException dce) {
      throw new ConfigException(CfgError.CONNECTION_ERROR, dce);
    } finally {
      this.lock.unlock();
    }
  }

  @Override
  public Lock getLock() {
    return this.lock;
  }

  @Override
  public void setConnection(String hostname) throws ConfigException, BusyException {
    setConnection(hostname, true);
  }

  @Override
  public void setConnection(String hostname, boolean enablePing)
      throws ConfigException, BusyException {
    this.lock.lock();
    try {
      getController().setConnection(this.uniqueId, hostname + ":" + CaesarConstants.CFG_PORT);
      getController().ensureConnection(this.uniqueId);
      getController().addConnectionListener(
          this.connectionListener = () -> LOG.log(Level.INFO, hostname + "connected!"));

      if (connectivityChecker != null) {
        connectivityChecker.setStop();
        connectivityChecker = null;
      }
      if (enablePing) {
        connectivityChecker = new ConnectivityChecker();
        Thread th = new Thread(connectivityChecker);
        th.setDaemon(true);
        th.start();
      }
      getDbDataModel().setConnection(hostname);
    } catch (DeviceConnectionException dce) {
      throw new ConfigException(CfgError.CONNECTION_ERROR, dce);
    } finally {
      this.lock.unlock();
    }
  }

  @Override
  public void closeConnection() {
    this.lock.lock();
    try {
      if (isIoCapable()) {
        try {
          getController().removeConnectionListener(this.connectionListener);
        } catch (DeviceConnectionException exception) {
          LOG.log(Level.WARNING, "Remove connection listener error!", exception);
        }
      }
      getController().setConnection(this.uniqueId, null);
      if (connectivityChecker != null) {
        connectivityChecker.setStop();
        connectivityChecker = null;
      }
    } finally {
      this.lock.unlock();
    }
  }

  @Override
  public void closeExternalConnection() {
    String ip = Utilities.getIpFromModel(this);
    for (MatrixData matrixData : getConfigData().getMatrixDatas()) {
      if (matrixData.isStatusActive()) {
        CaesarSwitchDataModel model = CaesarSwitchDataModelManager.getInstance()
            .unbind(ip, matrixData.getHostAddressString());
        if (model != null) {
          model.closeConnection();
        }
      }
    }
  }

  /**
   * .
   *
   * @return 如果能，返回true
   * @brief 判断能否收发设备数据
   */
  @Override
  public boolean isIoCapable() {
    return this.controller.isIoCapable();
  }

  public CaesarController getController() {
    return this.controller;
  }

  @Override
  public void activateConfig(int configNr) throws ConfigException, BusyException {
    this.lock.lock();
    try {
      getController().activateConfig(configNr + 1);
    } catch (DeviceConnectionException dce) {
      LOG.log(Level.WARNING, "Fail to activate config for device connection error!", dce);
    } finally {
      this.lock.unlock();
    }
  }

  /**
   * .
   *
   * @brief 保存online的配置，当发送配置给设备都，都要save一下的
   */
  @Override
  public void save() throws ConfigException, BusyException {
    this.lock.lock();
    try {
      getController().setSave();
      this.saveRequired = false;
    } catch (DeviceConnectionException dce) {
      LOG.log(Level.INFO, "Connection Timout", dce);
    } catch (ConfigException ex) {
      throw new ConfigException(CfgError.CONNECTION_ERROR, ex);
    } finally {
      this.lock.unlock();
    }
  }

  /**
   * .
   *
   * @param value 如果是master设备而且是active，就传1，否则传0
   * @brief 关闭设备
   */
  @Override
  public void shutdown(int value) throws ConfigException, BusyException {
    this.lock.lock();
    try {
      getController().setShutdown(value);
      this.saveRequired = false;
    } catch (DeviceConnectionException dce) {
      LOG.log(Level.INFO, "Connection Timout", dce);
    } catch (ConfigException ex) {
      throw new ConfigException(CfgError.CONNECTION_ERROR, ex);
    } finally {
      this.lock.unlock();
    }
  }

  /**
   * .
   *
   * @param slot 如果为0，就是把设备重置为出厂设置 如果是moduleData的oid，就是把相应的io板重置为出厂设置
   * @brief 出厂设置
   */
  @Override
  public void factoryReset(int slot) throws ConfigException, BusyException {
    this.lock.lock();
    try {
      getController().setFactoryReset(slot);
    } catch (DeviceConnectionException dce) {
      LOG.log(Level.INFO, "Connection Timout", dce);
    } catch (ConfigException ex) {
      throw new ConfigException(CfgError.CONNECTION_ERROR, ex);
    } finally {
      this.lock.unlock();
    }
  }

  @Override
  public void restart(int module) throws ConfigException, BusyException {
    this.lock.lock();
    try {
      getController().setRestart(module);
      this.saveRequired = false;
    } catch (DeviceConnectionException dce) {
      LOG.log(Level.WARNING, "Connection Timout", dce);
    } catch (ConfigException ex) {
      throw new ConfigException(CfgError.CONNECTION_ERROR, ex);
    } finally {
      this.lock.unlock();
    }
  }

  /**
   * 重启IO卡.
   */
  public void restartAll(int value) throws ConfigException, BusyException {
    int p1 = getConfigMetaData().getUtilVersion().getRestartAllSlot();
    restart(p1);
  }

  /**
   * 重置.
   */
  public void restartCpuBoard() throws ConfigException, BusyException {
    restart(0);
  }

  public void restartIoBoard(int module) throws ConfigException, BusyException {
    restart(module);
  }

  @Override
  public void reset(byte level1, byte level2, byte level3) throws ConfigException, BusyException {
    this.lock.lock();
    try {
      getExternalController(level1).setReset(level1, level2, level3);
      this.saveRequired = false;
    } catch (DeviceConnectionException dce) {
      throw new ConfigException(CfgError.RESTART_ERROR);
    } catch (ConfigException ex) {
      throw new ConfigException(CfgError.CONNECTION_ERROR, ex);
    } catch (RuntimeException exception) {
      throw new ConfigException(CfgError.NOT_FOUND, exception);
    } finally {
      this.lock.unlock();
    }
  }

  /**
   * .
   *
   * @param id          一般为255 @unknonw
   * @param serviceMode 是否进入serviceMode
   * @brief 设置为servicemode
   */
  @Override
  public void setServiceMode(int id, boolean serviceMode) throws ConfigException, BusyException {
    this.lock.lock();
    try {
      getController().setServiceMode(id, serviceMode);
    } catch (DeviceConnectionException dce) {
      throw new ConfigException(CfgError.CONNECTION_ERROR, dce);
    } catch (ConfigException ex) {
      throw new ConfigException(CfgError.CONNECTION_ERROR, ex);
    } finally {
      this.lock.unlock();
    }
  }

  @Override
  public void setServiceMode(int moduleId, int portId, boolean serviceMode)
      throws ConfigException, BusyException {
    this.lock.lock();
    try {
      getExternalController(moduleId).setServiceMode(moduleId, portId, serviceMode);
    } catch (DeviceConnectionException dce) {
      throw new ConfigException(CfgError.CONNECTION_ERROR, dce);
    } catch (ConfigException ex) {
      throw new ConfigException(CfgError.CONNECTION_ERROR, ex);
    } catch (RuntimeException ex) {
      throw new ConfigException(CfgError.NOT_FOUND, ex);
    } finally {
      this.lock.unlock();
    }
  }

  /**
   * .
   *
   * @brief 从设备读取cpu 与 console的配置
   */
  public void reloadCpuConsoleMatrix() throws ConfigException, BusyException {
    beginUpdate();
    this.lock.lock();
    try {
      getController().getCpuConsoleMatrix();
      // 重新获取multiview数据
      reloadMultiviewData();
      for (ConsoleData consoleData : getConfigData().getConsoleDatas()) {
        if (consoleData.isChanged()) {
          consoleData.commit();
        }
      }
      for (CpuData cpuData : getConfigData().getCpuDatas()) {
        if (cpuData.isChanged()) {
          cpuData.commit();
        }
      }
    } catch (DeviceConnectionException dce) {
      throw new ConfigException(CfgError.CONNECTION_ERROR, dce);
    } finally {
      this.lock.unlock();
      endUpdate();
    }
  }

  /**
   * .
   *
   * @param consoleData console 不能为空
   * @param cpuData     cpu 不能为空
   * @brief 设置consoleData与cpuData的video access连接
   */
  public void videoConnect(ConsoleData consoleData, CpuData cpuData)
      throws ConfigException, BusyException {
    this.lock.lock();
    try {
      getController().setCpuConnectToCon(consoleData, cpuData);
    } catch (DeviceConnectionException dce) {
      throw new ConfigException(CfgError.CONNECTION_ERROR, dce);
    } finally {
      this.lock.unlock();
    }
  }

  /**
   * .
   *
   * @param cpuData     cpu 可以为空，为空时就是删除console的连接
   * @param consoleData console 不可为空
   * @brief 设置console与cpu的full access 关系
   */
  public void sendCpuConsoleConnection(CpuData cpuData, ConsoleData consoleData)
      throws ConfigException, BusyException {
    this.lock.lock();
    try {
      getController().setCpuConsoleConnecton(cpuData, consoleData);
    } catch (DeviceConnectionException dce) {
      throw new ConfigException(CfgError.CONNECTION_ERROR, dce);
    } finally {
      this.lock.unlock();
    }
  }

  /**
   * switchMultiviewTx.
   */
  public void switchMultiviewTx(CpuData cpuData, ConsoleData conData, int channel, int mode)
      throws ConfigException, BusyException {
    this.lock.lock();
    try {
      getController().switchMultiviewTx(cpuData, conData, channel, mode, 0);
    } catch (DeviceConnectionException dce) {
      throw new ConfigException(CfgError.CONNECTION_ERROR, dce);
    } finally {
      this.lock.unlock();
    }
  }

  /**
   * 切换多画面的视频连接，TX的排列必须是4K60信号在前.
   */
  public void switchMultiviewAllVideo(ConsoleData conData, List<SwitchMultiviewAllVideoChannel> channels)
      throws ConfigException, BusyException {
    this.lock.lock();
    try {
      getController().switchMultiviewAllVideo(conData, channels);
    } catch (DeviceConnectionException dce) {
      LOG.log(Level.WARNING, "Switch Multiview All Video failed", dce);
    } finally {
      this.lock.unlock();
    }
  }

  /**
   * sendCpuConsolePrivate.
   *
   * @brief 设置console与cpu的private access关系.
   */
  public void sendCpuConsolePrivate(CpuData cpuData, ConsoleData consoleData)
      throws ConfigException, BusyException {
    this.lock.lock();
    try {
      getController().setCpuConsoleConnectionByMode(cpuData, consoleData,
          CaesarControllerConstants.BIN_CPUCON_PRIVATE);
    } catch (DeviceConnectionException dce) {
      throw new ConfigException(CfgError.CONNECTION_ERROR, dce);
    } finally {
      this.lock.unlock();
    }
  }

  /**
   * sendCpuConsoleBlock.
   *
   * @brief 发送当前的cpu与console的关联关系到设备, full access.
   */
  public void sendCpuConsoleBlock() throws ConfigException, BusyException {
    this.lock.lock();
    try {
      Collection<ConsoleData> consoleDatas =
          getConfigData().getConfigDataManager().getActiveConsoles();
      Map<ConsoleData, CpuData> map = new HashMap<>();
      for (ConsoleData consoleData : consoleDatas) {
        CpuData cpuData = consoleData.getCpuData();
        if (cpuData != null && consoleData.isStatusVideoOnly()) {
          getController().setCpuConsoleConnectionByMode(cpuData, consoleData,
              CaesarControllerConstants.BIN_CPUCON_VIDEO);
        } else if (cpuData != null && cpuData.isStatusPrivate() && consoleData.isStatusPrivate()) {
          getController().setCpuConsoleConnectionByMode(cpuData, consoleData,
              CaesarControllerConstants.BIN_CPUCON_PRIVATE);
        } else {
          map.put(consoleData, cpuData);
        }
      }
      getController().setCpuConsoleConnectionBlock(map);
    } catch (DeviceConnectionException dce) {
      throw new ConfigException(CfgError.CONNECTION_ERROR, dce);
    } finally {
      this.lock.unlock();
    }
  }

  /**
   * 设置cpu与console的full access与video access.
   */
  public void sendCpuConsoleBlock(Map<ConsoleData, CpuData> fullAccess,
                                  Map<ConsoleData, CpuData> videoAccess)
      throws ConfigException, BusyException {
    sendCpuConsoleBlock(fullAccess, videoAccess, Collections.emptyMap());
  }

  /**
   * 设置cpu与console的full access与video access还有private access.
   */
  public void sendCpuConsoleBlock(Map<ConsoleData, CpuData> fullAccess,
                                  Map<ConsoleData, CpuData> videoAccess,
                                  Map<ConsoleData, CpuData> privateMode)
      throws ConfigException, BusyException {
    this.lock.lock();
    try {
      getController().setCpuConsoleConnectionBlock(fullAccess);
      for (Map.Entry<ConsoleData, CpuData> entry : videoAccess.entrySet()) {
        try {
          getController().setCpuConsoleConnectionByMode(entry.getValue(), entry.getKey(),
              CaesarControllerConstants.BIN_CPUCON_VIDEO);
        } catch (DeviceConnectionException dce) {
          LOG.log(Level.WARNING, "VideoMode Command failed");
        }

      }
      for (Map.Entry<ConsoleData, CpuData> entry : privateMode.entrySet()) {
        try {
          getController().setCpuConsoleConnectionByMode(entry.getValue(), entry.getKey(),
              CaesarControllerConstants.BIN_CPUCON_PRIVATE);
        } catch (DeviceConnectionException dce) {
          LOG.log(Level.WARNING, "PrivateMode Command failed");
        }

      }
    } catch (DeviceConnectionException dce) {
      throw new ConfigException(CfgError.CONNECTION_ERROR, dce);
    } finally {
      this.lock.unlock();
    }
  }

  /**
   * 断开所有端口的连接.
   */
  public void sendDisconnectPorts() throws ConfigException, BusyException {
    this.lock.lock();
    try {
      Collection<ConsoleData> consoleDatas =
          getConfigData().getConfigDataManager().getActiveConsoles();
      Collection<ConsoleData> virtualConsoleDatas = getConfigData().getConfigDataManager()
          .getConsoles(CaesarConstants.Console.Status.VIRTUAL_DEVICE);

      consoleDatas.removeAll(virtualConsoleDatas);

      Map<ConsoleData, CpuData> map = new HashMap<>();
      for (ConsoleData consoleData : consoleDatas) {
        map.put(consoleData, null);
      }
      getController().setCpuConsoleConnectionBlock(map);
    } catch (DeviceConnectionException ex) {
      throw new ConfigException(CfgError.CONNECTION_ERROR, ex);
    } finally {
      this.lock.unlock();
    }
  }

  /**
   * 登陆验证.
   *
   * @param username 用户名
   * @param password 用户密码
   */
  public LoginResponse login(byte[] username, byte[] password)
      throws ConfigException, BusyException {
    String userNameStr = new String(username, StandardCharsets.UTF_8).trim();
    if (User.ADMINISTRATOR_NAME.equals(userNameStr)) {
      String passwordStr = new String(password, StandardCharsets.UTF_8).trim();
      String encrypted = DkmProtocalCrypto.sha1(passwordStr);
      if (User.ADMINISTRATOR_PW.equals(encrypted)) {
        return LoginResponse.SUCCESS;
      }
    }
    this.lock.lock();
    try {
      return getController().login(username, password);
    } catch (DeviceConnectionException | ConfigException dce) {
      LOG.log(Level.SEVERE, "username:" + userNameStr, dce);
      throw new ConfigException(CfgError.CONNECTION_ERROR, dce);
    } finally {
      this.lock.unlock();
    }

  }


  /**
   * CPU的虚拟拔出插入功能.
   */
  public void setCpuVirtualOutIn(CpuData data, boolean in) throws BusyException, ConfigException {
    ExtenderData extenderData = data.getExtenderData(0);
    if (extenderData == null) {
      LOG.warning("ExtenderData is null!");
      return;
    }
    int level1 = Utilities.getLevel1(this, extenderData);
    int level2 = Utilities.getLevel2(this, extenderData);

    CaesarController controller = getExternalController(level1);
    if (controller == null) {
      LOG.warning("Fail to find controller for module " + level1);
      return;
    }

    try {
      if (in) {
        controller.setCpuVirtualIn(level1, level2);
      } else {
        controller.setCpuVirtualOut(level1, level2);
      }
    } catch (DeviceConnectionException | ConfigException dce) {
      LOG.log(Level.SEVERE, "" + dce);
      throw new ConfigException(CfgError.CONNECTION_ERROR, dce);
    }
  }

  /**
   * 获得串口.
   *
   * @param level1 slot号，如果为0，表示获取CPU板或者矩阵的序列号，其他表示获取io板的序列号
   * @param level2 如果获取CPU板序列号为0，获取矩阵的序列号为1
   * @throws BusyException level1 level2 level2为缓存的key值 串口缓存为空,返回-1;串口缓存不为空返回一个int类型
   */
  public String getSerial(int level1, byte level2) throws ConfigException, BusyException {
    this.lock.lock();
    try {
      String key = level1 + "_" + level2;
      byte[] serial = this.serialCache.get(key);
      if (!isDemo()) {
        serial = getExternalController(level1).getSerial((byte) level1, level2);
        this.serialCache.put(key, serial);
      }
      String temp = serial == null ? "" : new String(serial, StandardCharsets.UTF_8);
      serial = temp.trim().getBytes(StandardCharsets.UTF_8);
      return DatatypeConverter.printHexBinary(serial);
    } catch (DeviceConnectionException | ConfigException dce) {
      LOG.log(Level.SEVERE, "Level: " + level1 + "_" + level2, dce);
      throw new ConfigException(CfgError.CONNECTION_ERROR, dce);
    } catch (RuntimeException exception) {
      LOG.log(Level.SEVERE, "Level: " + level1 + "_" + level2, exception);
      throw new ConfigException(CfgError.NOT_FOUND, exception);
    } finally {
      this.lock.unlock();
    }
  }

  /**
   * 获取matrix或者extender的产品名称.
   *
   * @param level1 level1
   * @param level2 level2
   * @note level3 必须为1
   */
  public String getExtName(byte level1, byte level2) throws ConfigException, BusyException {
    this.lock.lock();
    try {
      return getExternalController(level1).getExtName(level1, level2);
    } catch (DeviceConnectionException dce) {
      throw new ConfigException(CfgError.CONNECTION_ERROR, dce);
    } catch (ConfigException ce) {
      LOG.log(Level.SEVERE, "Level: " + level1 + "_" + level2, ce);
      throw new ConfigException(CfgError.CONNECTION_ERROR, ce);
    } catch (RuntimeException ce) {
      LOG.log(Level.SEVERE, "Level: " + level1 + "_" + level2, ce);
      throw new ConfigException(CfgError.NOT_FOUND, ce);
    } finally {
      this.lock.unlock();
    }
  }

  /**
   * 导入外设信息.
   */
  public void sendExtData(Collection<ExtenderInfo> infos)
      throws DeviceConnectionException, BusyException {
    lock.lock();
    try {
      for (ExtenderInfo info : infos) {
        // 导入外设且不受某个信息导入失败的影响
        try {
          getController().sendExtImport(info);
        } catch (ConfigException ex) {
          LOG.log(Level.WARNING, null, ex);
        }
      }
    } finally {
      lock.unlock();
    }
  }

  /**
   * USB透传设备绑定/接绑定.
   */
  public void setUsbBinding(boolean isBind, PortData portData, ExtenderData ext, DataObject data)
      throws ConfigException {
    lock.lock();
    try {
      getController().setUsbBinding(isBind, portData, ext, data);
    } catch (ConfigException | BusyException ce) {
      LOG.log(Level.SEVERE, "", ce);
      throw new ConfigException(CfgError.CONNECTION_ERROR, ce);
    } catch (DeviceConnectionException dce) {
      throw new ConfigException(CfgError.CONNECTION_ERROR, dce);
    } finally {
      lock.unlock();
    }
  }

  /**
   * 双主控对端重启.
   */
  public void dualMatrixReboot() throws ConfigException, BusyException {
    this.lock.lock();
    try {
      getController().dualMatrixReboot();
      this.saveRequired = false;
    } catch (DeviceConnectionException dce) {
      LOG.log(Level.WARNING, "Connection Timout", dce);
    } catch (ConfigException ex) {
      throw new ConfigException(CfgError.CONNECTION_ERROR, ex);
    } finally {
      this.lock.unlock();
    }
  }

  /**
   * 检查连接 当时间为83秒内检查连接成功,大于83秒检查连接失败.
   */
  public void checkConnectivity() {
    boolean connected = false;
    try {
      getTime();
      connected = true;
    } catch (ConfigException ex) {
      connected = false;
    } catch (BusyException ex) {
      connected = false;
    }
    final boolean finalConnected = connected;
    PlatformUtility.runInFxThreadLater(() -> setConnected(finalConnected));
  }

  private void setConnected(boolean connected) {
    PlatformUtility.runInFxThreadLater(() -> {
      boolean oldValue = this.connected.get();
      this.connected.set(connected);
      getChangeSupport().firePropertyChange(SwitchDataModel.PROPERTY_CONNECTED, oldValue,
          connected);
    });
  }

  /**
   * 判断是否连接 连接成功为true 失败为false.
   */
  @Override
  public boolean isConnected() {
    return this.connected.get();
  }

  public BooleanProperty connectedProperty() {
    return this.connected;
  }

  /**
   * 管理员用户 管理员用户为true 不存在为false.
   */
  @Override
  public boolean isAdminUser(String userName) throws ConfigException, BusyException {
    boolean isAdmin = false;

    reloadConfigData();
    for (UserData userData : getConfigDataManager().getActiveUsers()) {
      if (userName.equals(userData.getName())) {
        isAdmin = userData.hasRightAdmin();
        break;
      }
    }
    return isAdmin;
  }

  /**
   * 判断用户是否有存在 用户存在为true 不存在为false.
   */
  @Override
  public boolean userExists(String userName) throws ConfigException, BusyException {
    boolean userExists = false;

    reloadConfigData();
    for (UserData userData : getConfigDataManager().getActiveUsers()) {
      if (userName.equals(userData.getName())) {
        userExists = true;
        break;
      }
    }
    return userExists;
  }

  /**
   * 检查配置版本 urlName:带用户与密码的url 如********************************/，开始必须是@的下一个开始,最后必须带斜杠
   * ConfigDataModel:接口,里面有添加属性监听器和删除属性监听器等等 如果fileConfigVersion <= matrixConfigVersion
   * 就行行检查配置版本,否则false不进行检查
   */
  @Override
  public boolean checkConfigVersion(String urlName, ConfigDataModel cdm)
      throws ConfigException, BusyException {
    int startIndex = urlName.indexOf('@') + 1;
    int endIndex = urlName.lastIndexOf('/');
    System.out.println(endIndex);
    if (-1 != startIndex && -1 != endIndex && cdm instanceof CaesarSwitchDataModel) {
      int fileConfigVersion = getConfigMetaData().getVersion();
      setConnection(urlName.substring(startIndex, endIndex));
      int matrixConfigVersion = getConfigVersion();

      return fileConfigVersion <= matrixConfigVersion;
    }
    return false;
  }

  /**
   * .
   *
   * @brief 返回回滚的threshold
   */
  @Override
  public Threshold getUiThreshold() {
    return Threshold.UI_ALL;
  }

  /**
   * 本地常量 THRESHOLD_UI_LOCAL_CHANGES的常量有1到10和最大最小值.
   */
  @Override
  public boolean hasLocalChanges() {
    return isChanged(AbstractData.THRESHOLD_UI_LOCAL_CHANGES);
  }


  /**
   * 保存需求 执行保存,true为保存 false 保存失败.
   */
  @Override
  public boolean requiresSave() {
    return this.saveRequired;
  }

  private final class ConnectivityChecker implements Runnable {

    private boolean stop = false;

    private ConnectivityChecker() {
    }

    public void setStop() {
      stop = true;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    @Override
    public void run() {
      boolean useGetTime = false;
      try {
        SystemTimeData systemTimeData = CaesarSwitchDataModel.this.getTime();
        if (systemTimeData.getStatus() == 1) {
          getChangeSupport().firePropertyChange(PROPERTY_RECERTIFICATION, 0, 1);
        }
        useGetTime = true;
        CaesarSwitchDataModel.this.setConnected(true);
      } catch (ConfigException ex) {
        LOG.log(Level.INFO, "ConnectivityChecker  ConfigException");
        return;
      } catch (BusyException ex) {
        LOG.log(Level.INFO, "ConnectivityChecker - init -- matrix system is busy");
      }
      try {
        Thread.sleep(10000);
      } catch (InterruptedException ex) {
        LOG.log(Level.WARNING, "Sleep is interrupted!", ex);
      }
      while (CaesarSwitchDataModel.this.getController().isIoCapable() && !stop) {
        CaesarSwitchDataModel.this.lock.lock();
        try {
          if (CaesarSwitchDataModel.this.getController().isIoCapable()) {
            try {
              if (useGetTime) {
                SystemTimeData timeData = CaesarSwitchDataModel.this.getController().getTime();
                if (timeData.getStatus() == 1) {
                  getChangeSupport().firePropertyChange(PROPERTY_RECERTIFICATION, 0, 1);
                }
              } else {
                CaesarSwitchDataModel.this.getController().getConfigData();
              }
            } catch (BusyException ex) {
              LOG.log(Level.INFO, "ConnectivityChecker -- matrix system is busy");
            }
            CaesarSwitchDataModel.this.setConnected(true);
            continue;
          }
          CaesarSwitchDataModel.this.setConnected(false);
          continue;
        } catch (DeviceConnectionException ex) {
          CaesarSwitchDataModel.this.setConnected(false);
          continue;
        } catch (ConfigException ex) {
          CaesarSwitchDataModel.this.setConnected(false);
          continue;
        } finally {
          CaesarSwitchDataModel.this.lock.unlock();
          try {
            Thread.sleep(2000);
            continue;
          } catch (InterruptedException ex) {
            LOG.log(Level.WARNING, "Sleep is interrupted!", ex);
          }
          continue;
        }
      }
      LOG.log(Level.INFO, "Finished ConnectivityChecker");
    }
  }

  /**
   * 获取io的端口数.
   *
   * @return io的端口数.
   */
  public int getPortsPerIo() {
    int result = CaesarConstants.Module.DEFAULT_PORTS;
    for (ModuleData moduleData : getSwitchModuleData().getModuleDatas()) {
      if (!moduleData.isStatusActive()) {
        continue;
      }
      CaesarCpuTypeProperty property = CaesarCpuTypeProperty.getProperty(moduleData.getType());
      if (property == null) {
        continue;
      }
      result = property.getPortsPerIo();
    }
    return result;
  }

  /**
   * 获取最大的端口数目.
   *
   * @return 端口数目
   */
  public int getMaxPortCount() {
    ModuleData cpuModule = getSwitchModuleData().getModuleData(0);
    if (cpuModule != null) {
      return cpuModule.getPorts();
    } else {
      return 0;
    }
  }

  /**
   * io是否为虚拟的.
   *
   * @return 如果是虚拟的返回true.
   */
  public boolean isIoVirtual() {
    boolean result = true;
    for (ModuleData moduleData : getSwitchModuleData().getModuleDatas()) {
      if (!moduleData.isStatusActive()) {
        continue;
      }

      CaesarCpuTypeProperty property = CaesarCpuTypeProperty.getProperty(moduleData.getType());
      if (property == null) {
        continue;
      }

      result = property.isIoVirtual();
    }
    return result;
  }

  protected CaesarController getExternalController(int module)
      throws ConfigException, BusyException {
    if (module == 0) {
      return getController();
    }
    if (getConfigData().getSystemConfigData().isMatrixGridEnabled()) {
      for (MatrixData matrixData : getConfigData().getMatrixDatas()) {
        if (matrixData.getFirstModule() <= module && matrixData.getLastModule() >= module) {
          CaesarSwitchDataModel model =
              Utilities.getExternalModel(this, matrixData.getHostAddressString());
          return model.getController();
        }
      }
      LOG.warning("Fail to find controller for module " + module);
      return null;
    } else {
      return getController();
    }
  }

  /**
   * 获取光模信息.
   */
  public List<OpticalModuleInfo> getOpticalModulesInfo() {
    lock.lock();
    try {
      return getController().getOpticalModulesInfo();
    } catch (ConfigException | BusyException | DeviceConnectionException ex) {
      LOG.warning(ex.getCause().getMessage());
    } finally {
      lock.unlock();
    }
    return Collections.emptyList();
  }

  /**
   * 检查用户名是否符合规范.
   */
  public boolean checkUsernameValidity(String userName)
      throws BusyException, DeviceConnectionException, ConfigException {
    lock.lock();
    try {
      return getController().checkUsernameValidity(userName);
    } finally {
      lock.unlock();
    }
  }

  /**
   * 检查主控的硬件版本号与主机类型是否匹配，0.x匹配384主机，1.x匹配816主机.
   */
  public boolean isHwVersionIncompatible() {
    ModuleData moduleData = getSwitchModuleData().getModuleData(0);
    if (moduleData != null
        && CaesarConstants.Module.Type.PLUGIN_384.equals(
        CaesarConstants.Module.Type.valueOf(moduleData.getType()))
        && moduleData.getVersion().getHwVersionDef().getMasterVersion() != 0
        || moduleData != null
        && CaesarConstants.Module.Type.PLUGIN_816.equals(
        CaesarConstants.Module.Type.valueOf(moduleData.getType()))
        && moduleData.getVersion().getHwVersionDef().getMasterVersion() != 1) {
      return true;
    }
    return false;
  }
}

