package com.mc.tool.framework.utility;

import static org.junit.Assert.assertEquals;

import com.mc.common.collections.ObservableListWrapperEx;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import javafx.beans.property.IntegerProperty;
import javafx.beans.property.SimpleIntegerProperty;
import javafx.beans.value.ObservableIntegerValue;
import javafx.collections.FXCollections;
import javafx.collections.ListChangeListener;
import javafx.collections.ObservableList;
import org.junit.Assert;
import org.junit.Test;

/**
 * Testing the AggregatedObservableArrayList
 */
public class AggregatedObservableArrayListTest {


  @Test
  public void testObservableValue() {
    final AggregatedObservableArrayList<IntegerProperty> aggregatedWrapper =
        new AggregatedObservableArrayList<>("aggregatedWrapper");
    final ObservableList<IntegerProperty> aggregatedList = aggregatedWrapper.getAggregatedList();
    aggregatedList.addListener((ListChangeListener<IntegerProperty>) (change) -> {
      System.out.println("observable = " + change);
    });

    final ObservableList<IntegerProperty> list1 = FXCollections.observableArrayList();
    final ObservableList<IntegerProperty> list2 = FXCollections.observableArrayList();
    final ObservableList<IntegerProperty> list3 = FXCollections.observableArrayList();

    list1.addAll(new SimpleIntegerProperty(1), new SimpleIntegerProperty(2),
        new SimpleIntegerProperty(3), new SimpleIntegerProperty(4),
        new SimpleIntegerProperty(5));
    list2.addAll(new SimpleIntegerProperty(10), new SimpleIntegerProperty(11),
        new SimpleIntegerProperty(12), new SimpleIntegerProperty(13),
        new SimpleIntegerProperty(14), new SimpleIntegerProperty(15));
    list3.addAll(new SimpleIntegerProperty(100), new SimpleIntegerProperty(110),
        new SimpleIntegerProperty(120), new SimpleIntegerProperty(130),
        new SimpleIntegerProperty(140), new SimpleIntegerProperty(150));

    // adding list 1 to aggregate
    aggregatedWrapper.appendList(list1);
    assertEquals("wrong content", "[1,2,3,4,5]",
        aggregatedWrapper.dump(ObservableIntegerValue::get));

    // removing elems from list1
    list1.remove(2, 4);
    assertEquals("wrong content", "[1,2,5]", aggregatedWrapper.dump(ObservableIntegerValue::get));

    // adding second List
    aggregatedWrapper.appendList(list2);
    assertEquals("wrong content", "[1,2,5,10,11,12,13,14,15]",
        aggregatedWrapper.dump(ObservableIntegerValue::get));

    // removing elems from second List
    list2.remove(1, 3);
    assertEquals("wrong content", "[1,2,5,10,13,14,15]",
        aggregatedWrapper.dump(ObservableIntegerValue::get));

    // replacing element in first list
    list1.set(1, new SimpleIntegerProperty(3));
    assertEquals("wrong content", "[1,3,5,10,13,14,15]",
        aggregatedWrapper.dump(ObservableIntegerValue::get));

    // adding third List
    aggregatedWrapper.appendList(list3);
    assertEquals("wrong content", "[1,3,5,10,13,14,15,100,110,120,130,140,150]",
        aggregatedWrapper.dump(ObservableIntegerValue::get));

    // emptying second list
    list2.clear();
    assertEquals("wrong content", "[1,3,5,100,110,120,130,140,150]",
        aggregatedWrapper.dump(ObservableIntegerValue::get));

    // adding new elements to second list
    list2.addAll(new SimpleIntegerProperty(203), new SimpleIntegerProperty(202),
        new SimpleIntegerProperty(201));
    assertEquals("wrong content", "[1,3,5,203,202,201,100,110,120,130,140,150]",
        aggregatedWrapper.dump(ObservableIntegerValue::get));

    // sorting list2. this results in permutation
    list2.sort((o1, o2) -> o1.getValue().compareTo(o2.getValue()));
    assertEquals("wrong content", "[1,3,5,201,202,203,100,110,120,130,140,150]",
        aggregatedWrapper.dump(ObservableIntegerValue::get));

    // removing list2 completely
    aggregatedWrapper.removeList(list2);
    assertEquals("wrong content", "[1,3,5,100,110,120,130,140,150]",
        aggregatedWrapper.dump(ObservableIntegerValue::get));

    // updating one integer value in list 3
    SimpleIntegerProperty integer = (SimpleIntegerProperty) list3.get(0);
    integer.set(1);
    assertEquals("wrong content", "[1,3,5,1,110,120,130,140,150]",
        aggregatedWrapper.dump(ObservableIntegerValue::get));

    // prepending list 2 again
    aggregatedWrapper.prependList(list2);
    assertEquals("wrong content", "[201,202,203,1,3,5,1,110,120,130,140,150]",
        aggregatedWrapper.dump(ObservableIntegerValue::get));

  }

  @Test
  public void testSortedList() {
    AggregatedObservableArrayList<Integer> aoal = new AggregatedObservableArrayList<>("aoal");

    int listCount = 3;
    int elementCount = 3;
    ArrayList<ObservableList<Integer>> listOfList = new ArrayList<>();
    for (int i = 0; i < listCount; i++) {
      ObservableList<Integer> list = FXCollections.observableArrayList();
      for (int j = 0; j < elementCount; j++) {
        list.add((int) (Math.random() * 1000));
      }
      listOfList.add(list);
      aoal.appendList(list);
    }

    Assert.assertEquals(dump(listOfList), aoal.dump((val) -> val));
    for (ObservableList<Integer> list : listOfList) {
      Collections.sort(list);
      Assert.assertEquals(dump(listOfList), aoal.dump((val) -> val));
    }
  }

  @Test
  public void testObservaleWrapperEx() {
    AggregatedObservableArrayList<Integer> aoal = new AggregatedObservableArrayList<>("aoal");

    int listCount = 10;
    int elementCount = 10;
    ArrayList<ObservableListWrapperEx<Integer>> listOfList = new ArrayList<>();
    int index = 0;
    for (int i = 0; i < listCount; i++) {
      ObservableListWrapperEx<Integer> list = new ObservableListWrapperEx<>(new ArrayList<>());
      for (int j = 0; j < elementCount; j++) {
        list.add(index++);
      }
      listOfList.add(list);
      aoal.appendList(list);
    }

    Assert.assertEquals(dump(listOfList), aoal.dump((val) -> val));

    //元素向下移动
    for (int i = 0; i < listCount; i++) {
      ObservableListWrapperEx<Integer> list = listOfList.get(i);
      int size = list.size();
      for (int m = 0; m < size - 1; m++) {
        for (int n = m + 1; n < size; n++) {
          list.move(list.get(0), list.size() - 1);
          Assert.assertEquals(dump(listOfList), aoal.dump((val) -> val));
        }
      }
    }
    //元素向上移动
    for (int i = 0; i < listCount; i++) {
      ObservableListWrapperEx<Integer> list = listOfList.get(i);
      int size = list.size();
      for (int m = 1; m < size; m++) {
        for (int n = 0; n < m; n++) {
          list.move(list.get(m), n);
          Assert.assertEquals(dump(listOfList), aoal.dump((val) -> val));
        }
      }
    }

    //添加元素
    for (int i = 0; i < listCount; i++) {
      ObservableListWrapperEx<Integer> list = listOfList.get(i);
      list.add(0, (int) (Math.random() * 100));
      Assert.assertEquals(dump(listOfList), aoal.dump((val) -> val));
      list.add((int) (Math.random() * 100));
      Assert.assertEquals(dump(listOfList), aoal.dump((val) -> val));
    }
    //删除元素
    for (int i = 0; i < listCount; i++) {
      ObservableListWrapperEx<Integer> list = listOfList.get(i);
      list.remove(0);
      Assert.assertEquals(dump(listOfList), aoal.dump((val) -> val));
      list.remove(list.size() - 1);
      Assert.assertEquals(dump(listOfList), aoal.dump((val) -> val));
    }
    //remove add
    for (int i = 0; i < listCount; i++) {
      ObservableListWrapperEx<Integer> list = listOfList.get(i);

      List<Integer> removeItems = new ArrayList<>();
      removeItems.add(list.get(0));
      removeItems.add(list.get(list.size() - 1));

      List<Integer> addItems = new ArrayList<>();
      addItems.add(100);
      addItems.add(200);
      list.removeAndAdd(removeItems, addItems, 0);
      Assert.assertEquals(dump(listOfList), aoal.dump((val) -> val));
    }
    //set all with same data
    index = 1000;
    for (int i = 0; i < listCount; i++) {
      ObservableListWrapperEx<Integer> list = listOfList.get(i);
      list.setAll(1, 2, 3);
      index += 3;
      Assert.assertEquals(dump(listOfList), aoal.dump((val) -> val));
    }
    //set all with random length data
    for (int i = 0; i < listCount; i++) {
      ObservableListWrapperEx<Integer> list = listOfList.get(i);
      int length = (int) (3 + Math.random() * 10);
      Integer[] data = new Integer[length];
      for (int j = 0; j < length; j++) {
        data[j] = (int) (Math.random() * 100);
      }
      list.setAll(data);
      Assert.assertEquals(dump(listOfList), aoal.dump((val) -> val));
    }
    // remove all
    for (int i = 0; i < listCount; i++) {
      ObservableListWrapperEx<Integer> list = listOfList.get(i);
      list.removeAll();
      Assert.assertEquals(dump(listOfList), aoal.dump((val) -> val));
    }
    // add all with same data
    for (int i = 0; i < listCount; i++) {
      ObservableListWrapperEx<Integer> list = listOfList.get(i);
      list.addAll(1, 2, 3);
      Assert.assertEquals(dump(listOfList), aoal.dump((val) -> val));
    }

  }

  @Test
  public void testListRemove() {
    AggregatedObservableArrayList<String> aggregatedList = new AggregatedObservableArrayList<>("root");
    ObservableListWrapperEx<String> left = new ObservableListWrapperEx<>(new ArrayList<>());
    ObservableListWrapperEx<String> right = new ObservableListWrapperEx<>(new ArrayList<>());
    left.add("hello");
    right.add("hello");
    aggregatedList.appendList(left);
    aggregatedList.appendList(right);
    aggregatedList.removeList(right);
    left.removeAndAdd(Arrays.asList("hello"), Arrays.asList("world"), 0);
    Assert.assertEquals(dump(Arrays.asList(left)), aggregatedList.dump((val) -> val));

  }

  @Test
  public void testModifyWhileChange() {
    // 測試複合修改動作中的監聽
    ObservableListWrapperEx<String> value = new ObservableListWrapperEx<>(new ArrayList<>());
    AggregatedObservableArrayList<String> aggregatedList = new AggregatedObservableArrayList<>("root");
    aggregatedList.appendList(value);

    ObservableList<String> targetList = aggregatedList.getAggregatedList();
    targetList.addListener((ListChangeListener<? super String>) (change) -> {
      if (targetList.size() != 3) {
        Assert.fail();
      }
    });
    value.setAll("3", "4", "5");
    Assert.assertEquals("[3,4,5]", aggregatedList.dump((val) -> val));
    value.move("3", 1);
    Assert.assertEquals("[4,3,5]", aggregatedList.dump((val) -> val));
    value.removeAndAdd(Arrays.asList("4", "3"), Arrays.asList("6", "7"), 0);
    Assert.assertEquals("[6,7,5]", aggregatedList.dump((val) -> val));
  }

  @Test
  public void testEmbeddedList() {
    int count = 4;
    int deep = 3;
    List<AggregatedObservableArrayList<String>> aggregatedList = new ArrayList<>();
    List<ObservableListWrapperEx<String>> terminalList = new ArrayList<>();

    AggregatedObservableArrayList<String> rootList = new AggregatedObservableArrayList<>("root");
    ValueGenerator generator = new ValueGenerator();
    embeddedListImpl(deep, count, generator, rootList, aggregatedList, terminalList);

    System.out.println(dump(terminalList));
    Assert.assertEquals(dump(terminalList), rootList.dump((value) -> value));

    for (ObservableListWrapperEx<String> item : terminalList) {
      List<String> removeItem = new ArrayList<>();
      List<String> addItem = new ArrayList<>();
      removeItem.add(item.get(0));
      removeItem.add(item.get(1));
      addItem.add("hello");
      item.removeAndAdd(removeItem, addItem, 0);
      System.out.println(dump(terminalList));
      Assert.assertEquals(dump(terminalList), rootList.dump((value) -> value));
    }
  }

  private void embeddedListImpl(int level, int count, ValueGenerator gen,
                                AggregatedObservableArrayList<String> parentList,
                                List<AggregatedObservableArrayList<String>> aggregatedList,
                                List<ObservableListWrapperEx<String>> terminalList) {
    if (level <= 0) {
      for (int i = 0; i < count; i++) {
        ObservableListWrapperEx<String> terminal = new ObservableListWrapperEx<>(new ArrayList<>());
        parentList.appendList(terminal);
        terminalList.add(terminal);
        for (int j = 0; j < count; j++) {
          terminal.add(gen.generateValue() + "");
        }
      }
    } else {
      for (int i = 0; i < count; i++) {
        AggregatedObservableArrayList<String> item =
            new AggregatedObservableArrayList<>("level" + level + "_" + "index" + i);
        aggregatedList.add(item);
        embeddedListImpl(level - 1, count, gen, item, aggregatedList, terminalList);
        parentList.appendList(item.getAggregatedList());
      }
    }
  }

  class ValueGenerator {
    private int value = 0;

    public int generateValue() {
      return value++;
    }

    public int getLastValue() {
      return value;
    }
  }

  public <K, T extends ObservableList<K>> String dump(List<T> listOfList) {
    StringBuilder sb = new StringBuilder();
    sb.append("[");
    for (ObservableList<K> list : listOfList) {
      for (K item : list) {
        sb.append(item).append(',');
      }
    }
    final int length = sb.length();
    sb.replace(length - 1, length, "");
    sb.append("]");
    return sb.toString();
  }
}
