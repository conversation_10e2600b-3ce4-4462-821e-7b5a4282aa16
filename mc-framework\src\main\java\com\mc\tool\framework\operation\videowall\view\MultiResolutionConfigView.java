package com.mc.tool.framework.operation.videowall.view;

import com.mc.tool.framework.operation.videowall.datamodel.LayoutData;
import com.mc.tool.framework.utility.UndecoratedDialog;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import javafx.geometry.HPos;
import javafx.geometry.Pos;
import javafx.scene.control.ButtonType;
import javafx.scene.control.Label;
import javafx.scene.control.ScrollPane;
import javafx.scene.layout.ColumnConstraints;
import javafx.scene.layout.GridPane;
import javafx.scene.layout.HBox;
import javafx.scene.layout.RowConstraints;
import javafx.stage.Window;
import lombok.Getter;
import org.controlsfx.property.editor.NumericField;

/**
 * .
 */
public class MultiResolutionConfigView extends ScrollPane {
  private static final int COLUMN_WIDTH = 60;
  private static final int ROW_HEIGHT = 30;
  private static final int TEXT_WIDTH = 55;
  private static final int MAX_VALUE = 5000;
  private static final int MIN_VALUE = 100;
  private GridPane layoutGrid = new GridPane();

  @Getter
  private MultiResolution layoutData = null;

  public MultiResolutionConfigView() {
    this.setContent(layoutGrid);
  }

  /**
   * 设置布局数据.
   *
   * @param layoutData 布局数据
   */
  public void setLayoutData(MultiResolution layoutData) {
    layoutGrid.getChildren().clear();
    this.layoutData = layoutData;
    ColumnConstraints colConstraint = new ColumnConstraints(COLUMN_WIDTH / 2);
    colConstraint.setHalignment(HPos.CENTER);
    colConstraint.setFillWidth(false);
    RowConstraints rowConstraints = new RowConstraints(ROW_HEIGHT / 2);
    layoutGrid.getColumnConstraints().add(colConstraint);
    layoutGrid.getColumnConstraints().add(colConstraint);
    layoutGrid.getRowConstraints().add(rowConstraints);
    layoutGrid.getRowConstraints().add(rowConstraints);
    //列宽编辑
    for (int i = 0; i < layoutData.getColumns(); i++) {
      NumericField field = new NumericField(Integer.class, MAX_VALUE, MIN_VALUE);
      field.setPrefWidth(TEXT_WIDTH);
      field.valueProperty().setValue(layoutData.getWidths().get(i));
      final int index = i;
      field.valueProperty().addListener(
          (obs, oldVal, newVal) -> layoutData.getWidths().set(index, newVal.intValue()));
      layoutGrid.getColumnConstraints().add(colConstraint);
      layoutGrid.getColumnConstraints().add(colConstraint);
      layoutGrid.add(field, (i + 1) * 2, 0, 2, 2);
    }

    //行高编辑
    for (int i = 0; i < layoutData.getRows(); i++) {
      NumericField field = new NumericField(Integer.class, MAX_VALUE, MIN_VALUE);
      field.setPrefWidth(TEXT_WIDTH);
      field.valueProperty().setValue(layoutData.getHeights().get(i));
      final int index = i;
      field.valueProperty().addListener(
          (obs, oldVal, newVal) -> layoutData.getHeights().set(index, newVal.intValue()));
      layoutGrid.getRowConstraints().add(rowConstraints);
      layoutGrid.getRowConstraints().add(rowConstraints);
      layoutGrid.add(field, 0, (i + 1) * 2, 2, 2);
    }

    //横向margin
    layoutGrid.getColumnConstraints().add(colConstraint);
    layoutGrid.getColumnConstraints().add(colConstraint);
    layoutGrid.getRowConstraints().add(rowConstraints);
    layoutGrid.getRowConstraints().add(rowConstraints);
    for (int i = 0; i < layoutData.getColumns() - 1; i++) {

      NumericField field = new NumericField(Integer.class, Integer.MAX_VALUE, Integer.MIN_VALUE);
      field.setPrefWidth(TEXT_WIDTH);
      field.valueProperty().setValue(layoutData.getHorzMargins().get(i));
      final int index = i;
      field.valueProperty().addListener(
          (obs, oldVal, newVal) -> layoutData.getHorzMargins().set(index, newVal.intValue()));
      layoutGrid.add(field, (i + 1) * 2 + 1, (layoutData.getRows() + 1) * 2, 2, 2);
    }
    //纵向margin
    for (int i = 0; i < layoutData.getRows() - 1; i++) {
      NumericField field = new NumericField(Integer.class, Integer.MAX_VALUE, Integer.MIN_VALUE);
      field.setPrefWidth(TEXT_WIDTH);
      field.valueProperty().setValue(layoutData.getVertMargins().get(i));
      final int index = i;
      field.valueProperty().addListener(
          (obs, oldVal, newVal) -> layoutData.getVertMargins().set(index, newVal.intValue()));
      layoutGrid.add(field, (layoutData.getColumns() + 1) * 2, (i + 1) * 2 + 1, 2, 2);
    }
    // 网格

    for (int i = 0; i < layoutData.getRows(); i++) {
      for (int j = 0; j < layoutData.getColumns(); j++) {
        HBox pane = new HBox();
        pane.setAlignment(Pos.CENTER);
        pane.setPrefWidth(COLUMN_WIDTH);
        pane.setPrefHeight(ROW_HEIGHT);
        if (j == 0 && i == 0) {
          pane.setStyle("-fx-background-color: black, white; -fx-background-insets : 0, 1");
          Label label = new Label("1");
          pane.getChildren().add(label);
        } else if (j == 0) {
          pane.setStyle("-fx-background-color: black, white; -fx-background-insets : 0, 0 1 1 1");
          Label label = new Label((i + 1) + "");
          pane.getChildren().add(label);
        } else if (i == 0) {
          Label label = new Label((j + 1) + "");
          pane.getChildren().add(label);
          pane.setStyle("-fx-background-color: black, white; -fx-background-insets : 0, 1 1 1 0");
        } else {
          pane.setStyle("-fx-background-color: black, white; -fx-background-insets : 0, 0 1 1 0");
        }

        layoutGrid.add(pane, (j + 1) * 2, (i + 1) * 2, 2, 2);
      }
    }

  }


  /**
   * 显示布局配置界面.
   *
   * @param owner dialog的owner
   * @param data  布局数据
   * @param title dialog的titile
   */
  public static void config(Window owner, LayoutData data, String title) {
    UndecoratedDialog<ButtonType> dialog = new UndecoratedDialog<>();
    MultiResolutionConfigView view = new MultiResolutionConfigView();
    MultiResolution resolution = new MultiResolution();
    resolution.fromLayout(data);
    view.setLayoutData(resolution);
    dialog.initOwner(owner);
    dialog.setTitle(title);
    dialog.getDialogPane().setContent(view);
    dialog.getDialogPane().getButtonTypes().addAll(ButtonType.CANCEL, ButtonType.APPLY);
    dialog.getDialogPane().setMaxWidth(600);
    dialog.getDialogPane().setMaxHeight(400);
    Optional<ButtonType> result = dialog.showAndWait();
    if (result.isPresent() && result.get() == ButtonType.APPLY) {
      resolution.toLayout(data);
    }
  }

  /**
   * 显示布局预览.
   *
   * @param data 布局数据.
   */
  public static void preview(LayoutData data) {

  }

  static class MultiResolution {
    @Getter
    private int columns;
    @Getter
    private int rows;
    @Getter
    private List<Integer> widths = new ArrayList<>();
    @Getter
    private List<Integer> heights = new ArrayList<>();

    @Getter
    private List<Integer> horzMargins = new ArrayList<>();

    @Getter
    private List<Integer> vertMargins = new ArrayList<>();

    public void fromLayout(LayoutData data) {
      columns = data.getColumns();
      rows = data.getRows();
      widths.clear();

      widths.addAll(data.getWidths());
      heights.clear();
      heights.addAll(data.getHeights());
      horzMargins.clear();

      horzMargins.addAll(data.getHorzMargins());
      vertMargins.clear();

      vertMargins.addAll(data.getVertMargins());
    }

    public void toLayout(LayoutData data) {
      data.getColumnsProperty().set(columns);
      data.getRowsProperty().set(rows);
      data.updateMultiResWidths(widths);
      data.updateMultiResHeights(heights);
      data.updateHorzMargins(horzMargins);
      data.updateVertMargins(vertMargins);
      // 更新限制
      data.updateHorzMargins(data.getAdaptiveHorzMargins());
      data.updateVertMargins(data.getAdaptiveVertMargins());
      data.getMultiResObservable().update();
    }
  }
}
