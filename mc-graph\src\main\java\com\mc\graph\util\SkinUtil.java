package com.mc.graph.util;

import com.mc.graph.interfaces.CellObject;
import com.mc.graph.interfaces.CellSkin;
import com.mc.graph.interfaces.LinkObject;
import com.mc.graph.interfaces.LinkSkin;
import com.mc.graph.interfaces.OverviewableGraphCanvas;
import com.mc.graph.interfaces.SkinFactory;
import com.mc.graph.interfaces.SkinManager;

public class SkinUtil {
  /**
   * 添加一个cellskin到overview canvas.
   * @param canvas 主canvas
   * @param innerCanvas overview canvas
   * @param cellObject 要创建cellskin的cellobject
   * @param skinFactory skin factory
   * @param skinManager skin manager
   * @return 创建的cell skin
   */
  public static CellSkin addCellSkin(OverviewableGraphCanvas canvas, 
      OverviewableGraphCanvas.OverviewCanvas innerCanvas, 
      CellObject cellObject, 
      SkinFactory skinFactory, SkinManager skinManager) {
    CellSkin skin = null;
    if (canvas.isCellScaleEnable()) {
      skin = skinFactory.createScaleCellSkin(cellObject, innerCanvas.getGraphContainer(),
        innerCanvas.getGraphContainer(), skinManager, canvas.getCellScaleProperty());
    } else {
      skin = skinFactory.createCellSkin(cellObject, innerCanvas.getGraphContainer(),
          innerCanvas.getGraphContainer(), skinManager);
    }
    skin.add();
    return skin;
  }
  
  /**
   * 创建link skin并加入到overview canvas中.
   * @param innerCanvas overview canvas
   * @param linkObject 要创建skin的link object
   * @param skinFactory skin factory
   * @param skinManager skin manager
   * @return 创建的link skin
   */
  public static LinkSkin addLinkSkin(OverviewableGraphCanvas.OverviewCanvas innerCanvas, 
      LinkObject linkObject, SkinFactory skinFactory, SkinManager skinManager) {
    LinkSkin skin = skinFactory.createLinkSkin(linkObject, innerCanvas.getGraphContainer(),
        innerCanvas.getGraphContainer(), skinManager);
    skin.add();
    return skin;
  }
}
