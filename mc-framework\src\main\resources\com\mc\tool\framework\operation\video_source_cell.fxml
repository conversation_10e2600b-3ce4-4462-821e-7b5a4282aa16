<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.Label?>
<?import javafx.scene.layout.VBox?>
<VBox alignment="CENTER" stylesheets="@video_source_cell.css" xmlns="http://javafx.com/javafx/8"
      xmlns:fx="http://javafx.com/fxml/1">
  <children>
    <Label id="video-pic" fx:id="videoPic" onDragDetected="#onDragDetected" onDragDone="#onDragDone" prefHeight="108"
           prefWidth="71"/>
    <Label id="video-name" fx:id="videoName" alignment="CENTER" prefWidth="60" textAlignment="CENTER"/>
  </children>
</VBox>
