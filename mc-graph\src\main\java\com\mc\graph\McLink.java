package com.mc.graph;

import com.mc.graph.connector.ConnectorIntegration;
import com.mc.graph.interfaces.Connector;
import com.mc.graph.interfaces.LinkObject;
import javafx.util.Pair;

public class Mc<PERSON>ink extends AbstractGraphObject implements LinkObject {
  private Connector first;
  private Connector second;
  
  private ConnectorIntegration connectorIntegration = new ConnectorIntegration();
  
  public McLink() {
    connectorIntegration.bindHighLightValue(highLightProperty());
    connectorIntegration.bindHighLightColorValue(highLightColorProperty());
  }
  
  @Override
  public void setConnectors(Connector first, Connector second) {
    this.first = first;
    this.second = second;
    visibleProperty.unbind();
    visibleProperty.bind(first.visibleProperty().and(second.visibleProperty()));
  }

  @Override
  public Pair<Connector, Connector> getConnectors() {
    return new Pair<Connector, Connector>(first, second);
  }

  @Override
  public ConnectorIntegration getConnectorIntegration() {
    return connectorIntegration;
  }

  @Override
  public void destroy() {
    connectorIntegration.destroy();
  }

}
