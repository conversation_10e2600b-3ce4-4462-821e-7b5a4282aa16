package com.mc.tool.framework.systemedit.controller;

import com.mc.graph.interfaces.CellObject;
import com.mc.graph.interfaces.CellPropertyUpdater;
import com.mc.graph.interfaces.CellSkin;
import com.mc.graph.interfaces.ConnectorSkin;
import com.mc.graph.interfaces.SkinManager;
import com.mc.tool.framework.systemedit.datamodel.SystemEditDefinition;
import javafx.beans.value.ChangeListener;

/**
 * .
 */
public class CellXposUpdater implements CellPropertyUpdater {
  private CellObject cellObject;
  private SkinManager skinManager;
  private boolean attached = false;
  private boolean isLeft = false;
  private ConnectorSkin parentConnectorSkin = null;
  private CellSkin cellSkin = null;
  private ChangeListener<Number> changeListener;

  /**
   * Constructor.
   *
   * @param skinManager skin manager
   * @param cell        需要更新x坐标的cell
   */
  public CellXposUpdater(SkinManager skinManager, CellObject cell) {
    this.cellObject = cell;
    this.skinManager = skinManager;
    changeListener = (obs, oldVal, newVal) -> cellObject.getXProperty().set(getXpos());
  }

  /**
   * 设置父节点的connector的skin.
   *
   * @param skin   父节点的connector的skin.
   * @param isLeft 是否子父节点的左边.
   */
  public void setParentConnectorSkin(ConnectorSkin skin, boolean isLeft) {
    if (this.parentConnectorSkin == skin && this.isLeft == isLeft
        && cellSkin == skinManager.getCellSkin(cellObject)) {
      attach();
      return;
    }
    detach();
    this.parentConnectorSkin = skin;
    this.isLeft = isLeft;
    this.cellSkin = skinManager.getCellSkin(cellObject);
    attach();
  }

  protected double getXpos() {
    if (parentConnectorSkin == null) {
      return 0;
    }
    double aimingXpos = parentConnectorSkin.getContainerXposProperty().get();
    double rightOffset = SystemEditDefinition.CELL_X_OFFSET;
    double leftOffset = -SystemEditDefinition.CELL_X_OFFSET
        - skinManager.getCellSkin(cellObject).getRegion().widthProperty().get();
    if (isLeft) {
      return aimingXpos + leftOffset;
    } else {
      return aimingXpos + rightOffset;
    }
  }

  @Override
  public void destroy() {
    detach();
    parentConnectorSkin = null;
    cellObject = null;
    skinManager = null;
    cellSkin = null;
    changeListener = null;
  }

  @Override
  public void detach() {
    if (parentConnectorSkin != null) {
      parentConnectorSkin.getContainerXposProperty().removeListener(changeListener);

    }
    if (cellSkin != null) {
      cellSkin.getRegion().widthProperty().removeListener(changeListener);
    }
    attached = false;
  }

  @Override
  public void attach() {
    if (attached) {
      return;
    }
    if (parentConnectorSkin != null) {
      parentConnectorSkin.getContainerXposProperty().addListener(changeListener);
    }
    if (cellSkin != null) {
      cellSkin.getRegion().widthProperty().addListener(changeListener);
    }
    cellObject.getXProperty().set(getXpos());
    attached = true;
  }

}
