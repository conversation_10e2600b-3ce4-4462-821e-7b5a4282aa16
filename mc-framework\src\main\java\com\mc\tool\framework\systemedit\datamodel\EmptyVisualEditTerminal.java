package com.mc.tool.framework.systemedit.datamodel;

import com.mc.graph.interfaces.ConnectorIdentifier;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.collections.ObservableList;

/**
 * 空的terminal，不能与设备绑定.
 *
 * <AUTHOR>
 */
public class EmptyVisualEditTerminal extends AbstractVisualEditNode implements VisualEditTerminal {
  private final Boolean isRx;

  private ObjectProperty<TargetDeviceType> targetDeviceType =
      new SimpleObjectProperty<>(TargetDeviceType.COMPUTER);

  private BooleanProperty targetDeviceConnected = new SimpleBooleanProperty(false);

  public EmptyVisualEditTerminal(String name, boolean isRx) {
    setName(name);
    this.isRx = isRx;
  }

  @Override
  public boolean isRx() {
    return isRx;
  }

  @Override
  public boolean isTx() {
    return !isRx;
  }

  @Override
  public String getGuid() {
    return SystemEditDefinition.EMPTY_TERMINAL_GUID;
  }

  @Override
  public String getNodeType() {
    return SystemEditDefinition.EMPTY_TERMINAL_CELL;
  }

  @Override
  public int getPortCount() {
    return 0;
  }

  @Override
  public ObservableList<VisualEditTerminal> getAllTerminalChild() {
    return null;
  }

  @Override
  public ObservableList<VisualEditFunc> getAllFunctions() {
    return null;
  }

  @Override
  public void init() {

  }

  @Override
  public ObservableList<ConnectorIdentifier> getConnectorId() {
    return null;
  }

  @Override
  public boolean isOnline() {
    return false;
  }

  @Override
  public Resolution getResolution(int index) {
    return null;
  }

  @Override
  public boolean isTargetDeviceConnected() {
    return false;
  }

  @Override
  public BooleanProperty targetDeviceConnectedProperty() {
    return targetDeviceConnected;
  }

  @Override
  public TargetDeviceType getTargetDeviceType() {
    return targetDeviceType.get();
  }

  @Override
  public void setTargetDeviceType(TargetDeviceType type) {
    targetDeviceType.set(type);
  }

  @Override
  public ObjectProperty<TargetDeviceType> targetDeviceTypeProperty() {
    return targetDeviceType;
  }

  @Override
  public NetworkInterfaceType getNetworkInterfaceType() {
    return null;
  }

  @Override
  public void setNetworkInterfaceType(NetworkInterfaceType type) {

  }

  @Override
  public ObjectProperty<NetworkInterfaceType> networkInterfaceTypeProperty() {
    return null;
  }

  @Override
  public MultimediaInterfaceType getMultimediaInterfaceType() {
    return null;
  }

  @Override
  public void setMultimediaInterfaceType(MultimediaInterfaceType type) {

  }

  @Override
  public ObjectProperty<MultimediaInterfaceType> multimediaInterfaceTypeProperty() {
    return null;
  }

  @Override
  public boolean canSeperate() {
    return false;
  }

  @Override
  public void seperate(boolean sep) {

  }

  @Override
  public BooleanProperty onlineProperty() {
    return null;
  }

  @Override
  public boolean isValid() {
    return true;
  }

}
