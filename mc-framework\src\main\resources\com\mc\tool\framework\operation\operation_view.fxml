<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.ListView?>
<?import javafx.scene.control.ToggleButton?>
<?import javafx.scene.control.TreeView?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.Region?>
<?import javafx.scene.layout.StackPane?>
<?import javafx.scene.layout.VBox?>
<fx:root type="javafx.scene.layout.VBox" xmlns="http://javafx.com/javafx/8"
         xmlns:fx="http://javafx.com/fxml/1" stylesheets="@operation_view.css" prefHeight="720" prefWidth="1280" id="root">
  <HBox id="header-pane" prefHeight="40" minHeight="40" alignment="CENTER">
    <Label id="function-title" fx:id="functionTitle"/>
    <Region HBox.Hgrow="ALWAYS"/>
    <HBox id="header-toolbox" alignment="CENTER_RIGHT">
      <Button id="save-btn" fx:id="saveScenarioBtn" onAction="#onSaveScenario" styleClass="image-button"
              text="%toolbar.save_scenario"/>
      <Button id="saveas-btn" fx:id="saveAsScenarioBtn" onAction="#onSaveAsScenario" styleClass="image-button"
              text="%toolbar.saveas_scenario"/>
      <Button id="config-btn" fx:id="configBtn" onAction="#onConfig" styleClass="image-button" text="%toolbar.config"/>
      <Button id="switch-btn" onAction="#onSwitch" styleClass="image-button" text="%toolbar.meeting_room"/>
    </HBox>
  </HBox>
  <Region id="seperator" minHeight="1"/>
  <HBox VBox.Vgrow="ALWAYS" id="body-pane">
    <VBox id="function-source" minWidth="200" prefWidth="200">
      <HBox id="source-list-title-container" minHeight="24" alignment="CENTER_LEFT">
        <Label id="source-list-title" text="%source_list_title"/>
        <Region HBox.Hgrow="ALWAYS"/>
        <ToggleButton styleClass="image-button" fx:id="sourceListStyleBtn"/>
      </HBox>
      <VBox VBox.vgrow="ALWAYS">
        <ListView VBox.vgrow="ALWAYS" fx:id="sourceList"/>
        <TreeView VBox.vgrow="ALWAYS" fx:id="sourceTree"/>
      </VBox>
      <VBox fx:id="videoPreviewContainer" minHeight="139" maxHeight="139"/>
      <Region minHeight="1"/>
    </VBox>
    <VBox id="function-content" HBox.hgrow="ALWAYS">
      <HBox VBox.Vgrow="ALWAYS">
        <StackPane prefWidth="70" minWidth="70">
          <Label id="left-arrow" fx:id="leftArrow" prefWidth="32" prefHeight="56" onMouseClicked="#onLeftArrow"/>
        </StackPane>
        <StackPane HBox.Hgrow="ALWAYS" fx:id="functionView"/>
        <StackPane prefWidth="70" minWidth="70">
          <Label id="right-arrow" fx:id="rightArrow" prefWidth="32" prefHeight="56" onMouseClicked="#onRightArrow"/>
        </StackPane>
      </HBox>
    </VBox>


  </HBox>


</fx:root>