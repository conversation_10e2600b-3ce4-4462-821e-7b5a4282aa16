package org.controlsfx.control;

import javafx.beans.property.ObjectProperty;
import javafx.event.ActionEvent;
import javafx.event.EventHandler;

public class TextBlockPropertyItem extends ObjectPropertyItem<String> {

  private EventHandler<ActionEvent> handler = null;

  public TextBlockPropertyItem(ObjectProperty<String> value) {
    super(value, String.class);
  }
  
  public TextBlockPropertyItem(ObjectProperty<String> value, boolean needToUnbindValue) {
    super(value, String.class, needToUnbindValue);
  }

  public void setTextAction(EventHandler<ActionEvent> handler) {
    this.handler = handler;
  }

  public EventHandler<ActionEvent> getTextAction() {
    return handler;
  }

  @Override
  public void setValue(Object value) {
    if (clazz.isInstance(value)) {
      this.value.set(clazz.cast(value));
    }
  }

}
