@import "../common.css";


#statusPanel {
    -fx-background-color: gray;
}


#mainContainer {
    -fx-border-insets: 1;
    -fx-border-size: 1;
    -fx-border-color: #cccccc;
}


#panel {
    -fx-border-size: 1;
    -fx-border-color: #cccccc;
}


#userPanel {
    -fx-spacing: 10;
    -fx-border-insets: 30 30 5 30;
    -fx-border-color: transparent;
}

#permissionPanel {
    -fx-spacing: 10;
    -fx-border-insets: 30;
    -fx-border-color: transparent;
}

#tabPanel {
    -fx-spacing: 5;
}


#bottomPanel {
    -fx-spacing: 5;
    -fx-border-insets: 5;
    -fx-border-color: transparent;
}

#seperator {
    -fx-background-color: #cccccc;
}

#titlePanel {
    -fx-border-insets: 2;
    -fx-border-color: transparent;
}

.string-column-style {
    -fx-label-padding: 0 0 0 4;
}

#font-green {
    -fx-text-fill: green;
}


#font-orange {
    -fx-text-fill: orange;
}


#font-red {
    -fx-text-fill: red;
}


