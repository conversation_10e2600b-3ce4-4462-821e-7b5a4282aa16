.cell-container {
    -fx-shape: "M0.5,0.5h170v36H0.5V0.5z";
    -fx-border-color: #b3b3b3;
    -fx-background-color: white;

}

#name-text {
    -fx-label-padding: 0 5 0 5;
}

.cell-seperator {
    -fx-pref-width: 1;
    -fx-min-width: 1;
    -fx-background-color: #a6a6a6;
    -fx-background-insets: 8 0 7 0;
}

.connected-type {
    -fx-background-color: #f2f2f2;
    -fx-background-insets: 1;
    -fx-vgap: 0;
    -fx-hgap: 0;
    -fx-padding: 1;
}