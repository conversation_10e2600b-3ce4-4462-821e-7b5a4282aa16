package com.mc.tool.framework.operation.videowall.menu;

import com.mc.tool.framework.operation.videowall.controller.VideoWallControllable;
import com.mc.tool.framework.operation.videowall.menu.MenuAlign.AlignType;
import com.mc.tool.framework.utility.I18nUtility;
import javafx.scene.control.Menu;

/**
 * .
 */
public class MenuAlignGroup extends Menu {

  /**
   * Constructor.
   *
   * @param controllable controllable
   */
  public MenuAlignGroup(VideoWallControllable controllable) {
    setText(I18nUtility.getI18nBundle("operation").getString("menu.align"));
    setDisable(controllable.getSelectedVideos().size() <= 1);

    getItems().add(new MenuAlign(controllable, AlignType.LEFT_ALIGN));
    getItems().add(new MenuAlign(controllable, AlignType.RIGHT_ALIGN));
    getItems().add(new MenuAlign(controllable, AlignType.HORZ_CENTER_ALIGN));
    getItems().add(new MenuAlign(controllable, AlignType.TOP_ALIGN));
    getItems().add(new MenuAlign(controllable, AlignType.BOTTOM_ALIGN));
    getItems().add(new MenuAlign(controllable, AlignType.VERT_CENTER_ALIGN));
  }
}
