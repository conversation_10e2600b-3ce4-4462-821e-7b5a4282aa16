from xml.etree import ElementTree as ET
import sys
import regex as re
import subprocess

def splitVersion(versionValue):
    items = versionValue.strip().split(".")
    if len(items) != 3:
        print("Version value is error:", versionValue)
        exit(-1)
    else:
        return items

def modifyRevision(fileName, version):
    honorPom = open(fileName, "r", encoding="utf-8")
    content = "".join(honorPom.readlines())
    honorPom.close()
    content = re.sub("<revision>[^<]*</revision>", "<revision>%s</revision>"%version, content)
    honorPom = open(fileName, "w", encoding="utf-8")
    honorPom.write(content)
    honorPom.close()
def modifyApplicationVersion(version):
    fileName = "mc-honor-vpm/src/main/java/com/mc/tool/honor/vpm/HonorApplication.java"
    javaFile = open(fileName, "r", encoding="utf-8")
    content = "".join(javaFile.readlines())
    javaFile.close()
    versionItems = splitVersion(version)
    
    content = re.sub(r"(?<=getMainVersion\(\)\s*\{\s*[\r\n]*\s*return\s*)(\w*)(?=;)", versionItems[0], content, flags=re.MULTILINE)
    content = re.sub(r"(?<=getSubVersion\(\)\s*\{\s*[\r\n]*\s*return\s*)(\w*)(?=;)", versionItems[1], content, flags=re.MULTILINE)
    content = re.sub(r"(?<=getModifyVersion\(\)\s*\{\s*[\r\n]*\s*return\s*)(\w*)(?=;)", versionItems[2], content, flags=re.MULTILINE)
    
    javaFile = open(fileName, "w", encoding="utf-8")
    javaFile.write(content)
    javaFile.close()

def modifyApiVersion(version):
    fileName = "HonorServer/bundles/bundle-device-controller/src/main/java/com/mc/tool/honor/server/devicecontroller/HonorServerApiImpl.java"
    javaFile = open(fileName, "r", encoding="utf-8")
    content = "".join(javaFile.readlines())
    javaFile.close()

    content = re.sub(r'(?<=public\s*static\s*final\s*String\s*VERSION\s*=\s*)"[^"]*"', '"%s"'%version, content)
    javaFile = open(fileName, "w", encoding="utf-8")
    javaFile.write(content)
    javaFile.close()

def modifyTrayExeVersion(version):
    fileName = "HonorServer/HonorServerTray/Properties/AssemblyInfo.cs"
    csFile = open(fileName, "r", encoding="utf-8")
    content = "".join(csFile.readlines())
    csFile.close()

    content = re.sub(r'(?<=AssemblyVersion\(")[^"]*(?=")', version, content)
    content = re.sub(r'(?<=AssemblyFileVersion\(")[^"]*(?=")', version, content)
    csFile = open(fileName, "w", encoding="utf-8")
    csFile.write(content)
    csFile.close()

def modifyIssVersion(version):
    fileName = "HonorServer/WindowsPackage/makesetup.iss"
    issFile = open(fileName, "r", encoding="utf-8")
    content = "".join(issFile.readlines())
    issFile.close()

    content = re.sub(r'(?<=#define\s*MyAppVersion\s*")[^"]*(?=")', version, content)
    issFile = open(fileName, "w", encoding="utf-8")
    issFile.write(content)
    issFile.close()

def checkFile(name):
    result = subprocess.run("where %s"%name)
    if result.returncode != 0:
        print("%s is needed."%name)
        exit(1)

if len(sys.argv) < 2:
    print("Error: Need version.")
    print("Example: python honor_publish.py 3.0.0")
    exit(-1)


checkFile("7z")
checkFile("mvn")

version = sys.argv[1]
modifyRevision("mc-honor-build/pom.xml", version + "-RELEASE")
modifyRevision("HonorServer/server-application/pom.xml", version)
modifyRevision("HonorServer/bundles/bundle-device-controller/pom.xml", version + "-RELEASE")
modifyApplicationVersion(version)
modifyApiVersion(version)
modifyTrayExeVersion(version)
modifyIssVersion(version)

print("Start to clean mc-honor-build.")
result = subprocess.run("mvn -f mc-honor-build/pom.xml clean", shell="True")
if result.returncode != 0:
    print("Fail to clean mc-honor-build")
    exit(1)
print("Start to install mc-honor-build.")
result = subprocess.run("mvn -f mc-honor-build/pom.xml install -Dmaven.test.skip=true ", shell="True")
if result.returncode != 0:
    print("Fail to install mc-honor-build")
    exit(1)
print("Start to clean HonorServer.")
result = subprocess.run("mvn -f HonorServer/pom.xml clean", shell="True")
if result.returncode != 0:
    print("Fail to clean HonorServer.")
    exit(1)
print("Start to install HonorServer.")
result = subprocess.run("mvn -f HonorServer/pom.xml install -Dmaven.test.skip=true ", shell="True")
if result.returncode != 0:
    print("Fail to install HonorServer.")
    exit(1)
print("Start to rebuild HonorServerTray")
result = subprocess.run("devenv HonorServer/HonorServerTray/HonorServerTray.sln /rebuild release", shell="True")
if result.returncode != 0:
    print("Fail to rebuild HonorServerTray.")
    exit(1)
print("Start to package HonorServerTray")
result = subprocess.run("cd HonorServer/WindowsPackage && package.bat", shell="True")
if result.returncode != 0:
    print("Fail to package HonorServerTray.")
    exit(1)

print("Start to move file.")
subprocess.run(r'xcopy /y "mc-honor-vpm\target\HonorVPM v%s-RELEASE.exe" publish\honor\%s\vpm\ '%(version, version))
subprocess.run(r'xcopy /y "mc-honor-vpm\target\proguard_map.txt" publish\honor\%s\vpm\ '%(version))
subprocess.run(r'xcopy /y "HonorServer\server-application\target\server-application-%s.jar" publish\honor\%s\server\ '%(version, version))
subprocess.run(r'xcopy /y "HonorServer\server-application\target\proguard_map.txt" publish\honor\%s\server\ '%(version))
subprocess.run(r'xcopy /y "HonorServer\WindowsPackage\HonorServer_%s_setup.exe" publish\honor\%s\server\ '%(version, version))
print("Publish successfully!")






