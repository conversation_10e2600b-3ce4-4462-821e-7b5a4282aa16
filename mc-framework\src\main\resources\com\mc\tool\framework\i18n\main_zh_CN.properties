#Generated by ResourceBundle Editor (http://essiembre.github.io/eclipse-rbe/)
framework.menu.about=\u5173\u4E8E
framework.menu.about.title=\u5173\u4E8E
framework.menu.close=\u5173\u95ED
framework.menu.closeall=\u5173\u95ED\u5168\u90E8
framework.menu.config=\u914D\u7F6E
framework.menu.config.basic=\u57FA\u672C
framework.menu.config.basic.i18n=\u8BED\u8A00
framework.menu.config.basic.i18n.ch=\u4E2D\u6587
framework.menu.config.basic.i18n.en=English
framework.menu.config.prompt.msg=\u4E3A\u4E86\u6FC0\u6D3B\u6240\u6709\u4FEE\u6539\uFF0C\u5FC5\u987B\u91CD\u542F\u8F6F\u4EF6\uFF01
framework.menu.config.prompt.title=\u63D0\u793A
framework.menu.config.syslog=\u65E5\u5FD7\u670D\u52A1\u5668
framework.menu.config.title=\u8F6F\u4EF6\u914D\u7F6E
framework.menu.connect=\u8BBE\u5907
framework.menu.export=\u5BFC\u51FA
framework.menu.file=\u6587\u4EF6
framework.menu.help=\u5E2E\u52A9
framework.menu.search=\u67E5\u627E\u8BBE\u5907
framework.next=\u4E0B\u4E00\u6B65
framework.objectlist.device_name=\u8BBE\u5907
framework.objectlist.menu.connect_format=\u8FDE\u63A5%s
framework.objectlist.menu.create_format=\u521B\u5EFA%s
framework.objectlist.monitor_name=\u76D1\u63A7
framework.previous=\u4E0A\u4E00\u6B65
framework.start.text=\u672A\u8FDE\u63A5\u8BBE\u5907
framework.status.offline=\u79BB\u7EBF
framework.status.online=\u5728\u7EBF
