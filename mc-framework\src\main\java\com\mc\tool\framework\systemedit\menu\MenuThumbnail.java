package com.mc.tool.framework.systemedit.menu;

import com.mc.tool.framework.systemedit.controller.SystemEditControllable;
import com.mc.tool.framework.systemedit.datamodel.VisualEditNode;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import com.mc.tool.framework.systemedit.datamodel.preview.VideoThumbnailFunc;
import com.mc.tool.framework.systemedit.menu.predicate.MenuPredicateBinding;
import com.mc.tool.framework.systemedit.menu.predicate.TypePredicate;
import com.mc.tool.framework.utility.I18nUtility;
import java.util.function.Predicate;
import javafx.beans.binding.BooleanBinding;
import javafx.beans.property.ReadOnlyBooleanWrapper;
import javafx.scene.control.MenuItem;

/**
 * .
 */
public class MenuThumbnail extends MenuItem {
  private final SystemEditControllable controllable;

  /**
   * Constructor.
   *
   * @param controllable controllable
   */
  public MenuThumbnail(SystemEditControllable controllable) {
    this.controllable = controllable;
    this.setText(I18nUtility.getI18nBundle("systemedit").getString("menu.thumbnail"));
    this.setOnAction((event) -> onAction());
    this.disableProperty().bind(getMenuDisableBinding(controllable));
  }

  /**
   * 获取菜单是否应该禁掉的binding.
   *
   * @param controllable controllable
   * @return binding
   */
  public BooleanBinding getMenuDisableBinding(SystemEditControllable controllable) {
    MenuPredicateBinding binding = MenuGroup.getMenuDisableBinding(controllable);
    Predicate<VisualEditNode> notTypePredicate =
        new TypePredicate(VisualEditTerminal.class).negate();
    binding.addSingleSelectionPredicate(notTypePredicate);
    binding.addSingleSelectionPredicate((node) -> node.isTx());
    binding.addAllSelectionPredicate((nodes) -> nodes.size() != 1);
    // 只能有一个preview的组
    return binding.or(new ReadOnlyBooleanWrapper(!controllable.getModel().getAllOnlineFuncs()
        .filtered((func) -> func instanceof VideoThumbnailFunc).isEmpty()));
  }

  private void onAction() {
    controllable.addGroup("Thumbnail", VideoThumbnailFunc.class,
        controllable.getSelectedNodes().toArray(new VisualEditNode[0]));
  }
}
