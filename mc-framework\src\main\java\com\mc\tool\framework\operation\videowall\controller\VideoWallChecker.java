package com.mc.tool.framework.operation.videowall.controller;

import com.mc.tool.framework.operation.videowall.datamodel.interfaces.ScreenObject;
import com.mc.tool.framework.operation.videowall.datamodel.interfaces.VideoWallObject;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.function.Function;
import lombok.Getter;
import lombok.Setter;

/**
 * .
 */
public class VideoWallChecker {
  private List<Function<VideoWallObject, Collection<VideoWallError>>> functions = new ArrayList<>();

  /**
   * 检查视频墙的错误.
   *
   * @param videoWallData 视频墙数据
   * @return 错误列表
   */
  public Collection<VideoWallError> check(VideoWallObject videoWallData) {
    List<VideoWallError> errors = new ArrayList<>();

    for (Function<VideoWallObject, Collection<VideoWallError>> func : functions) {
      Collection<VideoWallError> errorList = func.apply(videoWallData);
      errors.addAll(errorList);
    }
    return errors;
  }

  public void registerChecker(Function<VideoWallObject, Collection<VideoWallError>> function) {
    functions.add(function);
  }

  /**
   * .
   */
  public static class VideoWallError {
    @Getter
    @Setter
    private ScreenObject screenData;
    @Getter
    @Setter
    private String errorReason;
  }
}
