package org.controlsfx.control;

import javafx.beans.property.ObjectProperty;
import javafx.util.StringConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.function.Predicate;

public class EnumPropertyItem<T> extends ObjectPropertyItem<T> {
  @Setter
  @Getter
  private StringConverter<T> converter = null;
  @Setter
  @Getter
  private Predicate<T> filter = null;

  public EnumPropertyItem(ObjectProperty<T> value, Class<T> clazz) {
    super(value, clazz);
  }

  public EnumPropertyItem(ObjectProperty<T> value, Class<T> clazz, StringConverter<T> converter) {
    super(value, clazz);
    this.converter = converter;
  }

  public EnumPropertyItem(ObjectProperty<T> value, Class<T> clazz, StringConverter<T> converter,
      boolean needToUnbind) {
    super(value, clazz, needToUnbind);
    this.converter = converter;
  }

  @Override
  public void setValue(Object value) {
    if (clazz.isInstance(value)) {
      this.value.set(clazz.cast(value));
    }
  }

}
