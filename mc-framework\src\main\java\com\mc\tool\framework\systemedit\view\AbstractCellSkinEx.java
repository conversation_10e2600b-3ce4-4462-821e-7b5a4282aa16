package com.mc.tool.framework.systemedit.view;

import com.mc.graph.AbstractCellSkin;
import com.mc.graph.interfaces.CellObject;
import com.mc.graph.interfaces.SkinManager;
import com.mc.tool.framework.systemedit.datamodel.VisualEditNode;
import java.util.Optional;
import javafx.event.EventHandler;
import javafx.scene.Node;
import javafx.scene.Parent;
import javafx.scene.control.Label;
import javafx.scene.control.Tooltip;
import javafx.scene.input.MouseEvent;

/**
 * .
 */
public abstract class AbstractCellSkinEx extends AbstractCellSkin {

  /**
   * Contructor.
   *
   * @param cellobject  cell object
   * @param parent      parent
   * @param container   container
   * @param skinManager skin manager
   */
  public AbstractCellSkinEx(CellObject cellobject, Parent parent, Parent container,
                            SkinManager skinManager) {
    super(cellobject, parent, container, skinManager);
  }

  protected void init() {
    Tooltip tooltip = new Tooltip();
    tooltip.textProperty().bind(getNameLabel().textProperty());
    getNameLabel().setTooltip(tooltip);

    addNameChangeBehavior(getNameLabel(), getNameLabelChangeSource());
    updateNameBinding(getNameLabel());
    getCell().getBindedObjectProperty().addListener(
        weakAdapter.wrap((change, oldVal, newVal) -> updateNameBinding(getNameLabel())));
  }

  /**
   * 获取用于显示名称的label.
   *
   * @return label
   */
  protected abstract Label getNameLabel();

  /**
   * 用于接收双击事件来改变名称的节点.
   *
   * @return 节点列表
   */
  protected abstract Node[] getNameLabelChangeSource();

  protected void updateNameBinding(Label nameLabel) {
    nameLabel.textProperty().unbind();
    if (getCell().getBindedObject() instanceof VisualEditNode) {
      VisualEditNode node = (VisualEditNode) getCell().getBindedObject();
      nameLabel.textProperty().bindBidirectional(node.nameProperty());
    }
  }

  protected void addNameChangeBehavior(Label nameLabel, Node... nodes) {
    for (Node node : nodes) {
      node.addEventHandler(MouseEvent.MOUSE_CLICKED,
          weakAdapter.wrap((EventHandler<MouseEvent>) (event) -> {
            onNameLabelMouseClicked(event, nameLabel);
          }));
    }
  }

  protected void onNameLabelMouseClicked(MouseEvent event, Label nameLabel) {
    if (event.getClickCount() == 2) {
      Optional<String> result = ViewUtility.getNameFromDialog(nameLabel.getScene().getWindow(),
          nameLabel.getText());
      result.ifPresent(nameLabel::setText);
      event.consume();
    }
  }
}
