package com.mc.tool.framework.office.controller;

import com.mc.tool.framework.interfaces.ViewControllable;
import com.mc.tool.framework.office.datamodel.OfficeData;
import com.mc.tool.framework.office.graph.OfficeGraph;
import com.mc.tool.framework.systemedit.datamodel.VisualEditModel;
import java.util.Collection;

/**
 * .
 */
public interface OfficeControllable extends ViewControllable {

  void initModel(VisualEditModel model);

  OfficeGraph getGraph();

  /**
   * 获取界面上选中的节点的集合.
   *
   * @return 选中的节点的集合.
   */
  Collection<OfficeData> getSelectedFuncs();

  /**
   * 删除视频墙或坐席数据.
   */
  void deleteSelectedFuncs();

  /**
   * 删除全部.
   */
  void deleteAll();

  /**
   * 生成测试数据.
   */
  void createTestData();

  /**
   * 在左侧TabPane面板添加Tab子项.
   *
   * @param tabName 会议室的名称，如果为null，自动生成一个名称
   */
  void addTab(String tabName);

}
