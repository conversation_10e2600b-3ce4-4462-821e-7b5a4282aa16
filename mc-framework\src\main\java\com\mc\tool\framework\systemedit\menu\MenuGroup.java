package com.mc.tool.framework.systemedit.menu;

import com.mc.graph.interfaces.CellSkin;
import com.mc.tool.framework.systemedit.controller.SystemEditControllable;
import com.mc.tool.framework.systemedit.datamodel.VisualEditFunc;
import com.mc.tool.framework.systemedit.datamodel.VisualEditGroup;
import com.mc.tool.framework.systemedit.datamodel.VisualEditMatrix;
import com.mc.tool.framework.systemedit.datamodel.VisualEditNode;
import com.mc.tool.framework.systemedit.menu.predicate.DirectionNotSamePredicate;
import com.mc.tool.framework.systemedit.menu.predicate.EmptyPrediate;
import com.mc.tool.framework.systemedit.menu.predicate.MenuPredicateBinding;
import com.mc.tool.framework.systemedit.menu.predicate.ParentNotSamePredicate;
import com.mc.tool.framework.systemedit.menu.predicate.TypePredicate;
import com.mc.tool.framework.utility.I18nUtility;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import javafx.scene.control.MenuItem;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class MenuGroup extends MenuItem {
  private SystemEditControllable controllable;

  /**
   * 创建group的菜单项.
   *
   * @param controllable 系统编辑控制器
   */
  public MenuGroup(SystemEditControllable controllable) {
    this.controllable = controllable;
    this.setText(I18nUtility.getI18nBundle("systemedit").getString("menu.group"));
    this.setOnAction((event) -> onAction());

    this.disableProperty().bind(getMenuDisableBinding(controllable));
  }

  /**
   * 获取菜单是否应该禁掉的binding.
   *
   * @param controllable controller
   * @return binding
   */
  public static MenuPredicateBinding getMenuDisableBinding(SystemEditControllable controllable) {
    MenuPredicateBinding binding = new MenuPredicateBinding(controllable, true);
    binding.addAllSelectionPredicate(new EmptyPrediate());
    binding.addAllSelectionPredicate(new ParentNotSamePredicate());
    binding.addAllSelectionPredicate(new DirectionNotSamePredicate());
    binding.addSingleSelectionPredicate(new TypePredicate(VisualEditMatrix.class));
    binding.addSingleSelectionPredicate((node) -> node.getParent() instanceof VisualEditFunc);
    return binding;
  }

  private void onAction() {
    Collection<CellSkin> skins = controllable.getGraph().getSelectionModel().getSelectedCellSkin();
    List<VisualEditNode> nodeList = new ArrayList<>();
    for (CellSkin skin : skins) {
      if (skin.getCell().getBindedObject() instanceof VisualEditNode) {
        nodeList.add((VisualEditNode) skin.getCell().getBindedObject());
      } else {
        log.warn("Cell's bindedobject is not VisualEditNode but {}",
            skin.getCell().getBindedObject());
      }
    }
    controllable.addGroup(null, VisualEditGroup.class, nodeList.toArray(new VisualEditNode[0]));
  }
}
