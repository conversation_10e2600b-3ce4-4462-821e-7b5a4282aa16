package com.mc.tool.framework.view.config;

import com.dooapp.fxform.annotation.FormFactory;
import com.mc.tool.framework.interfaces.ApplicationBase;
import com.mc.tool.framework.utility.InjectorProvider;
import java.util.Locale;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleObjectProperty;

/**
 * .
 */
public class BasicConfigBean {
  @FormFactory(LocaleFieldFactory.class)
  public ObjectProperty<Locale> currentLanguage = new SimpleObjectProperty<>(Locale.SIMPLIFIED_CHINESE);

  /**
   * Constructor.
   */
  public BasicConfigBean() {
    ApplicationBase base = InjectorProvider.getInjector().getInstance(ApplicationBase.class);
    if (base != null) {
      currentLanguage.bindBidirectional(base.getConfigBean().language);
    }
  }
}
