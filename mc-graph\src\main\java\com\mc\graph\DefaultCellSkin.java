package com.mc.graph;

import com.mc.graph.interfaces.CellBehavior;
import com.mc.graph.interfaces.CellObject;
import com.mc.graph.interfaces.Connector;
import com.mc.graph.interfaces.ConnectorSkin;
import com.mc.graph.interfaces.SkinManager;
import javafx.geometry.Point2D;
import javafx.scene.Parent;
import javafx.scene.layout.Pane;
import javafx.scene.layout.Region;
import javafx.scene.paint.Color;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

public class DefaultCellSkin extends AbstractCellSkin {

  private static final double CONNECTOR_GAP = 3;
  private List<ConnectorSkin> connectorSkins;

  protected Pane region;

  /**
   * Constructor.
   * 
   * @param cellobject cell object to be skin
   * @param parent parent to add skin's components
   * @param container TODO
   * @param skinManager skin manager
   */
  public DefaultCellSkin(CellObject cellobject, Parent parent, Parent container,
      SkinManager skinManager) {
    super(cellobject, parent, container, skinManager);
  }

  @Override
  public Region getRegion() {
    return region;
  }

  @Override
  protected void onConnectorChange() {
    if (cellObject == null) {
      return;
    }
    // 删除没有用的connectorskin，创建新的
    List<ConnectorSkin> newSkins = new ArrayList<>();
    for (Connector connector : cellObject.getConnectors()) {
      ConnectorSkin cs = null;
      for (ConnectorSkin skin : connectorSkins) {
        if (skin.getConnector() == connector) {
          cs = skin;
          break;
        }
      } // end for
      if (cs == null) {
        cs = new DefaultConnectorSkin(connector, getRegion(), container);
        skinManager.setConnectorSkin(connector, cs);
        for (CellBehavior cellBehavior : cellBehaviors) {
          cellBehavior.createConnectorBehavior(cs);
        }
        if (getRegion().getParent() != null) {
          cs.add();
        }
      }
      newSkins.add(cs);
    } // end for

    for (ConnectorSkin skin : connectorSkins) {
      if (!newSkins.contains(skin)) {
        skin.remove();
      }
    }

    connectorSkins = newSkins;


    double minHeight = (int) (connectorSkins.size() / 2.0 + 0.6)
        * (CONNECTOR_GAP + DefaultConnectorSkin.RADIUS * 2) + CONNECTOR_GAP;
    double minwidth = DefaultConnectorSkin.RADIUS * 2 + CONNECTOR_GAP;
    getRegion().setMinHeight(minHeight);
    getRegion().setMinWidth(minwidth);
    if (getRegion().prefHeight(-1) < minHeight) {
      getRegion().setPrefHeight(minHeight);
    }

    if (getRegion().prefWidth(-1) < minwidth) {
      getRegion().setPrefWidth(minwidth);
    }

    layoutConnectors();
  }

  @Override
  protected void layoutConnectors() {

    double width = getRegion().getPrefWidth();
    double height = getRegion().getPrefHeight();
    int index = 0;
    int leftCount = connectorSkins.size() / 2 + connectorSkins.size() % 2;
    int rightCount = connectorSkins.size() / 2;
    double leftTopInset =
        (height - leftCount * DefaultConnectorSkin.RADIUS * 2 - (leftCount - 1) * CONNECTOR_GAP)
            / 2;
    double rightTopInset =
        (height - rightCount * DefaultConnectorSkin.RADIUS * 2 - (rightCount - 1) * CONNECTOR_GAP)
            / 2;
    if (this.getParent() == null) {
      return;
    }
    for (ConnectorSkin skin : connectorSkins) {
      double xpos = (index % 2) * width + getRegion().getLayoutX();
      int vindex = index / 2;
      double ypos = ((index % 2) == 0 ? leftTopInset : rightTopInset)
          + vindex * (CONNECTOR_GAP + DefaultConnectorSkin.RADIUS * 2) + DefaultConnectorSkin.RADIUS
          + getRegion().getLayoutY();
      Point2D point2d = this.getParent().localToScene(xpos, ypos);
      point2d = getRegion().sceneToLocal(point2d);
      skin.getNode().setLayoutX(point2d.getX());
      skin.getNode().setLayoutY(point2d.getY());
      index++;
    }
  }

  @Override
  public Color getSelectionBorderColor() {
    return Color.RED;
  }

  @Override
  public boolean isSelectionEffectEnabled() {
    return false;
  }

  @Override
  public double getResizableBorderWidth() {
    return 10;
  }

  @Override
  public boolean isMovable() {
    return true;
  }

  @Override
  public boolean isResizeble() {
    return true;
  }

  @Override
  protected void initRegion() {
    region = new Pane();
    region.setUserData(this);
    region.setStyle("-fx-background-color:#123456");
  }

  @Override
  protected void initInner() {
    connectorSkins = new ArrayList<>();
  }

  @Override
  public Collection<ConnectorSkin> getConnectorSkins() {
    return connectorSkins;
  }
}
