package com.mc.tool.framework.operation.crossscreen.controller;

import com.mc.common.util.WeakAdapter;
import com.mc.tool.framework.systemedit.datamodel.CrossScreenFunc;
import com.mc.tool.framework.systemedit.datamodel.SystemEditDefinition;
import com.mc.tool.framework.systemedit.datamodel.VisualEditModel;
import com.mc.tool.framework.systemedit.datamodel.VisualEditNode;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import java.io.IOException;
import java.net.URL;
import java.util.ResourceBundle;
import javafx.beans.property.ObjectProperty;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.scene.canvas.Canvas;
import javafx.scene.control.Label;
import javafx.scene.image.Image;
import javafx.scene.input.ClipboardContent;
import javafx.scene.input.DragEvent;
import javafx.scene.input.Dragboard;
import javafx.scene.input.MouseEvent;
import javafx.scene.input.TransferMode;
import javafx.scene.layout.VBox;
import javafx.util.Pair;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class ScreenItem extends VBox implements Initializable {
  @Getter
  protected final int row;
  @Getter
  protected final int column;
  @Getter
  protected final ObjectProperty<VisualEditTerminal> target;

  protected final VisualEditModel model;

  protected final CrossScreenFunc func;

  protected final CrossScreenControllable controllable;
  @FXML
  protected Label screenImage;
  @FXML
  protected Label screenText;
  @FXML
  protected Canvas canvas;

  protected WeakAdapter weakAdapter = null;

  /**
   * Constructor.
   *
   * @param row          行
   * @param column       列
   * @param target       对应rx
   * @param model        数据模型
   * @param func         func
   * @param controllable controllable
   */
  public ScreenItem(int row, int column, ObjectProperty<VisualEditTerminal> target,
                    VisualEditModel model, CrossScreenFunc func, CrossScreenControllable controllable) {
    this.row = row;
    this.column = column;
    this.target = target;
    this.model = model;
    this.func = func;
    this.controllable = controllable;
    ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
    FXMLLoader loader = new FXMLLoader(
        classLoader.getResource("com/mc/tool/framework/operation/crossscreen/screenitem.fxml"));
    loader.setRoot(this);
    loader.setController(this);
    try {
      loader.load();
    } catch (IOException exception) {
      log.warn("Fail to load screenitem.fxml!", exception);
    }

    setMinSize(100, 100);
  }

  protected WeakAdapter getWeakAdapter() {
    if (weakAdapter == null) {
      weakAdapter = new WeakAdapter();
    }
    return weakAdapter;
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {

    screenImage.managedProperty().bind(screenImage.visibleProperty());
    screenText.managedProperty().bind(screenText.visibleProperty());
    screenImage.visibleProperty().bind(target.isNotNull());
    screenText.visibleProperty().bind(target.isNotNull());

    if (target.get() != null) {
      screenText.textProperty().bind(target.get().nameProperty());
    }

    target.addListener(getWeakAdapter().wrap((observable, oldValue, newValue) -> {
      if (newValue != null) {
        screenText.textProperty().bind(newValue.nameProperty());
      } else {
        screenText.textProperty().unbind();
      }
    }));

    updateImage();
    func.getCrossScreenData().getControlSource()
        .addListener(getWeakAdapter().wrap((obs, oldVal, newVal) -> updateImage()));

    this.setOnDragDetected(this::onDragDetected);
    this.setOnDragOver(this::onDragOver);
    this.setOnDragDropped(this::onDragDrop);
  }

  protected void updateImage() {
    String noControlImage = "/com/mc/tool/framework/operation/crossscreen/screen.png";
    String controlImage = "/com/mc/tool/framework/operation/crossscreen/screen_control.png";
    boolean isControl =
        func.getCrossScreenData().getControlSource().get() == target.get() && target.get() != null;
    String image = isControl ? controlImage : noControlImage;
    screenImage.setStyle(String.format("-fx-background-image:url(\"%s\")", image));
  }

  protected VisualEditTerminal getTerminal(DragEvent event) {
    if (event.getDragboard().hasString()) {
      VisualEditNode node = model.findNodeByGuid(event.getDragboard().getString());
      if (node instanceof VisualEditTerminal) {
        return (VisualEditTerminal) node;
      } else {
        return null;
      }
    } else {
      return null;
    }
  }

  protected void onDragOver(DragEvent event) {
    VisualEditTerminal terminal = getTerminal(event);
    if (terminal != null
        && (terminal.isRx() && func.isScreenMovable() || terminal.isTx() && target.get() != null)
        && controllable.isConnetable(terminal, target.get())) {
      event.acceptTransferModes(TransferMode.ANY);
    } else if (terminal == null
        && event.getDragboard().getString().equals(SystemEditDefinition.EMPTY_TERMINAL_GUID)
        && controllable.isConnetable(null, target.get())) {
      event.acceptTransferModes(TransferMode.ANY);
    }
    event.consume();
  }

  protected void onDragDrop(DragEvent event) {
    VisualEditTerminal terminal = getTerminal(event);
    if (terminal != null && terminal.isRx() && func.isScreenMovable()) {
      Pair<Integer, Integer> pos = func.posOfScreen(terminal);
      func.moveScreen(pos.getKey(), pos.getValue(), getRow(), getColumn());
    } else if (terminal != null && target.get() != null && terminal.isTx() && controllable
        .isConnetable(terminal, target.get())) {
      controllable.connectForCrossScreen(terminal, target.get());
    } else if (terminal == null && event.getDragboard().getString()
        .equals(SystemEditDefinition.EMPTY_TERMINAL_GUID)
        && controllable.isConnetable(null, target.get())) {
      controllable.connectForCrossScreen(null, target.get());
    }
    event.setDropCompleted(true);
    event.consume();
  }

  protected void onDragDetected(MouseEvent event) {
    if (target.get() != null && func.isScreenMovable()) {
      Dragboard dbDragboard = this.startDragAndDrop(TransferMode.ANY);
      ClipboardContent content = new ClipboardContent();
      content.putString(target.get().getGuid());
      Image image = new Image(Thread.currentThread().getContextClassLoader()
          .getResourceAsStream("com/mc/tool/framework/operation/crossscreen/screen.png"));
      dbDragboard.setDragView(image, image.getWidth() / 2, image.getHeight() / 2);
      dbDragboard.setContent(content);
    }
    event.consume();
  }
}
