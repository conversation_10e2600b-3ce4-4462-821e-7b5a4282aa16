package com.mc.tool.framework.systemedit.view;

import com.mc.graph.AbstractConnectorSkin;
import com.mc.graph.interfaces.Connector;
import com.mc.graph.util.LinkUtil;
import javafx.beans.binding.DoubleBinding;
import javafx.scene.Node;
import javafx.scene.Parent;
import javafx.scene.paint.Color;
import javafx.scene.shape.Rectangle;

/**
 * .
 */
public class TerminalConnectorSkin extends AbstractConnectorSkin {
  public static final int CONNECTOR_WIDTH = 4;
  public static final int CONNECTOR_HEIGHT = 7;

  protected boolean connectAtLeft = false;

  public TerminalConnectorSkin(Connector connector, Parent parent, Parent container) {
    super(connector, parent, container);
  }

  protected Rectangle node;

  public void setConnectAtLeft(boolean connectAtLeft) {
    this.connectAtLeft = connectAtLeft;
  }

  @Override
  public Node getNode() {
    return node;
  }

  @Override
  protected void initNode() {
    node = new Rectangle();
    node.setWidth(CONNECTOR_WIDTH);
    node.setHeight(CONNECTOR_HEIGHT);
    node.setFill(new Color(1 / 255.0, 209 / 255.0, 11 / 255.0, 1));
    node.setUserData(this);
    //
    Parent region = parent.getParent();
    DoubleBinding xposBinding = new DoubleBinding() {
      {
        super.bind(getNode().parentProperty(), parent.layoutXProperty(), region.layoutXProperty(),
            region.translateXProperty(), getNode().layoutXProperty(),
            getNode().translateXProperty());
      }

      @Override
      protected double computeValue() {
        return LinkUtil.localToGrandParent(parent, container, getNode().getLayoutX()
            + getNode().getTranslateX() + (connectAtLeft ? 0 : CONNECTOR_WIDTH), true);
      }
    };

    DoubleBinding yposBinding = new DoubleBinding() {
      {
        super.bind(getNode().parentProperty(), parent.layoutYProperty(), region.layoutYProperty(),
            region.translateYProperty(), getNode().layoutYProperty(),
            getNode().translateYProperty());

      }

      @Override
      protected double computeValue() {
        return LinkUtil.localToGrandParent(parent, container,
            getNode().getLayoutY() + getNode().getTranslateY() + CONNECTOR_HEIGHT / 2, false);
      }
    };

    containerXposProperty.bind(xposBinding);
    containerYposProperty.bind(yposBinding);
  }

}
