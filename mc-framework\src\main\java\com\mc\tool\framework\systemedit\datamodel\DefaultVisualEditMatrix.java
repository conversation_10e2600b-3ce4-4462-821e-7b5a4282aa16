package com.mc.tool.framework.systemedit.datamodel;

import com.google.common.collect.BiMap;
import com.google.common.collect.HashBiMap;
import com.google.gson.annotations.Expose;
import com.mc.graph.interfaces.ConnectorIdentifier;
import com.mc.tool.framework.utility.AggregatedObservableArrayList;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javafx.beans.Observable;
import javafx.beans.binding.ListBinding;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.collections.transformation.FilteredListEx;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class DefaultVisualEditMatrix extends AbstractVisualEditNode implements VisualEditMatrix {
  /**
   * 终端与矩阵的连接关系，一个终端可通过多个端口与矩阵相连。Key中第一个Object为Terminal的端口，第二个Object为Matrix的端口.
   */
  @Expose
  protected Map<VisualEditTerminal, BiMap<ConnectorIdentifier, ConnectorIdentifier>>
      terminalLinkPorts = new HashMap<>();

  /**
   * 矩阵端口与终端的对应关系.
   */
  @Expose
  protected Map<ConnectorIdentifier, VisualEditTerminal> matrixPortLinkTerminals = new HashMap<>();

  @Expose
  protected InnerVisualEditGroup txGroup = new InnerVisualEditGroup(this);
  @Expose
  protected InnerVisualEditGroup rxGroup = new InnerVisualEditGroup(this);

  protected AggregatedObservableArrayList<VisualEditTerminal> allTerminals =
      new AggregatedObservableArrayList<>("allTerminals");

  protected ObservableList<VisualEditTerminal> readOnlyAllTerminals =
      allTerminals.getAggregatedList();

  protected AggregatedObservableArrayList<VisualEditFunc> allFunctions =
      new AggregatedObservableArrayList<>("allFunctions");
  protected ObservableList<VisualEditFunc> readOnlyAllFunctions = allFunctions.getAggregatedList();

  @Expose
  protected ObservableList<ConnectorIdentifier> connectorIds = FXCollections.observableArrayList();

  protected AggregatedObservableArrayList<VisualEditNode> observableChildren =
      new AggregatedObservableArrayList<>("observableChildren");
  protected ObservableList<VisualEditNode> readonlyObservableChildren =
      observableChildren.getAggregatedList();

  protected FilteredListEx<ConnectorIdentifier> onlineFilteredConnectorIds;

  protected ObservableList<ConnectorIdentifier> onlineConnectorIds;

  /**
   * Constructor.
   */
  public DefaultVisualEditMatrix() {
  }

  protected void initConnectorIds() {
    onlineFilteredConnectorIds = new FilteredListEx<>(connectorIds, (id) -> {
      VisualEditTerminal terminal = matrixPortLinkTerminals.get(id);
      return terminal != null && terminal.isOnline();
    });

    for (VisualEditTerminal terminal : getAllTerminalChild()) {
      onlineFilteredConnectorIds.addDependencies(terminal.onlineProperty());
    }

    onlineConnectorIds = FXCollections.unmodifiableObservableList(onlineFilteredConnectorIds);
  }

  @Override
  public void recursiveInit() {
    super.recursiveInit();
    // 删除所有已经出错的项.
    List<VisualEditTerminal> terminalDeleteList = new ArrayList<>();
    Set<ConnectorIdentifier> portDeleteList = new HashSet<>();
    for (Map.Entry<VisualEditTerminal, BiMap<ConnectorIdentifier, ConnectorIdentifier>> entry :
        terminalLinkPorts.entrySet()) {
      VisualEditTerminal terminal = entry.getKey();
      if (!hasChild(terminal)) {
        terminalDeleteList.add(terminal);
        portDeleteList.addAll(entry.getValue().values());
      }
    }

    for (VisualEditTerminal item : terminalDeleteList) {
      terminalLinkPorts.remove(item);
    }

    for (ConnectorIdentifier item : portDeleteList) {
      matrixPortLinkTerminals.remove(item);
    }
  }

  @Override
  public String getNodeType() {
    return SystemEditDefinition.MATRIX_CELL;
  }

  @Override
  public int getPortCount() {
    return 16;
  }

  @Override
  public void addChildren(VisualEditNode... node) {
    addChildren(-1, node);
  }

  @Override
  public void addChildren(int index, VisualEditNode... nodes) {
    List<VisualEditNode> nodeList = Arrays.asList(nodes);
    if (index >= 0) {
      Collections.reverse(nodeList);
    }
    List<VisualEditNode> rxItems = new ArrayList<>();
    List<VisualEditNode> txItems = new ArrayList<>();
    for (VisualEditNode item : nodes) {
      if (item instanceof VisualEditGroup) {
        insertGroup(index, (VisualEditGroup) item);
      } else if (item instanceof VisualEditTerminal) {
        VisualEditTerminal terminal = (VisualEditTerminal) item;
        // 当为已添加到matrix的terminal才能add
        if (terminalLinkPorts.containsKey(terminal)) {
          if (terminal.isRx()) {
            rxItems.add(item);
          } else if (terminal.isTx()) {
            txItems.add(item);
          } else {
            log.warn("Unknown direction.");
          }
        } else {
          log.warn("The terminal is not added to matrix yet");
        }
      } else {
        log.warn("Unsupported node to add!");
      }
    }

    if (index >= 0) {
      Collections.reverse(rxItems);
      Collections.reverse(txItems);
    }

    if (rxItems.size() > 0) {
      rxGroup.addChildren(index, rxItems.toArray(new VisualEditNode[0]));
    }
    if (txItems.size() > 0) {
      txGroup.addChildren(index, txItems.toArray(new VisualEditNode[0]));
    }
  }

  @Override
  public int indexOfChild(VisualEditNode node) {
    if (node.isRx()) {
      return rxGroup.indexOfChild(node);
    } else if (node.isTx()) {
      return txGroup.indexOfChild(node);
    } else {
      if (rxGroup.getChildren().contains(node)) {
        return rxGroup.indexOfChild(node);
      } else if (txGroup.getChildren().contains(node)) {
        return txGroup.indexOfChild(node);
      } else {
        return -1;
      }
    }
  }

  @Override
  public void removeChildren(boolean recursive, VisualEditNode... node) {
    removeChildren(recursive, false, node);
  }

  @Override
  public void removeChildren(boolean recursive, boolean permanent, VisualEditNode... node) {
    List<VisualEditNode> rxItems = new ArrayList<>();
    List<VisualEditNode> txItems = new ArrayList<>();
    for (VisualEditNode item : node) {
      if (item instanceof VisualEditGroup) {
        removeGroup((VisualEditGroup) item);
      } else {
        if (permanent && item instanceof VisualEditTerminal) {
          removeTerminalPorts((VisualEditTerminal) item);
        }
        if (item.isRx()) {
          rxItems.add(item);
        } else if (item.isTx()) {
          txItems.add(item);
        }
      }
    }
    rxGroup.removeChildren(recursive, rxItems.toArray(new VisualEditNode[0]));
    txGroup.removeChildren(recursive, txItems.toArray(new VisualEditNode[0]));
  }

  @Override
  public void moveChild(VisualEditNode node, int index) {
    if (node.isRx()) {
      rxGroup.moveChild(node, index);
    } else if (node.isTx()) {
      txGroup.moveChild(node, index);
    }
  }

  @Override
  public void removeAndAdd(Collection<VisualEditNode> removeItems,
                           Collection<VisualEditNode> addItems, int addIndex) {
    if (removeItems.size() == 0) {
      log.warn("Can not remove with empty nodes!");
      return;
    }
    VisualEditNode node = removeItems.iterator().next();

    if (node.isRx()) {
      rxGroup.removeAndAdd(removeItems, addItems, addIndex);
    } else if (node.isTx()) {
      txGroup.removeAndAdd(removeItems, addItems, addIndex);
    } else if (node.getParent() == this) {
      if (rxGroup.getChildren().contains(node)) {
        rxGroup.removeAndAdd(removeItems, addItems, addIndex);
      } else if (txGroup.getChildren().contains(node)) {
        txGroup.removeAndAdd(removeItems, addItems, addIndex);
      }
    }
  }

  /**
   * 添加一个实际上有连接的终端.
   *
   * @param terminal     终端
   * @param matrixPort   矩阵的端口
   * @param terminalPort 终端的端口
   */
  @Override
  public void insertTerminal(VisualEditTerminal terminal, int matrixPort, int terminalPort) {
    if (terminal == null) {
      log.warn("The terminal is null.");
      return;
    }
    if (matrixPort > getPortCount() || matrixPort < 0) {
      log.warn("The matrix port is error. Expected in [0, {}], but is {}.", getPortCount(),
          matrixPort);
      return;
    }
    if (terminalPort > terminal.getPortCount() || terminalPort < 0) {
      log.warn("The terminal port is error. Expected in [0, {}], but is {}.",
          terminal.getPortCount(), terminalPort);
      return;
    }
    insertTerminlImpl(terminal, ConnectorIdentifier.getIdentifier(matrixPort),
        ConnectorIdentifier.getIdentifier(terminalPort));

  }

  protected void insertTerminlImpl(VisualEditTerminal terminal, ConnectorIdentifier matrixPort,
                                   ConnectorIdentifier terminalPort) {
    if (!connectorIds.contains(matrixPort)) {
      connectorIds.add(matrixPort);
    }
    // 添加对应关系
    BiMap<ConnectorIdentifier, ConnectorIdentifier> ports;
    if (terminalLinkPorts.containsKey(terminal)) {
      ports = terminalLinkPorts.get(terminal);
    } else {
      ports = HashBiMap.create();
    }
    ports.put(terminalPort, matrixPort);
    terminalLinkPorts.put(terminal, ports);

    matrixPortLinkTerminals.put(matrixPort, terminal);
    if (!hasChild(terminal)) {
      addChildren(terminal);
    }

    onlineFilteredConnectorIds.addDependencies(terminal.onlineProperty());
  }

  /**
   * 删除一个终端.
   *
   * @param terminal 要删除的终端
   */
  public void removeTerminal(VisualEditTerminal terminal) {
    removeTerminalPorts(terminal);
    // 删除节点.
    if (terminal.isRx()) {
      rxGroup.removeChildren(true, terminal);
    } else if (terminal.isTx()) {
      txGroup.removeChildren(true, terminal);
    } else {
      log.warn("Unknown node direction!");
      rxGroup.removeChildren(true, terminal);
      txGroup.removeChildren(true, terminal);
    }
    onlineFilteredConnectorIds.removeDependencies(terminal.onlineProperty());
  }

  protected void removeTerminalPorts(VisualEditTerminal terminal) {
    if (terminalLinkPorts.containsKey(terminal)) {
      connectorIds.removeAll(terminalLinkPorts.get(terminal).values());
    }
    // 删除端口的对应关系
    BiMap<ConnectorIdentifier, ConnectorIdentifier> ports = terminalLinkPorts.get(terminal);
    if (ports != null) {
      for (Object id : ports.values()) {
        matrixPortLinkTerminals.remove(id);
      }
      terminalLinkPorts.remove(terminal);
    }
  }

  @Override
  public Collection<VisualEditNode> getChildren() {
    List<VisualEditNode> nodes = new ArrayList<>();
    nodes.addAll(rxGroup.getChildren());
    nodes.addAll(txGroup.getChildren());
    return nodes;
  }

  @Override
  public ObservableList<VisualEditNode> getObservableChildren() {
    return readonlyObservableChildren;
  }

  @Override
  public Collection<VisualEditNode> getRxChildren() {
    return rxGroup.getChildren();
  }

  @Override
  public Collection<VisualEditNode> getTxChildren() {
    return txGroup.getChildren();
  }

  /**
   * 插入一个组.
   *
   * @param index 插入的位置
   * @param group 组
   */
  @Override
  public void insertGroup(int index, VisualEditGroup group) {
    if (group.isEmpty()) {
      log.warn("Can not add an empty group.");
      return;
    }
    if (group.isRx()) {
      deleteRepeatItem(group, rxGroup);
      rxGroup.addChildren(index, group);
    } else if (group.isTx()) {
      deleteRepeatItem(group, txGroup);
      txGroup.addChildren(index, group);
    } else {
      log.error("Can not check the group's tx and rx feature.");
    }
  }

  @Override
  public void removeGroup(VisualEditGroup group) {
    rxGroup.removeChildren(false, group);
    txGroup.removeChildren(false, group);
  }

  protected void deleteRepeatItem(VisualEditGroup inputGroup, VisualEditGroup children) {
    List<VisualEditNode> deleteItem = new ArrayList<>();
    for (VisualEditNode node : children.getChildren()) {
      if (inputGroup.hasChild(node)) {
        deleteItem.add(node);
      }
    }
    children.removeChildren(false, deleteItem.toArray(new VisualEditNode[0]));
  }

  @Override
  public boolean hasChild(VisualEditNode node) {
    if (node.isRx()) {
      return rxGroup.hasChild(node);
    } else if (node.isTx()) {
      return txGroup.hasChild(node);
    }
    return false;
  }

  @Override
  public ObservableList<ConnectorIdentifier> getConnectorId() {
    return connectorIds;
  }

  @Override
  public Collection<Pair<ConnectorIdentifier, ConnectorIdentifier>> getChildConnectorPair(
      VisualEditTerminal child) {
    if (!terminalLinkPorts.containsKey(child)) {
      return Collections.emptyList();
    }

    List<Pair<ConnectorIdentifier, ConnectorIdentifier>> result = new ArrayList<>();
    ObservableList<VisualEditTerminal> terminals = child.getAllTerminalChild();
    for (VisualEditNode node : terminals) {
      if (!terminalLinkPorts.containsKey(node)) {
        log.warn("Not add the terminal yet");
        continue;
      }
      for (Map.Entry<ConnectorIdentifier, ConnectorIdentifier> value : terminalLinkPorts.get(node)
          .entrySet()) {
        result.add(new Pair<>(value.getValue(), value.getKey()));
      }
    }
    return result;
  }

  @Override
  public ObservableList<VisualEditTerminal> getAllTerminalChild() {
    return readOnlyAllTerminals;
  }

  @Override
  public ObservableList<VisualEditFunc> getAllFunctions() {
    return readOnlyAllFunctions;
  }

  @Override
  public Collection<ConnectorIdentifier> getLeftConnectorIds() {
    VisualEditGroup group;
    if (SystemEditDefinition.TX_AT_LEFT) {
      group = txGroup;
    } else {
      group = rxGroup;
    }

    return getConnectorIds(group);
  }

  @Override
  public Collection<ConnectorIdentifier> getRightConnectorIds() {
    VisualEditGroup group;
    if (SystemEditDefinition.TX_AT_LEFT) {
      group = rxGroup;
    } else {
      group = txGroup;
    }

    return getConnectorIds(group);
  }

  private Collection<ConnectorIdentifier> getConnectorIds(VisualEditGroup group) {
    List<ConnectorIdentifier> result = new ArrayList<>();
    ObservableList<VisualEditTerminal> terminals = group.getAllTerminalChild();
    for (VisualEditTerminal terminal : terminals) {
      if (!terminalLinkPorts.containsKey(terminal)) {
        log.warn("Not add the terminal yet");
        continue;
      }
      result.addAll(terminalLinkPorts.get(terminal).values());
    }

    result.retainAll(new HashSet<>(getConnectorId()));
    return result;
  }

  /**
   * 获取终端与矩阵连接的矩阵端口.
   *
   * @param terminal 终端
   * @return 端口列表
   */
  public Collection<ConnectorIdentifier> getTerminalPorts(VisualEditTerminal terminal) {
    if (terminalLinkPorts.containsKey(terminal)) {
      return terminalLinkPorts.get(terminal).values();
    } else {
      return Collections.emptyList();
    }
  }

  @Override
  public VisualEditTerminal getTerminal(ConnectorIdentifier matrixPort) {
    if (matrixPortLinkTerminals.containsKey(matrixPort)) {
      return matrixPortLinkTerminals.get(matrixPort);
    } else {
      return null;
    }
  }

  @Override
  public ConnectorIdentifier getTermianlPort(ConnectorIdentifier matrixPort) {
    VisualEditTerminal terminal = getTerminal(matrixPort);
    if (terminal == null) {
      return null;
    } else {
      BiMap<ConnectorIdentifier, ConnectorIdentifier> links = terminalLinkPorts.get(terminal);
      if (links == null) {
        log.warn("The terminal link ports do not contain the terminal!");
        return null;
      } else {
        return links.inverse().get(matrixPort);
      }
    }
  }

  @Override
  public void init() {
    txGroup.init();
    rxGroup.init();
    allTerminals.appendList(txGroup.getAllTerminalChild());
    allTerminals.appendList(rxGroup.getAllTerminalChild());

    allFunctions.appendList(txGroup.getAllFunctions());
    allFunctions.appendList(rxGroup.getAllFunctions());

    observableChildren.appendList(rxGroup.getObservableChildren());
    observableChildren.appendList(txGroup.getObservableChildren());

    initConnectorIds();
    // 删除无效的设备
    List<VisualEditTerminal> terminals = new ArrayList<>(getAllTerminalChild());
    for (VisualEditTerminal terminal : terminals) {
      if (!terminal.isValid()) {
        removeTerminal(terminal);
      }
    }
  }

  @Override
  public ObservableList<VisualEditTerminal> getAllTxChildTerminal() {
    return txGroup.getAllTerminalChild();
  }

  @Override
  public ObservableList<VisualEditTerminal> getAllRxChildTerminal() {
    return rxGroup.getAllTerminalChild();
  }

  /**
   * .
   */
  public class ConnectorListBinding extends ListBinding<ConnectorIdentifier> {

    public ConnectorListBinding() {
      bind(connectorIds);
    }

    @Override
    protected ObservableList<ConnectorIdentifier> computeValue() {
      return connectorIds.filtered((id) -> {
        VisualEditTerminal terminal = matrixPortLinkTerminals.get(id);
        return terminal != null && terminal.isOnline();
      });
    }

    public void addBind(Observable... dependencies) {
      bind(dependencies);
      invalidate();
    }

    public void removeBind(Observable... dependencies) {
      unbind(dependencies);
      invalidate();
    }
  }
}
