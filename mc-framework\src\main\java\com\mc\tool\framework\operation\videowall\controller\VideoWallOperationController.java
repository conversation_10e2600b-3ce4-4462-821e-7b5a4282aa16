package com.mc.tool.framework.operation.videowall.controller;

import com.google.common.base.Charsets;
import com.google.common.eventbus.Subscribe;
import com.mc.common.lang.reflect.ParameterizedTypeImpl;
import com.mc.common.util.WeakAdapter;
import com.mc.graph.canvas.PageCanvas;
import com.mc.graph.canvas.PageCanvas.PageText;
import com.mc.graph.interfaces.CellObject;
import com.mc.graph.interfaces.CellSkin;
import com.mc.graph.util.GraphSelectionModel.SelectionMode;
import com.mc.graph.util.NodeUtil;
import com.mc.tool.framework.event.VisualEditFuncBeginUpdateEvent;
import com.mc.tool.framework.event.VisualEditFuncEndUpdateEvent;
import com.mc.tool.framework.interfaces.DeviceControllable;
import com.mc.tool.framework.operation.controller.DefaultSnapshotGetter;
import com.mc.tool.framework.operation.videowall.controller.VideoWallChecker.VideoWallError;
import com.mc.tool.framework.operation.videowall.datamodel.LayoutData;
import com.mc.tool.framework.operation.videowall.datamodel.VirtualVideoWallData;
import com.mc.tool.framework.operation.videowall.datamodel.interfaces.ScreenObject;
import com.mc.tool.framework.operation.videowall.datamodel.interfaces.VideoObject;
import com.mc.tool.framework.operation.videowall.datamodel.interfaces.VideoWallObject;
import com.mc.tool.framework.operation.videowall.graph.VideoWallGraph;
import com.mc.tool.framework.operation.videowall.menu.MenuAlignGroup;
import com.mc.tool.framework.operation.videowall.menu.MenuDelete;
import com.mc.tool.framework.operation.videowall.menu.MenuFitToScreen;
import com.mc.tool.framework.operation.videowall.menu.MenuFullScreen;
import com.mc.tool.framework.operation.videowall.menu.MenuMoveTo;
import com.mc.tool.framework.operation.videowall.menu.MenuSplitGroup;
import com.mc.tool.framework.operation.videowall.menu.MenuVideoSetting;
import com.mc.tool.framework.operation.videowall.scenario.ScenarioCell;
import com.mc.tool.framework.operation.videowall.scenario.SimpleScenarioCell;
import com.mc.tool.framework.systemedit.control.PreviewPopover;
import com.mc.tool.framework.systemedit.datamodel.SystemEditDefinition;
import com.mc.tool.framework.systemedit.datamodel.VideoWallFunc;
import com.mc.tool.framework.systemedit.datamodel.VisualEditFunc;
import com.mc.tool.framework.systemedit.datamodel.VisualEditModel;
import com.mc.tool.framework.systemedit.datamodel.VisualEditNode;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import com.mc.tool.framework.systemedit.view.ViewUtility;
import com.mc.tool.framework.utility.AggregatedObservableArrayList;
import com.mc.tool.framework.utility.EventBusProvider;
import com.mc.tool.framework.utility.I18nUtility;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.Reader;
import java.lang.reflect.Type;
import java.net.URL;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.ResourceBundle;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import javafx.application.Platform;
import javafx.beans.InvalidationListener;
import javafx.beans.binding.ListBinding;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.ReadOnlyStringWrapper;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.beans.value.ChangeListener;
import javafx.collections.FXCollections;
import javafx.collections.ListChangeListener;
import javafx.collections.ObservableList;
import javafx.event.ActionEvent;
import javafx.event.Event;
import javafx.event.EventHandler;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.geometry.Point2D;
import javafx.geometry.Point3D;
import javafx.geometry.Pos;
import javafx.geometry.Rectangle2D;
import javafx.scene.Node;
import javafx.scene.control.ContextMenu;
import javafx.scene.control.Label;
import javafx.scene.control.ListView;
import javafx.scene.control.Menu;
import javafx.scene.input.DragEvent;
import javafx.scene.input.KeyCode;
import javafx.scene.input.KeyEvent;
import javafx.scene.input.MouseButton;
import javafx.scene.input.MouseEvent;
import javafx.scene.input.TransferMode;
import javafx.scene.layout.HBox;
import javafx.scene.layout.Priority;
import javafx.scene.layout.VBox;
import javafx.scene.paint.Color;
import lombok.extern.slf4j.Slf4j;
import org.hildan.fxgson.FxGsonBuilder;

/**
 * .
 */
@Slf4j
public class VideoWallOperationController implements Initializable, VideoWallControllable {

  @FXML
  protected HBox graphBox;
  @FXML
  protected HBox scenarioListContainer;
  @FXML
  protected VBox simpleScenarioListContainer;
  @FXML
  protected ListView<VideoWallObject> scenarioList;
  @FXML
  protected ListView<VideoWallObject> simpleScenarioList;
  @FXML
  protected Label zoominBtn;
  @FXML
  protected Label zoomoutBtn;
  @FXML
  protected Label scenarioListTitle;
  @FXML
  protected Label listModeBtn;

  protected ObjectProperty<VideoWallObject> currentScenario = new SimpleObjectProperty<>();

  protected ObservableList<VirtualVideoWallData> presetVirtualVideoWallDatas =
      FXCollections.observableArrayList();

  protected ObservableList<VideoWallObject> presetActualVideoWallDatas =
      FXCollections.observableArrayList();

  protected AggregatedObservableArrayList<VideoWallObject> presetVideoWallDatas =
      new AggregatedObservableArrayList<>("presetVideoWallDatas");

  private PreviewPopover previewPopover;

  protected VideoWallGraph graph;

  protected VideoWallFunc func = null;

  protected VisualEditModel model = null;

  private AtomicInteger updateCount = new AtomicInteger(0);

  private boolean needToUpdate = false;

  private boolean updatingVideoFromGraph = false;

  protected ChangeListener<Number> videoChangeListener;

  protected ChangeListener<VisualEditTerminal> sourceChangeListener;

  protected VideoFixProcessor videoFixProcessor = new VideoFixProcessor();

  protected VideoWallChecker videoWallChecker = new VideoWallChecker();

  protected ObservableList<PageText> pageTexts = FXCollections.observableArrayList();

  protected ContextMenu menu = new ContextMenu();

  protected WeakAdapter weakAdapter = new WeakAdapter();

  protected BooleanProperty scenarioListMode = new SimpleBooleanProperty(false);

  @Override
  public void init(VisualEditModel model, VisualEditFunc currentFunction) {
    if (currentFunction instanceof VideoWallFunc) {
      func = (VideoWallFunc) currentFunction;
    } else {
      log.warn("Error function type!");
    }

    EventBusProvider.getEventBus().register(this);

    this.model = model;

    videoChangeListener = weakAdapter.wrap((observable, oldVale, newVal) -> {
      updatePresetVideoWallDatas();
      onVideoWallChange();
    });

    sourceChangeListener = weakAdapter.wrap((observable, oldVal, newVal) -> onVideoWallChange());

    func.getVideoWallObject().getScreens().addListener(
        weakAdapter.wrap((ListChangeListener<ScreenObject>) ((change) -> onVideoWallChange())));
    func.getVideoWallObject().getVideos()
        .addListener(weakAdapter.wrap((ListChangeListener<VideoObject>) change -> {
          onVideoWallChange();
          while (change.next()) {
            for (VideoObject data : change.getAddedSubList()) {
              updateVideoDataListener(data);
            }
            for (VideoObject data : change.getRemoved()) {
              removeVideoDataListener(data);
            }
          }
        }));

    for (VideoObject data : func.getVideoWallObject().getVideos()) {
      updateVideoDataListener(data);
    }

    func.getVideoWallObject().getLayoutData().getRowsProperty().addListener(videoChangeListener);
    func.getVideoWallObject().getLayoutData().getColumnsProperty().addListener(videoChangeListener);
    func.getVideoWallObject().getLayoutData().getResWidth().addListener(videoChangeListener);
    func.getVideoWallObject().getLayoutData().getResHeight().addListener(videoChangeListener);
    func.getVideoWallObject().getLayoutData().getFps().addListener(videoChangeListener);
    func.getVideoWallObject().getLayoutData().getMultiRes().addListener(
        weakAdapter.wrap((obs, oldVal, newVal) -> videoChangeListener.changed(null, null, null)));
    func.getVideoWallObject().getLayoutData().getMultiResObservable().addListener(weakAdapter
        .wrap((InvalidationListener) (obs) -> videoChangeListener.changed(null, null, null)));
    func.getVideoWallObject().getLayoutData().getCompensationScaleThreshold()
        .addListener(videoChangeListener);
    initVideoFixProcessor();
    initVideoWallChecker();

    createPresetVideoWallData();
    updatePresetVideoWallDatas();

    model.getTerminalStatusObservable()
        .addListener(weakAdapter.wrap((InvalidationListener) (change) -> updateErrorScreens()));
  }

  protected void updatePresetVideoWallDatas() {
    Collection<VideoWallObject> newPresetDatas = new ArrayList<>();
    for (VirtualVideoWallData virtual : presetVirtualVideoWallDatas) {
      VideoWallObject data = func.toVideoWallObject(virtual);
      if (data != null) {
        newPresetDatas.add(data);
      }
    }
    presetActualVideoWallDatas.setAll(newPresetDatas);
  }

  protected void createContextMenuItems(ContextMenu menu) {
    if (getSelectedVideos().size() == 0) {
      return;
    }
    menu.getItems().add(new MenuDelete(this));
    menu.getItems().add(new MenuFullScreen(this));
    Menu moveToMenu = new Menu(I18nUtility.getI18nBundle("operation").getString("menu.moveto"));
    moveToMenu.getItems().add(new MenuMoveTo(this, MenuMoveTo.MoveAction.LEFT_TOP_CORNOR));
    moveToMenu.getItems().add(new MenuMoveTo(this, MenuMoveTo.MoveAction.LEFT_BOTTOM_CORNOR));
    moveToMenu.getItems().add(new MenuMoveTo(this, MenuMoveTo.MoveAction.RIGHT_TOP_CORNOR));
    moveToMenu.getItems().add(new MenuMoveTo(this, MenuMoveTo.MoveAction.RIGHT_BOTTOM_CORNOR));
    moveToMenu.getItems().add(new MenuMoveTo(this, MenuMoveTo.MoveAction.CENTER));
    menu.getItems().add(moveToMenu);
    menu.getItems().add(new MenuAlignGroup(this));
    menu.getItems().add(new MenuSplitGroup(this));
    menu.getItems().add(new MenuFitToScreen(this));
    menu.getItems().add(new MenuVideoSetting(this));
  }

  /**
   * 初始化视频修正处理.
   */
  protected void initVideoFixProcessor() {

  }

  /**
   * 初始化错误检查.
   */
  protected void initVideoWallChecker() {

  }

  /**
   * 初始化视频墙的移动与缩放的行为.
   */
  protected void initVideoGraphBehavior() {

  }

  protected void fixVideos(VideoWallObject videoWallData) {
    videoFixProcessor.process(videoWallData);
  }

  protected Collection<ScreenObject> getErrorScreens(VideoWallObject videoWallData) {
    Collection<VideoWallError> errors = videoWallChecker.check(videoWallData);
    Set<ScreenObject> screenDatas = new HashSet<>();
    for (VideoWallError error : errors) {
      screenDatas.add(error.getScreenData());
    }
    return screenDatas;
  }

  protected void createPresetVideoWallData() {
    InputStream is = Thread.currentThread().getContextClassLoader()
        .getResourceAsStream("com/mc/tool/framework/operation/videowall/scenario/preset_wall.json");
    Reader reader = new InputStreamReader(is, Charsets.UTF_8);
    Collection<VirtualVideoWallData> virtualVideoWallDatas =
        new FxGsonBuilder().create().fromJson(reader,
            new ParameterizedTypeImpl(ArrayList.class, new Type[] {VirtualVideoWallData.class}));
    presetVirtualVideoWallDatas.addAll(virtualVideoWallDatas);
  }

  /**
   * 更新视频数据的监听.
   *
   * @param data 视频数据.
   */
  protected void updateVideoDataListener(VideoObject data) {
    data.getHeight().addListener(videoChangeListener);
    data.getWidth().addListener(videoChangeListener);
    data.getXpos().addListener(videoChangeListener);
    data.getYpos().addListener(videoChangeListener);
    data.getAlpha().addListener(videoChangeListener);
    data.getSource().addListener(sourceChangeListener);
  }

  protected void removeVideoDataListener(VideoObject data) {
    data.getHeight().removeListener(videoChangeListener);
    data.getWidth().removeListener(videoChangeListener);
    data.getXpos().removeListener(videoChangeListener);
    data.getYpos().removeListener(videoChangeListener);
    data.getAlpha().removeListener(videoChangeListener);
    data.getSource().removeListener(sourceChangeListener);
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {
    scenarioListTitle
        .setText(I18nUtility.getI18nBundle("operation").getString("scenario_list_title"));
    simpleScenarioListContainer.managedProperty()
        .bind(simpleScenarioListContainer.visibleProperty());
    simpleScenarioListContainer.visibleProperty().bind(scenarioListMode);
    listModeBtn.managedProperty().bind(listModeBtn.visibleProperty());

    initializeScenarioList();

    initializeGraph();
    //
    Platform.runLater(() -> {
      graph.getPageCanvas().fitToView();

      // 在这里添加cell避免显示异常
      for (VideoObject data : func.getVideoWallObject().getVideos()) {
        if (graph.getGraphModel().findCell(data) == null) {
          addVideoCell(data);
        }
      }
    });

    updateErrorScreens();
  }

  private void initializeScenarioList() {
    ListBinding<VideoWallObject> funcScenarios = new ListBinding<VideoWallObject>() {
      {
        bind(func.getScenarios());
      }

      @Override
      protected ObservableList<VideoWallObject> computeValue() {
        return FXCollections.observableArrayList(func.getScenarios());
      }
    };
    presetVideoWallDatas.appendList(funcScenarios);
    presetVideoWallDatas.appendList(presetActualVideoWallDatas);

    updateScenarioList();
    scenarioList.setCellFactory(view -> new ScenarioCell(this));
    simpleScenarioList.setCellFactory(view -> new SimpleScenarioCell(this));
  }

  protected void updateScenarioList() {
    // 值显示与当前的布局一样的预案
    scenarioList.setItems(presetVideoWallDatas.getAggregatedList());
    simpleScenarioList.setItems(presetVideoWallDatas.getAggregatedList());
  }

  protected void initializeGraph() {
    graph = new VideoWallGraph(new DefaultSnapshotGetter(model));
    graph.init();
    graphBox.getChildren().add(graph.getCanvas().getNode());
    HBox.setHgrow(graph.getCanvas().getNode(), Priority.ALWAYS);

    graph.getPageCanvas().getContainer().setOnDragOver((event) -> {
      boolean canDrop = getDeviceController() == null
          || getDeviceController().getUserRight().isVideoWallConnectable(getVideoWallFunction())
          || getDeviceController().getUserRight()
          .isVideoWallWindowCreateDeletable(getVideoWallFunction());
      if (event.getDragboard().hasString() && canDrop && canDropOver(event)) {
        event.acceptTransferModes(TransferMode.ANY);
      }

      event.consume();
    });

    graph.getPageCanvas().getContainer().setOnDragDropped((event) -> {
      if (isVideoNode(event)) {
        if (getDeviceController() == null
            || getDeviceController().getUserRight()
            .isVideoWallConnectable(getVideoWallFunction())) {
          onSwitchVideo(event);
        }
      } else {
        if (getDeviceController() == null
            || getDeviceController().getUserRight()
            .isVideoWallWindowCreateDeletable(getVideoWallFunction())) {
          onAddVideo(event);
        }
      }
      event.setDropCompleted(true);
      event.consume();
    });

    LayoutData layoutData = func.getVideoWallObject().getLayoutData();

    ChangeListener<Number> layoutChangeListener =
        weakAdapter.wrap((obs, oldVal, newVal) -> updateGraph());
    layoutData.getColumnsProperty().addListener(layoutChangeListener);
    layoutData.getRowsProperty().addListener(layoutChangeListener);
    layoutData.getResHeight().addListener(layoutChangeListener);
    layoutData.getResWidth().addListener(layoutChangeListener);
    layoutData.getMultiRes().addListener(weakAdapter.wrap((obs, oldVal, newVal) -> updateGraph()));
    layoutData.getMultiResObservable()
        .addListener(weakAdapter.wrap((InvalidationListener) (obs) -> updateGraph()));
    updateGraph();

    // 增删videodata时增删cell
    func.getVideoWallObject().getVideos()
        .addListener(weakAdapter.wrap((ListChangeListener<VideoObject>) change -> {
          if (updatingVideoFromGraph) {
            return;
          }
          while (change.next()) {
            if (change.wasAdded()) {
              for (VideoObject data : change.getAddedSubList()) {
                addVideoCell(data);
              }
            }

            if (change.wasRemoved()) {
              for (VideoObject data : change.getRemoved()) {
                CellObject cell = graph.getGraphModel().findCell(data);
                if (cell != null) {
                  graph.removeCells(cell);
                }
              }
            }
          }
        }));

    // 删除cell时删除videodata
    graph.getGraphModel().getObservableCells()
        .addListener(weakAdapter.wrap((ListChangeListener<CellObject>) change -> {
          beginUpdate();
          updatingVideoFromGraph = true;
          try {
            while (change.next()) {
              if (change.wasPermutated()) {
                VideoWallObject videoWallObject = func.getVideoWallObject();
                List<VideoObject> tempList = new ArrayList<>(videoWallObject.getVideos());
                for (int i = change.getFrom(); i < change.getTo(); i++) {
                  videoWallObject.setVideo(change.getPermutation(i), tempList.get(i));
                }
              }

              for (CellObject cell : change.getRemoved()) {
                if (cell.getBindedObject() != null && cell
                    .getBindedObject() instanceof VideoObject) {
                  func.getVideoWallObject().getVideos().remove((VideoObject) cell.getBindedObject());
                }
              }
            }
          } finally {
            updatingVideoFromGraph = false;
            endUpdate();
          }

        }));

    // 当鼠标释放时才算完成一个动作
    graph.getCanvas().getContainer().addEventFilter(MouseEvent.MOUSE_PRESSED,
        weakAdapter.wrap((EventHandler<MouseEvent>) (event) -> beginUpdate()));
    graph.getCanvas().getContainer().addEventFilter(MouseEvent.MOUSE_RELEASED,
        weakAdapter.wrap((EventHandler<MouseEvent>) (event) -> {
          fixVideos(func.getVideoWallObject());
          endUpdateClear();
        }));

    // 初始化右键菜单
    graph.getCanvas().getContainer().addEventFilter(MouseEvent.MOUSE_CLICKED,
        weakAdapter.wrap((EventHandler<MouseEvent>) (event) -> {
          if (event.getButton() == MouseButton.SECONDARY && event.getClickCount() == 1) {
            menu.getItems().clear();
            createContextMenuItems(menu);
            menu.show(graph.getCanvas().getContainer(), event.getScreenX(), event.getScreenY());
          } else {
            menu.hide();
          }
        }));

    // 初始化预览
    previewPopover = new PreviewPopover();
    previewPopover.setContentNode(graph.getOverviewCanvas());


    zoominBtn.disableProperty().bind(
        graph.getCanvas().getScaleProperty().greaterThanOrEqualTo(VideoWallConstants.MAX_SCALE));
    zoomoutBtn.disableProperty()
        .bind(graph.getCanvas().getScaleProperty().lessThanOrEqualTo(VideoWallConstants.MIN_SCALE));
    //
    graph.getPageRectangleSelectionController().setSelectionConsumer((rect) -> {
      // 如果跟已有的视频窗口重叠，就不生成视频窗口
      if (rect.getWidth() <= 0 || rect.getHeight() <= 0) {
        return;
      }
      for (VideoObject item : func.getVideoWallObject().getVideos()) {
        Rectangle2D itemRect = new Rectangle2D(item.getXpos().get(), item.getYpos().get(),
            item.getWidth().get(), item.getHeight().get());
        if (VideoWallUtility.getIntersection(itemRect, rect) != null) {
          return;
        }
      }
      // 添加视频窗口
      VideoObject data = func.createVideo();
      data.getXpos().set((int) rect.getMinX());
      data.getYpos().set((int) rect.getMinY());
      data.getWidth().set((int) rect.getWidth());
      data.getHeight().set((int) rect.getHeight());
      func.getVideoWallObject().addVideo(data);
    });

    // 监听文本变化
    pageTexts.addListener(weakAdapter.wrap(
        (ListChangeListener<PageText>) change -> graph.getPageCanvas().setPageTexts(pageTexts)));

    //
    graph.getSelectionModel().addSelectionListener((mode, selectionArea) -> {
      if (mode != SelectionMode.AREA) {
        return;
      }
      PageCanvas canvas = graph.getPageCanvas();
      VideoObject video = func.createVideo();
      video.getXpos().set((int) (selectionArea.getX() / canvas.getCellScaleProperty().get()));
      video.getYpos().set((int) (selectionArea.getY() / canvas.getCellScaleProperty().get()));
      video.getWidth().set((int) (selectionArea.getWidth() / canvas.getCellScaleProperty().get()));
      video.getHeight().set((int) (selectionArea.getHeight() / canvas.getCellScaleProperty().get()));
      if (video.getWidth().get() >= VideoWallConstants.MIN_WIDTH
          && video.getHeight().get() >= VideoWallConstants.MIN_HEIGHT) {
        VideoWallObject videoWallData = func.getVideoWallObject();
        videoWallData.addVideo(video);
      }
    });

    initVideoGraphBehavior();
  }

  protected void updateGraph() {
    LayoutData layoutData = func.getVideoWallObject().getLayoutData();
    graph.getPageCanvas().setAllPageAreas(layoutData.getScreenAreas());
  }

  @Override
  public void beginUpdate() {
    updateCount.incrementAndGet();
  }

  @Override
  public void endUpdate() {
    int count = updateCount.decrementAndGet();
    if (count <= 0) {
      updateCount.set(0);
      if (needToUpdate) {
        onVideoWallChange();
      }
    }
  }

  protected void endUpdateClear() {
    updateCount.decrementAndGet();
    if (updateCount.get() != 0) {
      log.warn("update count is not zero but {}", updateCount.get());
      updateCount.set(0);
    }
    if (needToUpdate) {
      onVideoWallChange();
    }
  }

  /**
   * 响应视频墙的改变.
   */
  protected void onVideoWallChange() {
    updateErrorScreens();

    if (!checkUpdate()) {
      return;
    }
  }

  protected void updateErrorScreens() {
    // 高亮有错的屏幕
    Collection<VideoWallError> errors = videoWallChecker.check(func.getVideoWallObject());
    Set<Integer> screenIndexes = new HashSet<>();
    List<PageText> texts = new ArrayList<>();
    List<Rectangle2D> highLightAreas = new ArrayList<>();
    for (VideoWallError screenData : errors) {
      int index =
          func.getVideoWallObject().getScreenIndex(screenData.getScreenData());
      if (index < 0) {
        log.warn("Fail to find screen data!");
        continue;
      }
      if (screenIndexes.contains(index)) {
        continue;
      }
      Rectangle2D area = graph.getPageCanvas().getOriginPageAreaByIndex(index);
      if (area == null) {
        log.warn("Fail to find area in video wall graph by index " + index);
        continue;
      }
      screenIndexes.add(index);
      highLightAreas.add(area);
      PageText text = new PageText(area,
          new ReadOnlyStringWrapper(screenData.getErrorReason()),
          Color.RED, Pos.CENTER);
      texts.add(text);
    }
    graph.getPageCanvas().setHighLightAreas(highLightAreas);
    pageTexts.setAll(texts);
  }

  /**
   * 是否可以更新视频墙数据.
   *
   * @return 如果可以，返回true，否则返回false.
   */
  protected boolean checkUpdate() {
    if (updateCount.get() != 0) {
      needToUpdate = true;
      return false;
    }
    needToUpdate = false;
    return true;
  }

  protected void addVideoCell(VideoObject data) {
    CellObject cellObject = graph.insertCell(data.getName().get(), "", 0, 0, 0, 0, "video", data);
    cellObject.getXProperty().bindBidirectional(data.getXpos());
    cellObject.getYProperty().bindBidirectional(data.getYpos());
    cellObject.getWidthProperty().bindBidirectional(data.getWidth());
    cellObject.getHeightProperty().bindBidirectional(data.getHeight());
  }

  protected boolean isVideoNode(DragEvent event) {
    CellSkin skin = NodeUtil.node2CellSkin(event.getPickResult().getIntersectedNode());
    if (skin == null) {
      return false;
    }
    return skin.getCell().getBindedObject() instanceof VideoObject;
  }

  protected boolean canDropOver(DragEvent event) {
    return true;
  }

  protected void onSwitchVideo(DragEvent event) {
    CellSkin skin = NodeUtil.node2CellSkin(event.getPickResult().getIntersectedNode());
    if (skin == null) {
      return;
    }
    Object bindedObject = skin.getCell().getBindedObject();
    if (!(bindedObject instanceof VideoObject)) {
      return;
    }
    VideoObject data = (VideoObject) bindedObject;
    VisualEditTerminal terminal = getDraggedVideoSource(event);

    data.getSource().set(terminal);

  }

  protected void onAddVideo(DragEvent event) {
    PageCanvas canvas = graph.getPageCanvas();

    if (event.getPickResult().getIntersectedNode() != canvas.getContainer()) {
      return;
    }
    if (!event.getDragboard().hasString()) {
      log.warn("No string store in drag board!");
    }

    VisualEditTerminal terminal = getDraggedVideoSource(event);
    Point3D graphLocation = event.getPickResult().getIntersectedPoint();

    Collection<Rectangle2D> areas =
        canvas.getIntersectedPageOriginAreas(
            new Point2D(graphLocation.getX(), graphLocation.getY()));
    if (areas.size() == 0) {
      log.warn("No grid intersected!");
      return;
    }
    Rectangle2D rect = areas.iterator().next();


    int width = (int) rect.getWidth();
    int height = (int) rect.getHeight();

    VideoObject video = func.createVideo();
    video.getSource().set(terminal);

    video.getXpos().set((int) rect.getMinX());
    video.getYpos().set((int) (rect.getMinY()));
    video.getWidth().set(width);
    video.getHeight().set(height);
    VideoWallObject videoWallData = func.getVideoWallObject();
    videoWallData.addVideo(video);
  }

  protected VisualEditTerminal getDraggedVideoSource(DragEvent event) {
    String guid = event.getDragboard().getString();
    VisualEditNode node = model.findNodeByGuid(guid);
    if (!(node instanceof VisualEditTerminal)
        && !guid.equals(SystemEditDefinition.EMPTY_TERMINAL_GUID)) {
      log.warn("The dragged node is not terminal!");
      return null;
    }
    VisualEditTerminal terminal = (VisualEditTerminal) node;
    if (terminal != null && !terminal.isTx()) {
      log.warn("The terminal is not tx!");
      return null;
    }
    return terminal;
  }

  @Override
  public void setDeviceController(DeviceControllable deviceController) {
    // No device controller.
  }

  @Override
  public void onConfig() {

  }

  @Override
  public Collection<VideoObject> getSelectedVideos() {
    List<VideoObject> videos = new ArrayList<>();
    Collection<CellSkin> skins = graph.getSelectionModel().getSelectedCellSkin();
    for (CellSkin skin : skins) {
      if (skin.getCell().getBindedObject() instanceof VideoObject) {
        videos.add((VideoObject) skin.getCell().getBindedObject());
      }
    }
    return videos;
  }

  @Override
  public VideoWallFunc getVideoWallFunction() {
    return func;
  }

  @Override
  public ObjectProperty<VideoWallObject> currentScenarioProperty() {
    return currentScenario;
  }

  @Override
  public void activeScenario(VideoWallObject scenario) {
    if (scenario == null) {
      return;
    }
    beginUpdate();
    try {
      scenario.copyTo(getVideoWallFunction().getVideoWallObject(), false);
      fixVideos(getVideoWallFunction().getVideoWallObject());
    } finally {
      endUpdate();
    }
    if (func.getScenarios().contains(scenario)) {
      currentScenario.set(scenario);
    }
  }

  @Override
  public boolean hasScenario(VideoWallObject scenario) {
    return getVideoWallFunction().getScenarios().contains(scenario);
  }

  @Override
  public void deleteScenario(VideoWallObject scenario) {
    getVideoWallFunction().getScenarios().remove(scenario);
  }

  @FXML
  protected void onZoomin(MouseEvent event) {
    double scale = graph.getCanvas().getScaleProperty().get();
    scale = scale * VideoWallConstants.SCALE_FACTOR;
    if (scale > VideoWallConstants.MAX_SCALE) {
      scale = VideoWallConstants.MAX_SCALE;
    }
    graph.getCanvas().getScaleProperty().set(scale);
  }

  @FXML
  protected void onZoomout(MouseEvent event) {
    double scale = graph.getCanvas().getScaleProperty().get();
    scale = scale / VideoWallConstants.SCALE_FACTOR;
    if (scale < VideoWallConstants.MIN_SCALE) {
      scale = VideoWallConstants.MIN_SCALE;
    }
    graph.getCanvas().getScaleProperty().set(scale);
  }

  @FXML
  protected void onRestore(MouseEvent event) {
    graph.getCanvas().getScaleProperty().set(1);
    graph.getPageCanvas().fitToView();
  }

  @FXML
  protected void onPreview(MouseEvent event) {
    if (previewPopover.isShowing()) {
      previewPopover.hide();
    } else {
      previewPopover.show((Node) event.getSource(), -10);
    }
  }

  @FXML
  protected void onScenarioToLeft(ActionEvent event) {
    Event.fireEvent(scenarioList,
        new KeyEvent(KeyEvent.KEY_PRESSED, "", "", KeyCode.PAGE_UP, false, false, false, false));
    Event.fireEvent(simpleScenarioList,
        new KeyEvent(KeyEvent.KEY_PRESSED, "", "", KeyCode.PAGE_UP, false, false, false, false));
  }

  @FXML
  protected void onScenarioToRight(ActionEvent event) {
    Event.fireEvent(scenarioList,
        new KeyEvent(KeyEvent.KEY_PRESSED, "", "", KeyCode.PAGE_DOWN, false, false, false, false));
    Event.fireEvent(simpleScenarioList,
        new KeyEvent(KeyEvent.KEY_PRESSED, "", "", KeyCode.PAGE_DOWN, false, false, false, false));
  }

  @Override
  public void splitVideo(VideoObject videoData, VideoSplitMode mode) {
    Collection<VideoObject> result;
    switch (mode) {
      case SPLIT_2_2:
        result = VideoWallUtility.splitVideo(func, videoData, 2, 2);
        break;
      case SPLIT_3_3:
        result = VideoWallUtility.splitVideo(func, videoData, 3, 3);
        break;
      case SPLIT_4_4:
        result = VideoWallUtility.splitVideo(func, videoData, 4, 4);
        break;
      case SPLIT_4_8:
        result = VideoWallUtility.splitVideo(func, videoData, 4, 8);
        break;
      case SPLIT_BY_SCREEN:
        result = VideoWallUtility.splitVideoByScreen(func, videoData);
        break;
      default:
        return;
    }

    if (result.isEmpty()) {
      return;
    }

    VideoWallObject data = func.getVideoWallObject();
    int index = data.getVideos().indexOf(videoData);
    if (index < 0) {
      log.warn("Can not find the video data!");
      return;
    }
    beginUpdate();
    try {
      data.addAll(index, result);
      data.getVideos().remove(videoData);
      fixVideos(data);
    } finally {
      endUpdate();
    }
  }

  @Override
  public void saveScenario() {
    if (!canSaveScenario()) {
      return;
    }
    if (currentScenario.get() != null && func.getScenarios().contains(currentScenario.get())) {
      // 保留当前的名字
      String name = currentScenario.get().getName().get();
      func.getVideoWallObject().copyTo(currentScenario.get(), false);
      currentScenario.get().getName().set(name);
      scenarioList.refresh();
      simpleScenarioList.refresh();
    } else {
      saveAsScenario();
    }
  }

  @Override
  public void saveAsScenario() {
    if (!canSaveScenario()) {
      return;
    }
    VideoWallFunc func = getVideoWallFunction();
    Optional<String> result = ViewUtility.getNameFromDialog(graphBox.getScene().getWindow(), "");
    if (result.isPresent()) {
      String name = result.get();
      VideoWallObject videoWallData = func.createVideoWallObject();
      func.getVideoWallObject().copyTo(videoWallData, false);
      videoWallData.getName().set(name);
      func.addScenario(videoWallData);
      currentScenario.set(videoWallData);
    }

  }

  @Override
  public boolean isConfigable() {
    return true;
  }

  @Override
  public boolean canSaveScenario() {
    return getDeviceController() == null
        || getDeviceController().getUserRight()
        .isVideoWallScenarioCreateDeletable(getVideoWallFunction());
  }

  @Override
  public DeviceControllable getDeviceController() {
    return null;
  }

  @Override
  public void setNodeSelectionMode() {
    graph.getSelectionModel().getSelectionMode().set(SelectionMode.NODE);
  }

  @Override
  public void setAreaSelectionMode() {
    graph.getSelectionModel().getSelectionMode().set(SelectionMode.AREA);
  }

  @Subscribe
  protected void onFuncBeginUpdate(VisualEditFuncBeginUpdateEvent event) {
    if (event.getFunc() == func) {
      beginUpdate();
    }
  }

  @Subscribe
  protected void onFuncEndUpdate(VisualEditFuncEndUpdateEvent event) {
    if (event.getFunc() == func) {
      endUpdate();
    }
  }

  @Override
  public void close() {
    EventBusProvider.getEventBus().unregister(this);
  }

  @Override
  public void moveToUp(VideoObject videoData) {
    CellObject cellObject = getCellByVideo(videoData);
    if (cellObject == null) {
      return;
    }
    int index = graph.getGraphModel().getObservableCells().indexOf(cellObject);
    if (index >= graph.getGraphModel().getObservableCells().size() - 1) {
      return;
    }
    Rectangle2D rectMe =
        new Rectangle2D(cellObject.getXProperty().get(), cellObject.getYProperty().get(),
            cellObject.getWidthProperty().get(), cellObject.getHeightProperty().get());
    beginUpdate();
    try {
      //查找需要移动到的索引
      //上面第一个与其有重叠的cell的索引为要移动到的索引
      int targetIndex = index + 1;
      for (; targetIndex < graph.getGraphModel().getObservableCells().size(); targetIndex++) {
        CellObject otherCell = graph.getGraphModel().getObservableCells().get(targetIndex);
        Rectangle2D rectOther =
            new Rectangle2D(otherCell.getXProperty().get(), otherCell.getYProperty().get(),
                otherCell.getWidthProperty().get(), otherCell.getHeightProperty().get());
        if (rectMe.intersects(rectOther)) {
          break;
        }
      }
      if (targetIndex >= graph.getGraphModel().getObservableCells().size()) {
        targetIndex--;
      }
      graph.orderCell(cellObject, targetIndex);
    } finally {
      endUpdate();
    }
  }

  @Override
  public void moveToDown(VideoObject videoData) {
    CellObject cellObject = getCellByVideo(videoData);
    if (cellObject == null) {
      return;
    }
    int index = graph.getGraphModel().getObservableCells().indexOf(cellObject);
    if (index == 0) {
      return;
    }
    Rectangle2D rectMe =
        new Rectangle2D(cellObject.getXProperty().get(), cellObject.getYProperty().get(),
            cellObject.getWidthProperty().get(), cellObject.getHeightProperty().get());
    beginUpdate();
    try {
      //查找需要移动到的索引
      //下面第一个与其有重叠的cell的索引为要移动到的索引
      int target = index - 1;
      for (; target >= 0; target--) {
        CellObject otherCell = graph.getGraphModel().getObservableCells().get(target);
        Rectangle2D rectOther =
            new Rectangle2D(otherCell.getXProperty().get(), otherCell.getYProperty().get(),
                otherCell.getWidthProperty().get(), otherCell.getHeightProperty().get());
        if (rectMe.intersects(rectOther)) {
          break;
        }
      }
      if (target < 0) {
        target = 0;
      }
      graph.orderCell(cellObject, target);
    } finally {
      endUpdate();
    }
  }

  @Override
  public void moveToTop(VideoObject videoData) {
    CellObject cellObject = getCellByVideo(videoData);
    if (cellObject == null) {
      return;
    }

    beginUpdate();
    try {
      graph.orderCell(cellObject, graph.getGraphModel().getObservableCells().size() - 1);
    } finally {
      endUpdate();
    }
  }

  @Override
  public void moveToBottom(VideoObject videoData) {
    CellObject cellObject = getCellByVideo(videoData);
    if (cellObject == null) {
      return;
    }

    beginUpdate();
    try {
      graph.orderCell(cellObject, 0);
    } finally {
      endUpdate();
    }
  }

  protected CellObject getCellByVideo(VideoObject videoData) {
    if (videoData == null) {
      return null;
    }

    for (CellObject cell : graph.getGraphModel().getObservableCells()) {
      if (cell.getBindedObject() == videoData) {
        return cell;
      }
    }
    return null;
  }


  @FXML
  protected void onChangeMode() {
    scenarioListMode.set(!scenarioListMode.get());
  }

  @FXML
  protected void onCloseSimple() {
    scenarioListMode.set(false);
  }
}
