package com.dooapp.fxform.controller;

import static com.dooapp.fxform.utils.ObjectUtils.areSame;

import com.dooapp.fxform.AbstractFXForm;
import com.dooapp.fxform.adapter.Adapter;
import com.dooapp.fxform.adapter.AdapterException;
import com.dooapp.fxform.adapter.AnnotationAdapterProvider;
import com.dooapp.fxform.model.Element;
import com.dooapp.fxform.model.PropertyElement;
import com.dooapp.fxform.validation.PropertyElementValidator;
import com.dooapp.fxform.view.FXFormNode;
import javafx.application.Platform;
import javafx.beans.value.ChangeListener;
import javafx.beans.value.ObservableValue;

import java.util.concurrent.atomic.AtomicBoolean;
import java.util.logging.Level;
import java.util.logging.Logger;

public class PropertyEditorController extends NodeController {
  
  /**
   * The logger.
   */
  private static final Logger logger = Logger.getLogger(PropertyEditorController.class.getName());

  private PropertyElementValidator propertyElementValidator = null;

  private ChangeListener<?> viewChangeListener;
  private ChangeListener<?> modelChangeListener;

  private final AnnotationAdapterProvider annotationAdapterProvider =
      new AnnotationAdapterProvider();

  private AtomicBoolean lock = new AtomicBoolean();

  /**
   * Constructor.
   */
  public PropertyEditorController(AbstractFXForm fxForm, Element<?> element) {
    super(fxForm, element);
    if (element instanceof PropertyElement<?>) {
      propertyElementValidator = new PropertyElementValidator((PropertyElement) element);
      propertyElementValidator.validatorProperty().bind(fxForm.fxFormValidatorProperty());
    }
    

  }

  @Override
  protected void bind(final FXFormNode fxFormNode) {
    viewChangeListener = new ChangeListener() {
      public void changed(ObservableValue observableValue, Object o0, Object o1) {
        if (lock.getAndSet(true)) {
          return;
        }
        try {
          Adapter adapter = annotationAdapterProvider.getAdapter(getElement().getType(),
              getNode().getProperty().getClass(), getElement(), getNode());
          if (adapter == null) {
            adapter = getFxForm().getAdapterProvider().getAdapter(getElement().getType(),
                getNode().getProperty().getClass(), getElement(), getNode());
          }
          Object newValue = propertyElementValidator.adapt(o1, adapter);
          // validate this new value
          propertyElementValidator.validate(newValue);
          // check whether the value represented in the view is different from the current model
          // value
          if (!areSame(getElement().getValue(), newValue) && !propertyElementValidator.isInvalid()
              && !((PropertyElement) getElement()).isBound()) {
            // and update the model if no constraint prevent from it
            ((PropertyElement) getElement()).setValue(newValue);
            // and perform a class level validation
            getFxForm().getClassLevelValidator().validate();
          }
        } catch (AdapterException ex) {
          logger.log(Level.FINE, ex.getMessage(), ex);
        } finally {
          lock.set(false);
        }
      }
    };
    fxFormNode.getProperty().addListener(viewChangeListener);
    modelChangeListener = new ChangeListener() {
      public void changed(ObservableValue observableValue, Object o0, Object o1) {
        if (Platform.isFxApplicationThread()) {
          updateView(o1, fxFormNode);
        } else {
          Platform.runLater(() -> updateView(o1, fxFormNode));
        }
        // The element value was updated, so request a class level check again
        propertyElementValidator.validate(o1);
        getFxForm().getClassLevelValidator().validate();
      }
    };
    getElement().addListener(modelChangeListener);
    updateView(getElement().getValue(), getNode());
  }
  
  public void updateView() {
    updateView(getElement().getValue(), getNode());
    propertyElementValidator.validate(getElement().getValue());
  }

  private void updateView(Object o1, FXFormNode fxFormNode) {
    try {
      Adapter adapter = annotationAdapterProvider.getAdapter(getElement().getType(),
          getNode().getProperty().getClass(), getElement(), getNode());
      if (adapter == null) {
        adapter = getFxForm().getAdapterProvider().getAdapter(getElement().getType(),
            getNode().getProperty().getClass(), getElement(), getNode());
      }
      Object currentViewValue = null;
      try {
        currentViewValue = adapter.adaptFrom(fxFormNode.getProperty().getValue());
      } catch (NumberFormatException ex) {
        logger.log(Level.FINE, ex.getMessage(), ex);
      }
      
      // Make sure that the value represented by the view differ from the new model value
      if (!areSame(o1, currentViewValue)) {
        Object newValue = adapter.adaptTo(o1);
        // make sure not to update the view if the current controller has been disposed
        // between the trigger and the execution time of this Platform.runLater.
        if (!isDisposed()) {
          // make sure that this view update won't trigger a model update
          lock.set(true);
          fxFormNode.getProperty().setValue(newValue);
          lock.set(false);
        }
      }
      if (!fxFormNode.getNode().disableProperty().isBound()) {
        fxFormNode.getNode().setDisable(((PropertyElement) getElement()).isBound());
      }
    } catch (AdapterException ex) {
      // The model value can not be adapted to the view
      logger.log(Level.FINE, ex.getMessage(), ex);
    }
  }

  public PropertyElementValidator getPropertyElementValidator() {
    return propertyElementValidator;
  }

  @Override
  protected void unbind(FXFormNode fxFormNode) {
    fxFormNode.getProperty().removeListener(viewChangeListener);
    getElement().removeListener(modelChangeListener);
  }
}
