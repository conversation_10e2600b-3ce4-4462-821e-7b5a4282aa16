package com.mc.common.io;

import static org.junit.Assert.*;
import java.io.File;
import java.io.IOException;
import org.junit.Test;
import com.mc.common.io.AtomicFileWriter;

public class AtomicFileWriterTest {

  @Test
  public void test() {
    try {
      AtomicFileWriter writer = new AtomicFileWriter(new File("./testatomic.txt"));
      writer.write("hello world");
      writer.commit();
    } catch (IOException e) {
      fail("expect no exception");
    }
  }

}
