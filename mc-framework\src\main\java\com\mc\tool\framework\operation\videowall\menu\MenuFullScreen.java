package com.mc.tool.framework.operation.videowall.menu;

import com.mc.tool.framework.operation.videowall.controller.VideoWallControllable;
import com.mc.tool.framework.operation.videowall.datamodel.LayoutData;
import com.mc.tool.framework.operation.videowall.datamodel.interfaces.VideoObject;
import com.mc.tool.framework.utility.I18nUtility;
import java.util.Collection;

/**
 * .
 */
public class MenuFullScreen extends VideoWallMenuBase {

  public MenuFullScreen(VideoWallControllable controllable) {
    super(controllable);
  }

  @Override
  protected void onAction() {
    Collection<VideoObject> videos = controllable.getSelectedVideos();
    controllable.beginUpdate();
    try {
      LayoutData layoutData = controllable.getVideoWallFunction()
          .getVideoWallObject().getLayoutData();
      int width = layoutData.getTotalWidth();
      int height = layoutData.getTotalHeight();
      for (VideoObject videoData : videos) {
        videoData.getXpos().set(0);
        videoData.getYpos().set(0);

        videoData.getWidth().set(width);
        videoData.getHeight().set(height);
      }
    } finally {
      controllable.endUpdate();
    }

  }

  @Override
  protected String getMenuText() {
    return I18nUtility.getI18nBundle("operation").getString("menu.full_screen");
  }

}
