package com.mc.tool.framework.office.menu.predicate;

import com.mc.tool.framework.systemedit.datamodel.VisualEditNode;
import java.util.function.Predicate;

/**
 * .
 */
public class TypePredicate implements Predicate<VisualEditNode> {
  private final Class<?> clazz;

  public TypePredicate(Class<?> clazz) {
    this.clazz = clazz;
  }

  @Override
  public boolean test(VisualEditNode node) {
    return clazz.isInstance(node);
  }

}
