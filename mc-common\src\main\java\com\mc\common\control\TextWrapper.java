package com.mc.common.control;

import static javafx.scene.control.OverrunStyle.CLIP;
import com.sun.javafx.scene.control.skin.SkinUtils;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.StringProperty;
import javafx.geometry.HPos;
import javafx.geometry.Pos;
import javafx.geometry.VPos;
import javafx.scene.control.Control;
import javafx.scene.control.OverrunStyle;
import javafx.scene.paint.Paint;
import javafx.scene.shape.Rectangle;
import javafx.scene.text.Font;
import javafx.scene.text.Text;

public class TextWrapper extends Control {
  private static final String FONT_FAMILY = "Microsoft YaHei";
  private static final int FONT_MAX_SIZE = 12;
  private static final Font labelFont = new Font(FONT_FAMILY, FONT_MAX_SIZE);
  private StringProperty textProperty = new SimpleStringProperty();
  
  private ObjectProperty<Pos> alignmentProperty = new SimpleObjectProperty<>(Pos.CENTER);

  private Text textShape = new Text();

  private Rectangle textClip;
  private double wrapWidth;
  private double wrapHeight;

  /**
   * The cached full width of the non-truncated text. We only want to recompute this if the text has
   * itself changed, or if the font has changed. This is package private ONLY FOR THE SAKE OF
   * TESTING
   */
  double textWidth = Double.NEGATIVE_INFINITY;

  /**
   * The cached width of the ellipsis string. This will be recomputed if the font or the
   * ellipsisString property have changed. This is package private ONLY FOR THE SAKE OF TESTING
   */
  double ellipsisWidth = Double.NEGATIVE_INFINITY;


  /**
   * Indicates that the text content is invalid and needs to be updated. This is package private
   * only for the sake of testing.
   */
  boolean invalidText = true;

  /**
   * 是否自动缩小字体，以适配大小，显示全部字符
   */
  boolean autoScale = false;

  public StringProperty textProperty() {
    return textProperty;
  }
  
  public ObjectProperty<Paint> fillProperty() {
    return textShape.fillProperty();
  }

  public TextWrapper() {
    this(false);
  }

  public TextWrapper(boolean autoScale) {
    textShape.setFont(labelFont);
    getChildren().add(textShape);
    textProperty.addListener((obs, oldVal, newVal) -> {
      invalidText = true;
      requestLayout();
    });
    alignmentProperty.addListener((obs, oldVal, newVal) -> {
      requestLayout();
    });
    this.autoScale = autoScale;
  }
  
  public String getText() {
    return textProperty.get();
  }
  
  public void setText(String text) {
    textProperty.set(text);
  }
  
  public void setAlignment(Pos alignment) {
    alignmentProperty.set(alignment);
  }
  
  @Override
  protected void layoutChildren() {
    layoutLabelInArea(0, 0, getWidth(), getHeight(), alignmentProperty.get());
  }

  private void updateDisplayedText(double w, double h) {
    if (invalidText) {
      String s = textProperty.get();

      String result;

      double availableWidth = getWidth() - snappedLeftInset() - snappedRightInset();
      availableWidth = Math.max(availableWidth, 0);

      if (w == -1) {
        w = availableWidth;
      }
      double minW = Math.min(computeMinLabeledPartWidth(-1, snappedTopInset(), snappedRightInset(),
          snappedBottomInset(), snappedLeftInset()), availableWidth);
      wrapWidth = Math.max(minW, w);

      double availableHeight = getHeight() - snappedTopInset() - snappedBottomInset();
      availableHeight = Math.max(availableHeight, 0);

      if (h == -1) {
        h = availableHeight;
      }
      double minH = Math.min(computeMinLabeledPartHeight(wrapWidth, snappedTopInset(),
          snappedRightInset(), snappedBottomInset(), snappedLeftInset()), availableHeight);
      wrapHeight = Math.max(minH, h);

      updateWrappingWidth();

      Font font = autoScale ? computeScaledFont(FONT_FAMILY, FONT_MAX_SIZE, s, wrapWidth)
          : labelFont;
      if (font != textShape.getFont()) {
        textShape.setFont(font);
      }
      String ellipsisString = "...";

      result =
          SkinUtils.computeClippedText(font, s, wrapWidth, OverrunStyle.ELLIPSIS, ellipsisString);

      if (result != null && result.endsWith("\n")) {
        // Strip ending newline so we don't display another row.
        result = result.substring(0, result.length() - 1);
      }

      textShape.setText(result);
      updateWrappingWidth();
      invalidText = false;
    }
  }

  protected static Font computeScaledFont(String fontFamily, int maxFontSize, String text,
      double width) {
    Font font = null;
    for (int i = maxFontSize; i >= 1; i--) {
      font = new Font(fontFamily, i);
      double stringWidth = SkinUtils.computeTextWidth(font, text, 0);
      if (stringWidth - width < 0.0010F) {
        return font;
      }
    }
    return font;
  }

  protected void layoutLabelInArea(double x, double y, double w, double h, Pos alignment) {
    final HPos hpos = alignment == null ? HPos.LEFT : alignment.getHpos();
    final VPos vpos = alignment == null ? VPos.CENTER : alignment.getVpos();

    double textWidth;
    double textHeight;

    updateDisplayedText(w, h); // Have to do this just in case it needs to be recomputed
    textWidth = snapSize(Math.min(textShape.getLayoutBounds().getWidth(), wrapWidth));
    textHeight = snapSize(Math.min(textShape.getLayoutBounds().getHeight(), wrapHeight));

    final double gap = 0;

    // Figure out the contentWidth and contentHeight. This is the width
    // and height of the Labeled and Graphic together, not the available
    // content area (which would be a different calculation).
    double contentWidth = textWidth;
    double contentHeight = textHeight;

    // Now we want to compute the x/y location to place the content at.

    // Compute the contentX position based on hpos and the space available
    double contentX;
    if (hpos == HPos.LEFT) {
      contentX = x;
    } else if (hpos == HPos.RIGHT) {
      contentX = x + (w - contentWidth);
    } else {
      // TODO Baseline may not be handled correctly
      // may have been CENTER or null, treat as center
      contentX = (x + ((w - contentWidth) / 2.0));
    }

    // Compute the contentY position based on vpos and the space available
    double contentY;
    if (vpos == VPos.TOP) {
      contentY = y;
    } else if (vpos == VPos.BOTTOM) {
      contentY = (y + (h - contentHeight));
    } else {
      // TODO Baseline may not be handled correctly
      // may have been CENTER, BASELINE, or null, treat as center
      contentY = (y + ((h - contentHeight) / 2.0));
    }


    // Now to position the graphic and text. At this point I know the
    // contentX and contentY locations (including the padding and whatnot
    // that was defined on the Labeled). I also know the content width and
    // height. So now I just need to lay out the graphic and text within
    // that content x/y/w/h area.
    if (!textShape.isManaged()) {
      textShape.setManaged(true);
    }

    // There is both text and a graphic, so I need to position them
    // relative to each other
    double textX = 0;
    double textY = 0;

    textX = contentX + gap;
    textY = contentY + ((contentHeight - textHeight) / 2.0);
    textShape.relocate(snapPosition(textX), snapPosition(textY));

    /**
     * check if the label text overflows it's bounds. If there's an overflow, and no text clip then
     * we'll clip it. If there is no overflow, and the label text has a clip, then remove it.
     */
    if ((textShape.getLayoutBounds().getHeight() > wrapHeight)
        || (textShape.getLayoutBounds().getWidth() > wrapWidth)) {

      if (textClip == null) {
        textClip = new Rectangle();
      }

      textClip.setX(textShape.getLayoutBounds().getMinX());
      textClip.setY(textShape.getLayoutBounds().getMinY());
      textClip.setWidth(wrapWidth);
      textClip.setHeight(wrapHeight);
      if (textShape.getClip() == null) {
        textShape.setClip(textClip);
      }
    } else {
      /**
       * content fits inside bounds, no need for a clip
       */
      if (textShape.getClip() != null) {
        textShape.setClip(null);
      }
    }

  }

  private double computeMinLabeledPartWidth(double height, double topInset, double rightInset,
      double bottomInset, double leftInset) {
    // First compute the minTextWidth by checking the width of the string
    // made by the ellipsis "...", and then by checking the width of the
    // string made up by labeled.text. We want the smaller of the two.
    double minTextWidth = 0;

    final Font font = textShape.getFont();
    OverrunStyle truncationStyle = OverrunStyle.ELLIPSIS;
    String ellipsisString = "...";
    final String string = textProperty.get();
    final boolean emptyText = string == null || string.isEmpty();

    if (!emptyText) {
      // We only want to recompute the full text width if the font or text changed
      if (truncationStyle == CLIP) {
        if (textWidth == Double.NEGATIVE_INFINITY) {
          // Show at minimum the first character
          textWidth = SkinUtils.computeTextWidth(font, string.substring(0, 1), 0);
        }
        minTextWidth = textWidth;
      } else {
        if (textWidth == Double.NEGATIVE_INFINITY) {
          textWidth = SkinUtils.computeTextWidth(font, string, 0);
        }
        // We only want to recompute the ellipsis width if the font has changed
        if (ellipsisWidth == Double.NEGATIVE_INFINITY) {
          ellipsisWidth = SkinUtils.computeTextWidth(font, ellipsisString, 0);
        }
        minTextWidth = Math.min(textWidth, ellipsisWidth);
      }
    }

    // Now inspect the graphic and the hpos to determine the the minWidth
    double width;
    width = minTextWidth;

    return width + leftInset + rightInset;
  }

  private double computeMinLabeledPartHeight(double width, double topInset, double rightInset,
      double bottomInset, double leftInset) {
    final Font font = textShape.getFont();

    String str = textProperty.get();
    if (str != null && str.length() > 0) {
      int newlineIndex = str.indexOf('\n');
      if (newlineIndex >= 0) {
        str = str.substring(0, newlineIndex);
      }
    }

    double s = 0;
    final double textHeight =
        SkinUtils.computeTextHeight(font, str, 0, s, textShape.getBoundsType());

    double h = textHeight;

    return topInset + h + bottomInset;
  }

  /**
   * Updates the wrapping width of the text node. Although changing the font does affect the metrics
   * used for text layout, this method does not call requestLayout or invalidate the text, since it
   * may be called from the constructor and such work would be duplicative and wasted.
   */
  private void updateWrappingWidth() {
    textShape.setWrappingWidth(0);
  }

}
