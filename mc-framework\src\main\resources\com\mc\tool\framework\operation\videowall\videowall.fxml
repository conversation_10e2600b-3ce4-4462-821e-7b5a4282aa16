<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.ListView?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.Region?>
<?import javafx.scene.layout.StackPane?>
<?import javafx.scene.layout.VBox?>
<fx:root stylesheets="@videowall.css" type="javafx.scene.layout.VBox" xmlns="http://javafx.com/javafx/8"
         xmlns:fx="http://javafx.com/fxml/1">
  <HBox VBox.vgrow="ALWAYS">
    <VBox HBox.hgrow="ALWAYS">
      <HBox fx:id="graphBox" VBox.vgrow="ALWAYS"/>
      <HBox id="graph-tool-bar" alignment="CENTER" minHeight="36" prefHeight="36">
        <HBox id="graph-tool-bar" alignment="CENTER" HBox.hgrow="ALWAYS">
          <Label id="preview-btn" onMouseClicked="#onPreview" prefHeight="18" prefWidth="18"/>
          <Label id="zoomin-btn" fx:id="zoominBtn" onMouseClicked="#onZoomin" prefHeight="18" prefWidth="18"/>
          <Label id="zoomout-btn" fx:id="zoomoutBtn" onMouseClicked="#onZoomout" prefHeight="18" prefWidth="18"/>
          <Label id="restore-btn" onMouseClicked="#onRestore" prefHeight="18" prefWidth="18"/>
        </HBox>
        <Label fx:id="listModeBtn" id="list-mode-btn" onMouseClicked="#onChangeMode" prefHeight="18" prefWidth="18"
               visible="false"/>
      </HBox>
      <Region minHeight="1" styleClass="seperator"/>
      <HBox fx:id="scenarioListContainer" minHeight="113" prefHeight="113" styleClass="scenario-list-container">
        <StackPane minWidth="44" prefWidth="44">
          <Button onAction="#onScenarioToLeft" styleClass="image-button, scenario-left-btn"/>
        </StackPane>
        <ListView fx:id="scenarioList" orientation="HORIZONTAL" styleClass="scenario-list" HBox.hgrow="ALWAYS"/>
        <StackPane minWidth="44" prefWidth="44">
          <Button onAction="#onScenarioToRight" styleClass="image-button, scenario-right-btn"/>
        </StackPane>
      </HBox>
    </VBox>
    <VBox fx:id="simpleScenarioListContainer" id="scenario-list-box">
      <HBox id="scenario-list-title-box" alignment="CENTER_LEFT" prefHeight="28.0">
        <Label fx:id="scenarioListTitle"/>
        <Region HBox.hgrow="ALWAYS"/>
        <Label id="close-btn" onMouseClicked="#onCloseSimple" prefHeight="15" prefWidth="15"/>
      </HBox>
      <ListView fx:id="simpleScenarioList" minWidth="140" prefWidth="140.0" VBox.vgrow="ALWAYS"
                id="simple-scenario-list"/>
    </VBox>
  </HBox>
</fx:root>
