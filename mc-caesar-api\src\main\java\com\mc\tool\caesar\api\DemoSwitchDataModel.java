package com.mc.tool.caesar.api;

import com.google.gson.Gson;
import com.google.gson.JsonIOException;
import com.google.gson.JsonSyntaxException;
import com.mc.common.io.ResourceFile;
import com.mc.common.lang.reflect.ParameterizedTypeImpl;
import com.mc.tool.caesar.api.datamodel.ConfigData.IoMode;
import com.mc.tool.caesar.api.datamodel.ConsoleData;
import com.mc.tool.caesar.api.datamodel.CpuData;
import com.mc.tool.caesar.api.datamodel.ExtenderData;
import com.mc.tool.caesar.api.datamodel.ExtenderNetworkInfo;
import com.mc.tool.caesar.api.datamodel.ExtenderStatusInfo;
import com.mc.tool.caesar.api.datamodel.FunctionKeyData;
import com.mc.tool.caesar.api.datamodel.LoginResponse;
import com.mc.tool.caesar.api.datamodel.MatrixData;
import com.mc.tool.caesar.api.datamodel.ModuleData;
import com.mc.tool.caesar.api.datamodel.MultiScreenData;
import com.mc.tool.caesar.api.datamodel.MultiviewLayoutType;
import com.mc.tool.caesar.api.datamodel.NetworkData;
import com.mc.tool.caesar.api.datamodel.PortData;
import com.mc.tool.caesar.api.datamodel.ScenarioData;
import com.mc.tool.caesar.api.datamodel.SystemTimeData;
import com.mc.tool.caesar.api.datamodel.TxRxGroupData;
import com.mc.tool.caesar.api.datamodel.UserData;
import com.mc.tool.caesar.api.datamodel.UserGroupData;
import com.mc.tool.caesar.api.datamodel.VersionSet;
import com.mc.tool.caesar.api.datamodel.VideoWallGroupData;
import com.mc.tool.caesar.api.datamodel.extargs.ExtArgBean;
import com.mc.tool.caesar.api.datamodel.extargs.ExtenderAnalogAudioInput;
import com.mc.tool.caesar.api.datamodel.vp.Vp6OutputData;
import com.mc.tool.caesar.api.datamodel.vp.VpConConfigData;
import com.mc.tool.caesar.api.datamodel.vp.VpConsoleData;
import com.mc.tool.caesar.api.datamodel.vp.VpConsoleData.VpResolution;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.exception.DeviceConnectionException;
import com.mc.tool.caesar.api.interfaces.Threshold;
import com.mc.tool.caesar.api.utils.CfgReader;
import java.io.BufferedInputStream;
import java.io.ByteArrayInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import javafx.application.Platform;
import javafx.util.Pair;

/**
 * .
 */
public class DemoSwitchDataModel extends CaesarSwitchDataModel {

  private static final Logger LOG = Logger.getLogger(DemoSwitchDataModel.class.getName());
  private Map<String, CaesarConfigDataModel> ftpConfigurations = new HashMap<>();

  public DemoSwitchDataModel() {
    connected.set(true);
    initDefaults();
  }

  public DemoSwitchDataModel(boolean onlyConfig) {
    super(onlyConfig);
    connected.set(true);
  }

  @Override
  protected CaesarSwitchModuleData createSwitchModuleData() {
    return new DemoSwitchModuleData(getChangeSupport(), this);
  }

  @Override
  public boolean isDemo() {
    return true;
  }

  @Override
  public CaesarController getController() {
    return new DemoController(this);
  }

  @Override
  protected VpDataModel createVpDataModel() {
    return new DemoVpDataModel(getChangeSupport(), this, lock, () -> saveRequired = true);
  }

  /**
   * 读取状态文件.
   *
   * @param file 状态文件.
   * @return 如果读取成功，返回true.
   */
  public boolean readStatus(ResourceFile file) {
    try {
      setConnection("***************:5555");
    } catch (ConfigException ex) {
      LOG.log(Level.SEVERE, null, ex);
    }

    InputStream fis = null;
    ZipInputStream zis;
    try {
      fis = file.createInputStream();
      zis = new ZipInputStream(new BufferedInputStream(fis));

      ZipEntry entry;
      while ((entry = zis.getNextEntry()) != null) {
        if (entry.getName().equals(CaesarConstants.STATUS_CONFIG_NAME)) {
          CfgReader reader = new CfgReader(zis);
          getConfigData().readData(reader, IoMode.Available);
        } else if (entry.getName().equals(CaesarConstants.STATUS_MODULE_NAME)) {
          CfgReader reader = new CfgReader(zis);
          for (ModuleData data : getSwitchModuleData().getModuleDatas()) {
            data.readData(reader);
          }
        } else if (entry.getName().equals(CaesarConstants.STATUS_PORT_NAME)) {
          CfgReader reader = new CfgReader(zis);
          for (PortData data : getConfigData().getPortDatas()) {
            data.readData(reader);
          }
        } else if (entry.getName().equals(CaesarConstants.STATUS_VP_NAME)) {
          getVpDataModel().getVideoWallGroupData().read(zis);
        } else if (entry.getName().equals(CaesarConstants.STATUS_VP_SCENARIO_NAME)) {
          CfgReader reader = new CfgReader(zis);
          boolean stop = false;
          while (!stop) {
            try {
              ScenarioData data = new ScenarioData();
              int index = reader.read2ByteValue();
              data.readData(reader, this);
              data.setIndex(index);
              getVpDataModel().sendScenarioData(data);
            } catch (ConfigException | IOException | RuntimeException exception) {
              stop = true;
            }
          }
        } else if (entry.getName().equals(CaesarConstants.STATUS_VP_RES_NAME)) {
          InputStreamReader reader = new InputStreamReader(zis, StandardCharsets.UTF_8);
          VpResolutions resolutions = new Gson().fromJson(reader, VpResolutions.class);
          for (VpConsoleData vpConsoleData : getConfigDataManager().getActiveVpconsolses()) {
            Pair<Integer, Integer>[] pairs = resolutions.get(vpConsoleData.getId());
            if (pairs == null || pairs.length != vpConsoleData.getOutPortCount()) {
              continue;
            }

            for (int i = 0; i < vpConsoleData.getOutPortCount(); i++) {
              Pair<Integer, Integer> pair = pairs[i];
              if (pair != null) {
                VpResolution vpResolution = new VpResolution();
                vpResolution.setWidth(pair.getKey());
                vpResolution.setHeight(pair.getValue());
                vpConsoleData.setOutResolution(i, vpResolution);
              }
            }
          }
        } else if (entry.getName().equals(CaesarConstants.STATUS_EXTRA_CONF_NAME)) {
          loadExtraConfig(this, zis);
        } else if (entry.getName().equals(CaesarConstants.STATUS_VERSION_NAME)) {
          InputStreamReader reader = new InputStreamReader(zis, StandardCharsets.UTF_8);
          Map<Integer, VersionSet> versions = new Gson().fromJson(reader,
              new ParameterizedTypeImpl(Map.class, new Class[]{Integer.class, VersionSet.class}));
          if (versions != null) {
            for (ExtenderData data : getConfigData().getExtenderDatas()) {
              VersionSet versionSet = versions.get(data.getId());
              if (versionSet != null) {
                data.setVersion(versionSet);
              }
            }
          }
        } else if (entry.getName().equals(CaesarConstants.STATUS_SERIAL_NAME)) {
          InputStreamReader reader = new InputStreamReader(zis, StandardCharsets.UTF_8);
          Map<String, byte[]> serials = new Gson().fromJson(reader, SerialCache.class);
          if (serials != null) {
            getSerialCache().clear();
            getSerialCache().putAll(serials);
            reloadSerial(false);
          }
        } else if (entry.getName().equals(CaesarConstants.STATUS_EXTINFO_NAME)) {
          InputStreamReader reader = new InputStreamReader(zis, StandardCharsets.UTF_8);
          Map<Integer, Integer> extinfos = new Gson().fromJson(reader,
              new ParameterizedTypeImpl(Map.class, new Class[]{Integer.class, Integer.class}));
          if (extinfos != null) {
            for (ExtenderData data : getConfigData().getExtenderDatas()) {
              Integer extinfo = extinfos.get(data.getId());
              if (extinfo != null) {
                data.getExtenderStatusInfo().readData(extinfo);
              }
            }
          }
        } else if (entry.getName().equals(CaesarConstants.STATUS_EXTINFO2_NAME)) {
          InputStreamReader reader = new InputStreamReader(zis, StandardCharsets.UTF_8);
          Map<Integer, ExtenderStatusInfo> extinfos =
              new Gson().fromJson(reader, new ParameterizedTypeImpl(Map.class,
                  new Class[]{Integer.class, ExtenderStatusInfo.class}));
          if (extinfos != null) {
            for (ExtenderData data : getConfigData().getExtenderDatas()) {
              ExtenderStatusInfo extinfo = extinfos.get(data.getId());
              if (extinfo != null) {
                data.getExtenderStatusInfo().readData(extinfo);
              }
            }
          }
        } else if (entry.getName().equals(CaesarConstants.STATUS_EXTARGS_NAME)) {
          InputStreamReader reader = new InputStreamReader(zis, StandardCharsets.UTF_8);
          Map<Integer, ExtArgBean> extArgsMap =
              new Gson().fromJson(reader, new ParameterizedTypeImpl(Map.class,
                  new Class[]{Integer.class, ExtArgBean.class}));
          if (extArgsMap != null) {
            for (ExtenderData data : getConfigData().getExtenderDatas()) {
              ExtArgBean extArgBean = extArgsMap.get(data.getId());
              if (extArgBean != null) {
                if (extArgBean.audioInput != null) {
                  data.getAnalogAudioInputProperty().set(extArgBean.audioInput);
                  data.setExtArgExist(CaesarExtArg.EXT_ARG_ANALOG_INPUT_ID, true);
                }
                if (extArgBean.usbEnable != null) {
                  data.getUsbDiskEnableProperty().set(extArgBean.usbEnable.getValue());
                  data.setExtArgExist(CaesarExtArg.EXT_ARG_USB_ENABLE_ID, true);
                }
                if (extArgBean.videoQp != null) {
                  data.getVideoQpProperty().set(extArgBean.videoQp);
                  data.setExtArgExist(CaesarExtArg.EXT_ARG_VIDEO_QP_ID, true);
                }
                if (extArgBean.touchingScreen != null) {
                  data.getTouchEnableProperty().set(extArgBean.touchingScreen.getValue());
                  data.setExtArgExist(CaesarExtArg.EXT_ARG_TOUCHING_SCREEN_ID, true);
                }
                if (extArgBean.audioTrigger != null) {
                  data.getAudioTriggerProperty().set(extArgBean.audioTrigger);
                  data.setExtArgExist(CaesarExtArg.EXT_ARG_AUDIO_TRIGGER, true);
                }
                if (extArgBean.eventEnable != null) {
                  data.getEventEnableProperty().set(extArgBean.eventEnable.getValue());
                  data.setExtArgExist(CaesarExtArg.EXT_ARG_EVENT_ENABLE, true);
                }
                if (extArgBean.triggerHoldTime != null) {
                  data.getTriggerHoldTimeProperty().set(extArgBean.triggerHoldTime);
                  data.setExtArgExist(CaesarExtArg.EXT_ARG_TRIGGER_HOLD_TIME, true);
                }
                if (extArgBean.highCompressionRatio != null) {
                  data.getHighCompressionRatioProperty()
                      .set(extArgBean.highCompressionRatio.getValue());
                  data.setExtArgExist(CaesarExtArg.EXT_ARG_HIGH_COMPRESSION_RATIO, true);
                }
                if (extArgBean.osdMenu != null) {
                  data.getOsdMenuProperty().set(extArgBean.osdMenu.getValue());
                  data.setExtArgExist(CaesarExtArg.EXT_ARG_OSD_MENU, true);
                }
                if (extArgBean.icronEnable != null) {
                  data.getIcronEnableProperty().set(extArgBean.icronEnable.getValue());
                  data.setExtArgExist(CaesarExtArg.EXT_ARG_ICRON_ENABLE, true);
                }
                if (extArgBean.uartBaudRate != null) {
                  data.getUartBaudRateProperty().set(extArgBean.uartBaudRate);
                  data.setExtArgExist(CaesarExtArg.EXT_ARG_UART_BAUD_RATE, true);
                }
                if (extArgBean.dpMode != null) {
                  data.getDpModeProperty().set(extArgBean.dpMode);
                  data.setExtArgExist(CaesarExtArg.EXT_ARG_DOUBLE_DP_ENABLE, true);
                }
              }
            }
          }
        } else if (entry.getName().equals(CaesarConstants.STATUS_EDID_NAME)) {
          InputStreamReader reader = new InputStreamReader(zis, StandardCharsets.UTF_8);
          Map<Integer, List<String>> edids =
              new Gson().fromJson(reader, new ParameterizedTypeImpl(Map.class,
                  new Class[]{Integer.class, List.class}));
          if (edids != null) {
            for (ExtenderData data : getConfigData().getExtenderDatas()) {
              List<String> edid = edids.get(data.getId());
              if (edid != null) {
                for (int i = 0; i < edid.size(); i++) {
                  data.setProperty(ExtenderData.PROPERTY_EDID + i, edid.get(i));
                }
              }
            }
          }
        } else if (entry.getName().equals(CaesarConstants.STATUS_MISC_NAME)) {
          InputStreamReader reader = new InputStreamReader(zis, StandardCharsets.UTF_8);
          Map<String, Object> misc =
              new Gson().fromJson(reader, new ParameterizedTypeImpl(Map.class,
                  new Class[]{String.class, Object.class}));
          Object index = misc.get(CaesarConstants.STATUS_MISC_MASTER);
          if (index instanceof Number) {
            getConfigData().setGridIndex(((Number) index).intValue());
          }
          Object doubleBackup = misc.get(CaesarConstants.STATUS_DOUBLE_BACKUP);
          if (doubleBackup instanceof Boolean) {
            getConfigData().getMatrixStatus().setDoubleBackup((Boolean) doubleBackup);
          }
        }
      }
      return true;
    } catch (JsonSyntaxException | JsonIOException | IOException | ConfigException exception) {
      LOG.log(Level.WARNING, "Read demo error!", exception);
      return false;
    } finally {
      if (fis != null) {
        try {
          fis.close();
        } catch (IOException exception) {
          LOG.log(Level.WARNING, "Fail to close stream!");
        }
      }
    }
  }

  /**
   * 读取配置数据文件.
   *
   * @param file 文件
   * @return 如果读取成功，返回true
   */
  public boolean readDemoData(ResourceFile file) {
    try {
      setConnection("***************:5555");
    } catch (ConfigException ex) {
      LOG.log(Level.SEVERE, null, ex);
    }

    InputStream fis = null;
    try {
      fis = file.createInputStream();
      CfgReader reader = new CfgReader(fis, true);
      getConfigData().readData(reader, IoMode.Available);
      return true;
    } catch (FileNotFoundException exception) {
      LOG.log(Level.WARNING, "Fail to find : {}!", file.getName());
      return false;
    } catch (ConfigException exception) {
      LOG.log(Level.WARNING, "Fail to load data", exception);
      return false;
    } catch (RuntimeException exception) {
      LOG.log(Level.WARNING, "Unknown error!", exception);
      return false;
    } finally {
      if (fis != null) {
        try {
          fis.close();
        } catch (IOException exception) {
          LOG.log(Level.WARNING, "Fail to close stream!");
        }
      }
    }
  }

  /**
   * 读取cfgx文件.
   *
   * @param file 文件
   * @return 如果读取成功，返回true.
   */
  public boolean readCfgxFile(ResourceFile file) {
    try {
      setConnection("***************:5555");
    } catch (ConfigException ex) {
      LOG.log(Level.SEVERE, null, ex);
    }

    InputStream fis = null;
    ZipInputStream zis = null;
    try {
      fis = file.createInputStream();
      zis = new ZipInputStream(new BufferedInputStream(fis));
      ZipEntry entry;
      boolean hasConfig = false;
      while ((entry = zis.getNextEntry()) != null) {
        if (entry.getName().equals(CaesarConstants.CFGX_CONFIG_NAME)) {
          CfgReader reader = new CfgReader(zis, true);
          getConfigData().readData(reader, IoMode.Available);
          hasConfig = true;
        } else if (entry.getName().equals(CaesarConstants.CFGX_EXTRA_CONF_NAME)) {
          loadExtraConfig(this, zis);
        }
      }
      return hasConfig;
    } catch (FileNotFoundException exception) {
      LOG.log(Level.WARNING, "Fail to find : {}!", file.getName());
      return false;
    } catch (ConfigException exception) {
      LOG.log(Level.WARNING, "Fail to load data", exception);
      return false;
    } catch (RuntimeException exception) {
      LOG.log(Level.WARNING, "Unknown error!", exception);
      return false;
    } catch (IOException exception) {
      LOG.log(Level.WARNING, "IO error!", exception);
      return false;
    } finally {
      if (zis != null) {
        try {
          zis.close();
        } catch (IOException exception) {
          LOG.log(Level.WARNING, "Fail to close stream!");
        }
      }
      if (fis != null) {
        try {
          fis.close();
        } catch (IOException exception) {
          LOG.log(Level.WARNING, "Fail to close stream!");
        }
      }
    }
  }

  @Override
  public void setConnection(String connection) throws ConfigException {
    checkThreadState();
  }

  @Override
  public void setConnection(String hostname, boolean enablePing)
      throws ConfigException, BusyException {
    checkThreadState();
  }

  @Override
  public void reloadNetworkData() throws ConfigException {
    getConfigData().getSystemConfigData().commit(Threshold.ALL);
    NetworkData networkData = getConfigData().getSystemConfigData().getNetworkDataCurrent1();
    if (networkData.getMacAddress() == null || networkData.getMacAddress().isEmpty()) {
      networkData.setMacAddress("00:00:00:00:demo");
    }
    networkData.commit();

    NetworkData networkData2 = getConfigData().getSystemConfigData().getNetworkDataCurrent2();
    if (networkData2.getMacAddress() == null || networkData2.getMacAddress().isEmpty()) {
      networkData2.setMacAddress("00:00:00:00:demo2");
    }
    networkData2.commit();
  }

  @Override
  public void reloadSystemData() throws ConfigException {
    checkThreadState();
    getConfigData().commit(Threshold.ALL);
  }

  @Override
  public void reloadConfigData() throws ConfigException, BusyException {
    checkThreadState();
  }

  @Override
  public void reloadExtenderData() throws ConfigException {
    checkThreadState();
    getConfigData().commit(Threshold.ALL);
  }

  @Override
  public void reloadExtenderData(Iterable<ExtenderData> requestedDatas) throws ConfigException {
    checkThreadState();
    getConfigData().commit(Threshold.ALL);
  }

  @Override
  public void reloadCpuData() throws ConfigException {
    checkThreadState();
    getConfigData().commit(Threshold.ALL);
  }

  @Override
  public void reloadCpuData(Iterable<CpuData> requestedDatas) throws ConfigException {
    checkThreadState();
    getConfigData().commit(Threshold.ALL);
  }

  @Override
  public void reloadConsoleData() throws ConfigException {
    checkThreadState();
    getConfigData().commit(Threshold.ALL);
  }

  @Override
  public void reloadConsoleData(Iterable<ConsoleData> requestedDatas) throws ConfigException {
    checkThreadState();
    getConfigData().commit(Threshold.ALL);
  }

  @Override
  public void reloadFunctionKeyData() throws ConfigException {
    checkThreadState();
    getConfigData().commit(Threshold.ALL);
  }

  @Override
  public void reloadFunctionKeyData(Iterable<FunctionKeyData> requestedDatas)
      throws ConfigException, BusyException {
    checkThreadState();
  }

  @Override
  public void reloadUserData() throws ConfigException {
    checkThreadState();
    getConfigData().commit(Threshold.ALL);
  }

  @Override
  public void reloadCpuConsoleMatrix() throws ConfigException {
    checkThreadState();
    getConfigData().commit(Threshold.ALL);
  }

  @Override
  public void reloadAll() throws ConfigException, BusyException {
    checkThreadState();
  }

  @Override
  public void reloadExtenderVersion(boolean onlyOldVersion) {
    checkThreadState();
  }

  @Override
  protected void reloadExtResolution(boolean onlyOldVersion) {
    checkThreadState();
  }

  @Override
  protected void reloadEdid() {
    checkThreadState();
  }

  @Override
  protected void reloadExtInfo(boolean onlyOldVersion) {
    checkThreadState();
  }

  @Override
  public void reloadVpconResolution() {
    checkThreadState();
  }

  @Override
  public void sendVpconConfigData(VpConsoleData consoleData)
      throws DeviceConnectionException, BusyException {
    checkThreadState();
    if (consoleData != null && consoleData.getConfigData() != null) {
      LOG.log(Level.INFO, "vpcon " + consoleData.getName() + " data:");
      consoleData.getConfigData().print();
    }
  }

  public CaesarConfigDataModel getFtpConfiguration(String name) {
    return this.ftpConfigurations.get(name);
  }

  @Override
  public void setServiceMode(int id, boolean serviceMode) throws ConfigException {
    checkThreadState();
  }

  @Override
  public void setServiceMode(int moduleId, int portId, boolean serviceMode)
      throws ConfigException, BusyException {
    checkThreadState();
  }

  @Override
  public void reset(byte level1, byte level2, byte level3) throws ConfigException {
    checkThreadState();
  }

  @Override
  public void restart(int module) throws ConfigException {
    checkThreadState();
  }

  @Override
  public void videoConnect(ConsoleData consoleData, CpuData cpuData) throws ConfigException {
    checkThreadState();
  }

  @Override
  public void sendCpuConsoleConnection(CpuData cpuData, ConsoleData consoleData)
      throws ConfigException {
    checkThreadState();
  }

  @Override
  public void sendCpuConsoleBlock() throws ConfigException {
    checkThreadState();
  }

  @Override
  public void sendCpuConsoleBlock(Map<ConsoleData, CpuData> fullAccess,
      Map<ConsoleData, CpuData> videoAccess, Map<ConsoleData, CpuData> privateMode)
      throws ConfigException, BusyException {
    checkThreadState();
  }

  @Override
  public void sendCpuConsoleBlock(Map<ConsoleData, CpuData> fullAccess,
      Map<ConsoleData, CpuData> videoAccess) throws ConfigException, BusyException {
    checkThreadState();
  }

  @Override
  public void sendCpuConsolePrivate(CpuData cpuData, ConsoleData consoleData)
      throws ConfigException, BusyException {
    checkThreadState();
  }

  @Override
  public void sendCpuData(Iterable<CpuData> cpuDatas)
      throws DeviceConnectionException, BusyException {
    checkThreadState();
  }

  @Override
  public void sendDisconnectPorts() throws ConfigException, BusyException {
    checkThreadState();
  }

  @Override
  public void sendExtenderData(Iterable<ExtenderData> extenderDatas)
      throws DeviceConnectionException, BusyException {
    checkThreadState();
  }

  @Override
  public void sendFunctionKeyData(Iterable<FunctionKeyData> functionKeyDatas)
      throws DeviceConnectionException, BusyException {
    checkThreadState();
  }

  @Override
  public void sendMatrixData(Iterable<MatrixData> matrixDatas)
      throws DeviceConnectionException, BusyException {
    checkThreadState();
  }

  @Override
  public void sendMultiscreenData(Iterable<MultiScreenData> datas)
      throws DeviceConnectionException, BusyException {
    checkThreadState();
  }

  @Override
  public void sendPortData(Iterable<PortData> portData)
      throws DeviceConnectionException, BusyException {
    checkThreadState();
  }

  @Override
  public void sendUserData(Iterable<UserData> userDatas)
      throws DeviceConnectionException, BusyException {
    checkThreadState();
  }

  @Override
  public void sendUserGroupData(Iterable<UserGroupData> userGroupDatas)
      throws DeviceConnectionException, BusyException {
    checkThreadState();
  }

  @Override
  public void sendVpconOutputData(VpConsoleData consoleData, Vp6OutputData vp6OutputData)
      throws BusyException {
    checkThreadState();
  }

  @Override
  public void sendConsoleData(Iterable<ConsoleData> consoleDatas)
      throws DeviceConnectionException, BusyException {
    checkThreadState();
  }

  @Override
  public void sendSystemData() throws DeviceConnectionException, BusyException {
    checkThreadState();
  }

  @Override
  public void setCpuVirtualOutIn(CpuData data, boolean in) throws BusyException, ConfigException {
    checkThreadState();
  }

  @Override
  public void setEdid(ExtenderData extenderData, int index, byte[] edid)
      throws BusyException, ConfigException {
    checkThreadState();
  }

  @Override
  public void setExtUsbDiskEnable(ExtenderData data, boolean enable)
      throws BusyException, ConfigException {
    checkThreadState();
  }

  @Override
  public void setAnalogAudioInput(ExtenderData extenderData, ExtenderAnalogAudioInput input)
      throws ConfigException, BusyException, DeviceConnectionException {
    checkThreadState();
  }

  @Override
  public void getAllExtArgs(ExtenderData extenderData)
      throws ConfigException, BusyException, DeviceConnectionException {
    checkThreadState();
  }

  @Override
  public void setTime(LocalDateTime date) throws ConfigException, BusyException {
    checkThreadState();
  }

  @Override
  public boolean getExtResolution(ExtenderData extenderData) {
    checkThreadState();
    return true;
  }

  @Override
  public void getEdid(ExtenderData extenderData, int index) throws BusyException, ConfigException {
    checkThreadState();
  }

  @Override
  public void getExtInfo(ExtenderData extenderData) throws BusyException, ConfigException {
    checkThreadState();
  }

  @Override
  public void getVpconResolution(VpConsoleData vpConsoleData) throws BusyException {
    checkThreadState();
  }

  @Override
  public VpResolution[] getVpconResolution(ExtenderData extenderData) {
    checkThreadState();
    return new VpResolution[0];
  }

  @Override
  public VpConConfigData getVpconConfigData(VpConsoleData vpConsoleData) throws BusyException {
    checkThreadState();
    return vpConsoleData.getConfigData();
  }

  @Override
  public VersionSet getVersion(byte level1, byte level2) throws ConfigException, BusyException {
    checkThreadState();
    return new VersionSet();
  }

  @Override
  public void beginUpdateVideoWall()
      throws DeviceConnectionException, BusyException, ConfigException {
    checkThreadState();
  }

  @Override
  public void endUpdateVideoWall()
      throws DeviceConnectionException, BusyException, ConfigException {
    checkThreadState();
  }

  @Override
  public SystemTimeData getTime() throws ConfigException, BusyException {
    checkThreadState();
    return new SystemTimeData(LocalDateTime.now(), 0);
  }

  @Override
  public LoginResponse login(byte[] username, byte[] password)
      throws ConfigException, BusyException {
    checkThreadState();
    return LoginResponse.SUCCESS;
  }

  public void setMultiviewLayout(int id, MultiviewLayoutType layout)
      throws DeviceConnectionException, BusyException {
    checkThreadState();
  }

  public boolean checkUsernameValidity(String username) {
    checkThreadState();
    return true;
  }


  public ExtenderNetworkInfo getExtNetworkInfo(ExtenderData extenderData) {
    checkThreadState();
    return new ExtenderNetworkInfo();
  }

  public void sendTxRxGroupData(Iterable<TxRxGroupData> datas) {
    checkThreadState();
  }

  private void checkThreadState() {
    if (Platform.isFxApplicationThread()) {
      throw new IllegalStateException("Should not be in javafx thread!");
    }
  }

  /**
   * 读取cfgx的额外配置数据.
   *
   * @param model        model
   * @param is          额外配置数据流.
   * @return 如果读取成功，返回true.
   */
  private static boolean loadExtraConfig(CaesarSwitchDataModel model, InputStream is) {
    try {
      ZipInputStream zis = new ZipInputStream(is);
      ZipEntry entry;
      while ((entry = zis.getNextEntry()) != null) {
        if (entry.getName().equals("vp/" + CaesarConstants.CFGX_VP_PRE_NAME)) {
          loadVpScenarios(model, zis);
        } else if (entry.getName().equals("vp/" + CaesarConstants.CFGX_VP_BACKUP_NAME)) {
          model.getVpDataModel().getVideoWallGroupData().read(zis);
        } else if (entry.getName().equals("db/" + CaesarConstants.CFGX_DB_BK)) {
          byte[] buffer = new byte[1024];
          StringBuilder sb = new StringBuilder();
          int len = 0;
          while ((len = zis.read(buffer)) != -1) {
            sb.append(new String(buffer, 0, len, StandardCharsets.UTF_8));
          }
          model.getDbDataModel().setDataBackup(sb.toString());
        }
      }
    } catch (IOException exception) {
      LOG.log(Level.WARNING, "Fail write vp zip file!", exception);
      return false;
    }
    return true;
  }

  private static void loadVpScenarios(CaesarSwitchDataModel model, ZipInputStream zis)
      throws IOException {
    for (int i = 0; i < CaesarConstants.SCENARIO_SIZE && zis.available() > 0; i++) {
      byte[] bytes = new byte[VideoWallGroupData.VIDEOWALL_SCENARIO_RESERVED_DATA_COUNT];
      int count = 0;
      while (count < VideoWallGroupData.VIDEOWALL_SCENARIO_RESERVED_DATA_COUNT
          && zis.available() > 0) {
        int size = zis.read(bytes, count,
            VideoWallGroupData.VIDEOWALL_SCENARIO_RESERVED_DATA_COUNT - count);
        count += size;
      }
      if (count != VideoWallGroupData.VIDEOWALL_SCENARIO_RESERVED_DATA_COUNT) {
        break;
      }

      ByteArrayInputStream bais = new ByteArrayInputStream(bytes);
      // 读取信息头
      byte[] header = new byte[9];
      int read = bais.read(header);
      if (read == -1) {
        break;
      }
      ScenarioData data = new ScenarioData();
      // 读取数据
      CfgReader reader = new CfgReader(bais);
      try {
        data.readData(reader, model);
      } catch (IOException | ConfigException exception) {
        LOG.log(Level.WARNING, "Fail to read scenario " + i + "'s data!", exception);
      }
      if (data.getVideoWallData().isDataValid()) {
        data.setIndex(i);
        model.getVpDataModel().sendScenarioData(data);
      }
    }
  }

  static class SerialCache extends HashMap<String, byte[]> {

    private static final long serialVersionUID = -4067614634472610800L;
  }

  static class VpResolutions extends HashMap<Integer, Pair<Integer, Integer>[]> {

    private static final long serialVersionUID = 32871758971603857L;
  }
}

