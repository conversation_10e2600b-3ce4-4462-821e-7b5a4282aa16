package com.mc.graph;

import com.mc.common.beans.FactorBidirectionalBinding;
import com.mc.common.beans.FactorBidirectionalBinding.FactorType;
import com.mc.graph.interfaces.CellObject;
import com.mc.graph.interfaces.CellSkin;
import com.mc.graph.interfaces.Connector;
import com.mc.graph.interfaces.ConnectorSkin;
import com.mc.graph.interfaces.LinkObject;
import com.mc.graph.interfaces.LinkSkin;
import com.mc.graph.interfaces.NewLinkSkin;
import com.mc.graph.interfaces.SkinFactory;
import com.mc.graph.interfaces.SkinManager;
import javafx.beans.property.DoubleProperty;
import javafx.scene.Parent;

public class DefaultSkinFactory implements SkinFactory {

  @Override
  public CellSkin createCellSkin(CellObject cellObject, Parent parent, Parent container,
      SkinManager skinManager) {
    CellSkin skin = new DefaultCellSkin(cellObject, parent, container, skinManager);
    skin.getRegion().layoutXProperty().bindBidirectional(cellObject.getXProperty());
    skin.getRegion().layoutYProperty().bindBidirectional(cellObject.getYProperty());
    skin.getRegion().prefWidthProperty().bindBidirectional(cellObject.getWidthProperty());
    skin.getRegion().prefHeightProperty().bindBidirectional(cellObject.getHeightProperty());
    skinManager.setCellSkin(cellObject, skin);
    return skin;
  }

  @Override
  public ConnectorSkin createConnectorSkin(String type, Parent parent) {
    return null;
  }

  @Override
  public NewLinkSkin createNewLinkSkin(String type, Connector sender, Parent parent,
      Parent container, SkinManager skinManager) {
    return new DefaultNewLinkSkin(sender, parent, container, skinManager);
  }

  @Override
  public LinkSkin createLinkSkin(LinkObject link, Parent parent, Parent container,
      SkinManager skinManager) {
    LinkSkin skin = new DefaultLinkSkin(link, parent, container, skinManager);
    skinManager.setLinkSkin(link, skin);
    return skin;
  }

  @Override
  public CellSkin createScaleCellSkin(CellObject cellObject, Parent parent, Parent container,
      SkinManager skinManager, DoubleProperty cellScaleProperty) {
    CellSkin skin = new DefaultCellSkin(cellObject, parent, container, skinManager);
    FactorBidirectionalBinding.bind(skin.getRegion().layoutXProperty(), 
        cellObject.getXProperty(), cellScaleProperty, FactorType.MULTIPLY);
    FactorBidirectionalBinding.bind(skin.getRegion().layoutYProperty(), 
        cellObject.getYProperty(), cellScaleProperty, FactorType.MULTIPLY);
    FactorBidirectionalBinding.bind(skin.getRegion().prefWidthProperty(), 
        cellObject.getWidthProperty(), cellScaleProperty, FactorType.MULTIPLY);
    FactorBidirectionalBinding.bind(skin.getRegion().prefHeightProperty(), 
        cellObject.getHeightProperty(), cellScaleProperty, FactorType.MULTIPLY);
    skinManager.setCellSkin(cellObject, skin);
    return skin;
  }

}
