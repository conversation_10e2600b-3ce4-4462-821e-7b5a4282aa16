package com.mc.tool.framework.systemedit.datamodel;

import com.google.gson.annotations.Expose;
import com.mc.tool.framework.operation.seat.datamodel.SeatData;
import com.mc.tool.framework.operation.seat.datamodel.SeatData.SeatConnection;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import javafx.collections.FXCollections;
import javafx.collections.ListChangeListener;
import javafx.collections.ObservableList;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class DefaultSeatFunc extends VisualEditFunc {
  @Getter
  @Expose
  protected SeatData seatData = new SeatData();
  @Getter
  @Expose
  protected ObservableList<SeatData> scenarios = FXCollections.observableArrayList();

  @Override
  public String getNodeType() {
    return SystemEditDefinition.SEAT_CELL;
  }

  @Override
  public void init() {
    super.init();

    getAllTerminalChild().addListener(new ListChangeListener<VisualEditTerminal>() {

      @Override
      public void onChanged(ListChangeListener.Change<? extends VisualEditTerminal> change) {
        while (change.next()) {
          if (change.wasRemoved()) {
            for (VisualEditTerminal item : change.getRemoved()) {
              removeItem(item);
            }
          }

          if (change.wasAdded()) {
            for (VisualEditTerminal item : change.getAddedSubList()) {
              addItem(item);
            }
          }
        }
      }
    });
  }

  protected void addItem(VisualEditTerminal item) {
    if (item == null) {
      log.warn("null item!");
      return;
    }
    //添加预案的项
    for (SeatData scenario : getScenarios()) {
      SeatConnection connection = new SeatConnection();
      connection.setRx(item);
      scenario.getConnections().put(item, connection);
    }
    //
    SeatConnection connection = new SeatConnection();
    connection.setRx(item);
    if (seatData.getConnections().containsKey(item)) {
      log.warn("{} exists!", item.getName());
    } else {
      seatData.getConnections().put(item, connection);
    }
  }

  protected void removeItem(VisualEditTerminal item) {
    if (item == null) {
      log.warn("null item!");
      return;
    }
    //删除预案相关的项
    for (SeatData scenario : getScenarios()) {
      scenario.getConnections().remove(item);
    }
    //
    if (seatData.getConnections().containsKey(item)) {
      seatData.getConnections().remove(item);
    } else {
      log.warn("{} do not exist!", item.getName());
    }

  }

  /**
   * 获取所有的连接列表.
   *
   * @return 连接列表
   */
  public Collection<SeatConnection> getConnections() {
    List<SeatConnection> conns = new ArrayList<>();
    for (VisualEditTerminal terminal : getAllTerminalChild()) {
      SeatConnection conn = getSeatData().getConnections().get(terminal);
      if (conn != null) {
        conns.add(conn);
      }
    }
    return conns;
  }
}
