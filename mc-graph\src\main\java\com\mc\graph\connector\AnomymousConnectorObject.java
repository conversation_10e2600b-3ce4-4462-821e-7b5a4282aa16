package com.mc.graph.connector;

import com.google.gson.annotations.Expose;

import com.mc.graph.interfaces.ConnectorIdentifier;
import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.StringProperty;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

public class AnomymousConnectorObject implements ConnectorIdentifier {
  /**
   * 所有的匿名connector的id与text的对应关系.
   */
  private static Map<String, StringProperty> textMap;

  static {
    textMap = new HashMap<>();
  }



  @Getter
  @Expose
  private final String id = UUID.randomUUID().toString();

  private StringProperty text;

  public AnomymousConnectorObject() {}

  @Override
  public String toString() {
    return textProperty().get();
  }

  @Override
  public int hashCode() {
    return id.hashCode();
  }

  @Override
  public boolean equals(Object obj) {
    if (obj instanceof AnomymousConnectorObject) {
      return ((AnomymousConnectorObject) obj).getId().equals(getId());
    }
    return false;
  }

  @Override
  public StringProperty textProperty() {
    if (text == null) {
      text = textMap.get(id);
      if (text == null) {
        text = new SimpleStringProperty("#");
        textMap.put(id, text);
      }
    }
    return text;
  }
}
