package com.dooapp.fxform.view.factory;

import com.dooapp.fxform.handler.AbstractWrappedTypeHandler;
import com.dooapp.fxform.handler.ElementHandler;
import com.dooapp.fxform.handler.EnumHandler;
import com.dooapp.fxform.handler.InterfaceWrappedTypeHandler;
import com.dooapp.fxform.handler.TypeFieldHandler;
import com.dooapp.fxform.handler.WrappedTypeHandler;
import com.dooapp.fxform.model.Element;
import com.dooapp.fxform.view.FXFormNode;
import com.dooapp.fxform.view.factory.impl.CheckboxFactory;
import com.dooapp.fxform.view.factory.impl.ColorPickerFactory;
import com.dooapp.fxform.view.factory.impl.DatePickerFactory;
import com.dooapp.fxform.view.factory.impl.DateTimePickerFactory;
import com.dooapp.fxform.view.factory.impl.EnumChoiceBoxFactory;
import com.dooapp.fxform.view.factory.impl.LabelFactory;
import com.dooapp.fxform.view.factory.impl.MapEditorControlFactory;
import com.dooapp.fxform.view.factory.impl.SubClassFactory;
import com.dooapp.fxform.view.factory.impl.TableViewFactory;
import com.dooapp.fxform.view.factory.impl.TextFieldFactory;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.DoubleProperty;
import javafx.beans.property.FloatProperty;
import javafx.beans.property.IntegerProperty;
import javafx.beans.property.ListProperty;
import javafx.beans.property.LongProperty;
import javafx.beans.property.MapProperty;
import javafx.beans.property.ReadOnlyBooleanProperty;
import javafx.beans.property.ReadOnlyDoubleProperty;
import javafx.beans.property.ReadOnlyIntegerProperty;
import javafx.beans.property.ReadOnlyLongProperty;
import javafx.beans.property.ReadOnlyStringProperty;
import javafx.beans.property.StringProperty;
import javafx.scene.paint.Color;
import javafx.util.Callback;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.Map;

public class DefaultFactoryProvider implements FactoryProvider {

  private final Map<ElementHandler, Callback<Void, FXFormNode>> defaultMap =
      new LinkedHashMap<ElementHandler, Callback<Void, FXFormNode>>();

  private static final Map<ElementHandler, Callback<Void, FXFormNode>> globalMap =
      Collections.synchronizedMap(new LinkedHashMap<ElementHandler, Callback<Void, FXFormNode>>());

  private final Map<ElementHandler, Callback<Void, FXFormNode>> userMap =
      new LinkedHashMap<ElementHandler, Callback<Void, FXFormNode>>();

  {
    // register default delegates
    defaultMap.put(new EnumHandler(), new EnumChoiceBoxFactory());

    // based on element type
    defaultMap.put(new TypeFieldHandler(StringProperty.class), new TextFieldFactory());
    defaultMap.put(new TypeFieldHandler(BooleanProperty.class), new CheckboxFactory());
    defaultMap.put(new TypeFieldHandler(IntegerProperty.class), new TextFieldFactory());
    defaultMap.put(new TypeFieldHandler(LongProperty.class), new TextFieldFactory());
    defaultMap.put(new TypeFieldHandler(DoubleProperty.class), new TextFieldFactory());
    defaultMap.put(new TypeFieldHandler(FloatProperty.class), new TextFieldFactory());
    defaultMap.put(new TypeFieldHandler(ListProperty.class), new TableViewFactory());
    defaultMap.put(new TypeFieldHandler(ReadOnlyStringProperty.class), new LabelFactory());
    defaultMap.put(new TypeFieldHandler(ReadOnlyBooleanProperty.class), new CheckboxFactory());
    defaultMap.put(new TypeFieldHandler(ReadOnlyIntegerProperty.class), new LabelFactory());
    defaultMap.put(new TypeFieldHandler(ReadOnlyLongProperty.class), new LabelFactory());
    defaultMap.put(new TypeFieldHandler(ReadOnlyDoubleProperty.class), new LabelFactory());
    defaultMap.put(new TypeFieldHandler(MapProperty.class), new MapEditorControlFactory());

    // based on element wrapped type
    defaultMap.put(new WrappedTypeHandler(BigDecimal.class), new TextFieldFactory());
    defaultMap.put(new WrappedTypeHandler(Color.class), new ColorPickerFactory());
    defaultMap.put(new WrappedTypeHandler(LocalDate.class), new DatePickerFactory());
    defaultMap.put(new WrappedTypeHandler(LocalDateTime.class), new DateTimePickerFactory());
    defaultMap.put(new WrappedTypeHandler(String.class), new TextFieldFactory());
    defaultMap.put(new WrappedTypeHandler(Boolean.class), new CheckboxFactory());
    defaultMap.put(new WrappedTypeHandler(Integer.class), new TextFieldFactory());
    defaultMap.put(new WrappedTypeHandler(Long.class), new TextFieldFactory());
    defaultMap.put(new WrappedTypeHandler(Double.class), new TextFieldFactory());
    defaultMap.put(new WrappedTypeHandler(Float.class), new TextFieldFactory());
    defaultMap.put(new AbstractWrappedTypeHandler(), new SubClassFactory(this));
    defaultMap.put(new InterfaceWrappedTypeHandler(), new SubClassFactory(this));
  }

  private Callback<Void, FXFormNode> getDelegate(Element element,
      Map<ElementHandler, Callback<Void, FXFormNode>> map) {
    for (Map.Entry<ElementHandler, Callback<Void, FXFormNode>> entry : map.entrySet()) {
      if (entry.getKey().handle(element)) {
        return entry.getValue();
      }
    }
    return null;
  }

  public static void addGlobalFactory(ElementHandler handler, Callback<Void, FXFormNode> factory) {
    globalMap.put(handler, factory);
  }

  public void addFactory(ElementHandler handler, Callback<Void, FXFormNode> factory) {
    userMap.put(handler, factory);
  }

  /**
   * Create the node by trying to find a delegate factory. This method will lookup in the user map,
   * the global map and finally in the default map.
   *
   * @return the created node
   */
  public Callback<Void, FXFormNode> getFactory(Element element) {
    // check user defined factories
    Callback<Void, FXFormNode> delegate = getDelegate(element, userMap);
    // check user defined global factories
    if (delegate == null) {
      delegate = getDelegate(element, globalMap);
    }
    // check default map
    if (delegate == null) {
      delegate = getDelegate(element, defaultMap);
    }
    return delegate;
  }

  public String toString() {
    return "[DefaultFactoryProvider\n" + "GLOBAL_MAP:\n" + dumpMap(globalMap) + "\nUSER_MAP:\n"
        + dumpMap(userMap) + "]";
  }

  private String dumpMap(Map map) {
    StringBuilder sb = new StringBuilder();
    Iterator<Map.Entry> iter = map.entrySet().iterator();
    while (iter.hasNext()) {
      Map.Entry entry = iter.next();
      sb.append(entry.getKey());
      sb.append('=').append('"');
      sb.append(entry.getValue());
      sb.append('"');
      if (iter.hasNext()) {
        sb.append(',').append('\n');
      }
    }
    return sb.toString();
  }
}
