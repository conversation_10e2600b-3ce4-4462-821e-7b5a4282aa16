package com.mc.common.control;

import com.mc.common.beans.SimpleBooleanBinding;
import com.mc.common.util.CommonResource;
import javafx.application.Platform;
import javafx.beans.binding.BooleanBinding;
import javafx.geometry.Pos;
import javafx.scene.Node;
import javafx.scene.control.ButtonBar.ButtonData;
import javafx.scene.control.ButtonType;
import javafx.scene.control.CheckBox;
import javafx.scene.control.Control;
import javafx.scene.control.DialogEx;
import javafx.scene.control.DialogPaneEx;
import javafx.scene.control.Label;
import javafx.scene.control.PasswordField;
import javafx.scene.control.TextField;
import javafx.scene.control.TextFormatter;
import javafx.scene.control.TextFormatter.Change;
import javafx.scene.layout.GridPane;
import javafx.scene.layout.Priority;
import javafx.scene.layout.Region;
import javafx.util.Pair;
import javafx.util.converter.IntegerStringConverter;
import org.controlsfx.validation.ValidationResult;
import org.controlsfx.validation.ValidationSupport;
import org.controlsfx.validation.Validator;
import org.controlsfx.validation.decoration.ValidationDecoration;

import java.util.ArrayList;
import java.util.List;
import java.util.function.UnaryOperator;

public class IpInputDialog extends DialogEx<IpInputInfo> {
  private GridPane grid;

  private List<Pair<Node, Node>> gridControls = new ArrayList<>();
  private List<ValidationSupport> validations = new ArrayList<>();

  private static final String IP_REGX = "((2(5[0-5]|[0-4]\\d))|[0-1]?\\d{1,2})"
      + "(\\.((2(5[0-5]|[0-4]\\d))|[0-1]?\\d{1,2})){3}";
  private static final ValidationDecoration VALIDATION_DECORATION = null;
  
  /**
   * 创建可输入ip与端口的对话框.
   * 
   * @param defaultIp 默认ip
   * @param defaultPort 默认端口
   */
  public IpInputDialog(String defaultIp, int defaultPort) {
    TextField ipTextField = initIp(defaultIp);
    TextField portTextField = initPort(defaultPort);

    initImpl();

    updateGrid();

    setResultConverter((dialogButton) -> {
      ButtonData data = dialogButton == null ? null : dialogButton.getButtonData();
      if (data == ButtonData.OK_DONE) {
        IpInputInfo info = new IpInputInfo();
        info.setIp(ipTextField.getText());
        info.setPort(Integer.parseInt(portTextField.getText()));
        return info;
      } else {
        return null;
      }
    });
  }

  /**
   * 创建一个可输入ip、用户名、密码的对话框.
   * @param defaultIp 默认ip
   */
  @SuppressWarnings("checkstyle:WhitespaceAfter")
  public IpInputDialog(String defaultIp, String defaultUser, String defaultPwd, boolean remember) {
    TextField ipTextField = initIp(defaultIp);
    TextField userTextField = initUser(defaultUser);
    TextField pwdTextField = initPwd(defaultPwd);
    CheckBox rememberCheckBox = initRemember(remember);


    initImpl();
    updateGrid();

    setResultConverter(dialogButton -> {
      ButtonData data = dialogButton == null ? null : dialogButton.getButtonData();
      if (data == ButtonData.OK_DONE) {
        IpInputInfo info = new IpInputInfo();
        info.setIp(ipTextField.getText());
        info.setUser(userTextField.getText());
        info.setPwd(pwdTextField.getText());
        info.setRemember(rememberCheckBox.isSelected());
        return info;
      } else {
        return null;
      }
    });
  }

  private void initImpl() {
    final DialogPaneEx dialogPane = getDialogPane();
    this.grid = new GridPane();
    this.grid.setHgap(10);
    this.grid.setVgap(10);
    this.grid.setMaxWidth(Double.MAX_VALUE);
    this.grid.setAlignment(Pos.CENTER_LEFT);

    dialogPane.contentTextProperty().addListener(o -> updateGrid());
    dialogPane.setGraphic(null);
    setTitle(CommonResource.getString("ip_input_dialog.title"));
    dialogPane.setHeaderText(CommonResource.getString("ip_input_dialog.header"));
    dialogPane.getStyleClass().add("text-input-dialog");
    dialogPane.getButtonTypes().addAll(ButtonType.OK, ButtonType.CANCEL);

    BooleanBinding validationBinding = new SimpleBooleanBinding(false);
    for (ValidationSupport support : validations) {
      validationBinding = validationBinding.or(support.invalidProperty());
    }
    dialogPane.lookupButton(ButtonType.OK).disableProperty().bind(validationBinding);
  }

  private TextField initIp(String defaultIp) {
    // -- textfield
    TextField ipTextField = new TextField(defaultIp);
    ipTextField.setMaxWidth(Double.MAX_VALUE);
    ipTextField.setId("ip-input");
    GridPane.setHgrow(ipTextField, Priority.ALWAYS);
    GridPane.setFillWidth(ipTextField, true);

    ValidationSupport validationSupport = new ValidationSupport();
    validationSupport.setValidationDecorator(VALIDATION_DECORATION);
    validationSupport.registerValidator(ipTextField, new IpValidator());

    // -- label
    Label ipLabel = new Label();
    ipLabel.setPrefWidth(Region.USE_COMPUTED_SIZE);
    ipLabel.setText(CommonResource.getString("ip_input_dialog.ip_address"));

    gridControls.add(new Pair<>(ipLabel, ipTextField));
    validations.add(validationSupport);
    return ipTextField;
  }

  private TextField initUser(String defaultUser) {
    // -- textfield
    TextField userTextField = new TextField(defaultUser);
    userTextField.setId("user-input");
    userTextField.setMaxWidth(Double.MAX_VALUE);
    GridPane.setHgrow(userTextField, Priority.ALWAYS);
    GridPane.setFillWidth(userTextField, true);

    ValidationSupport validationSupport = new ValidationSupport();
    validationSupport.setValidationDecorator(VALIDATION_DECORATION);
    validationSupport.registerValidator(userTextField, new StringValidator());

    // -- label
    Label userLabel = new Label();
    userLabel.setPrefWidth(Region.USE_COMPUTED_SIZE);
    userLabel.setText(CommonResource.getString("ip_input_dialog.user"));

    gridControls.add(new Pair<>(userLabel, userTextField));
    validations.add(validationSupport);
    return userTextField;
  }

  private TextField initPwd(String defaultPwd) {
    // -- textfield
    TextField pwdTextField = new PasswordField();
    pwdTextField.setText(defaultPwd);
    pwdTextField.setId("pwd-input");
    pwdTextField.setMaxWidth(Double.MAX_VALUE);
    GridPane.setHgrow(pwdTextField, Priority.ALWAYS);
    GridPane.setFillWidth(pwdTextField, true);

    ValidationSupport validationSupport = new ValidationSupport();
    validationSupport.setValidationDecorator(VALIDATION_DECORATION);
    validationSupport.registerValidator(pwdTextField, new StringValidator());

    // -- label
    Label pwdLabel = new Label();
    pwdLabel.setPrefWidth(Region.USE_COMPUTED_SIZE);
    pwdLabel.setText(CommonResource.getString("ip_input_dialog.password"));

    gridControls.add(new Pair<>(pwdLabel, pwdTextField));
    validations.add(validationSupport);
    return pwdTextField;
  }

  private CheckBox initRemember(boolean remember) {
    CheckBox rememberCheckBox = new CheckBox();
    rememberCheckBox.setText(CommonResource.getString("ip_input_dialog.remember"));
    rememberCheckBox.setSelected(remember);
    gridControls.add(new Pair<>(new Region(), rememberCheckBox));
    return rememberCheckBox;
  }

  private TextField initPort(int defaultPort) {
    //
    TextField portTextField = new TextField(defaultPort + "");
    GridPane.setHgrow(portTextField, Priority.ALWAYS);
    GridPane.setFillWidth(portTextField, true);
    UnaryOperator<Change> integerFilter = change -> {
      String newText = change.getControlNewText();
      if (newText.matches("-?([1-9][0-9]*)?")) {
        return change;
      }
      return null;
    };

    portTextField.setTextFormatter(
        new TextFormatter<>(new IntegerStringConverter(), defaultPort, integerFilter));

    ValidationSupport validationSupport = new ValidationSupport();
    validationSupport.setValidationDecorator(VALIDATION_DECORATION);
    validationSupport.registerValidator(portTextField, new PortValidator());

    //
    Label portLabel = new Label();
    portLabel.setPrefWidth(Region.USE_COMPUTED_SIZE);
    portLabel.setText(CommonResource.getString("ip_input_dialog.port"));

    gridControls.add(new Pair<>(portLabel, portTextField));
    validations.add(validationSupport);

    return portTextField;
  }

  private void updateGrid() {
    grid.getChildren().clear();

    for (int i = 0; i < gridControls.size(); i++) {
      if (gridControls.get(i).getKey() != null) {
        grid.add(gridControls.get(i).getKey(), 0, i);
      }
      if (gridControls.get(i).getValue() != null) {
        grid.add(gridControls.get(i).getValue(), 1, i);
      }
    }

    getDialogPane().setContent(grid);
    if (((TextField) gridControls.get(0).getValue()).getText().equals("") 
        || !((TextField) gridControls.get(1).getValue()).getText().isEmpty()) {
      Platform.runLater(() -> gridControls.get(0).getValue().requestFocus());
    } else {
      Platform.runLater(() -> gridControls.get(1).getValue().requestFocus());
    }
  }

  static class IpValidator implements Validator<String> {
    @Override
    public ValidationResult apply(Control target, String value) {
      ValidationResult vd = new ValidationResult();
      if (!value.matches(IP_REGX)) {
        vd.addErrorIf(target, CommonResource.getString("ip_input_dialog.ip_format_error"), true);
      }
      return vd;
    }
  }

  static class StringValidator implements Validator<String> {

    @Override
    public ValidationResult apply(Control target, String value) {
      ValidationResult vd = new ValidationResult();
      if (value.length() == 0) {
        vd.addErrorIf(target, "too short", true);
      }
      return vd;
    }

  }

  static class PortValidator implements Validator<String> {
    @Override
    public ValidationResult apply(Control target, String str) {
      ValidationResult vd = new ValidationResult();
      try {
        Integer value = Integer.parseInt(str);
        if (value <= 0 || value >= 65536) {
          vd.addErrorIf(target, CommonResource.getString("ip_input_dialog.port_format_error"),
              true);
        }
      } catch (NumberFormatException exception) {
        vd.addErrorIf(target, CommonResource.getString("ip_input_dialog.port_format_error"), true);
      }
      return vd;
    }
  }
}
