package com.mc.common.validation.constraints;

import com.mc.common.validation.constraints.impl.StringFormatImpl;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import javax.validation.Constraint;
import javax.validation.Payload;

@Target({ElementType.METHOD, ElementType.FIELD, ElementType.ANNOTATION_TYPE,
  ElementType.CONSTRUCTOR, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Constraint(validatedBy = {StringFormatImpl.class})
public @interface StringFormat {
  
  /**
   * 获取校验错误的文本.
   * @return 校验文本
   */
  String message() default "{com.dooapp.fxform.constraint.Adaptable.message}";

  /**
   * Groups.
   * @return groups
   */
  Class<?>[] groups() default {};

  /**
   * Payload.
   * @return payloads
   */
  Class<? extends Payload>[] payload() default {};
  
  /**
   * 验证格式的正则表达式
   * @return 正则表达式
   */
  String format() default ".*";
}
