package com.mc.tool.framework.utility;

/**
 * .
 */
public class EntityUtility {

  public static final String MONITOR_TYPE = "monitor.";
  public static final String DEVICE_TYPE = "device.";
  public static final String CONNECT_TYPE = "connect.";
  public static final String CREATE_TYPE = "create.";

  public static boolean isMonitorType(String type) {
    return type.contains(MONITOR_TYPE);
  }

  public static boolean isDeviceType(String type) {
    return type.contains(DEVICE_TYPE);
  }

  public static boolean isConnectType(String type) {
    return type.contains(CONNECT_TYPE);
  }

  public static boolean isCreateType(String type) {
    return type.contains(CREATE_TYPE);
  }

  public static boolean isType(String sourceType, String targetType) {
    return sourceType.contains(targetType);
  }

}
