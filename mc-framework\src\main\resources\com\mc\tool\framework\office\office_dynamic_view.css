@import "../common.css";


#graphPanel {
    -fx-background-color: #f5f5f5;
}

#statusPanel {
    -fx-background-color: gray;
}


#mainContainer {
    -fx-border-insets: 10;
    -fx-border-size: 1;
    -fx-border-color: #cccccc;
}

#seperator {
    -fx-background-color: #cccccc;
}


#preview-btn {
    -fx-graphic: url("preview_normal.png");
}

#preview-btn:hover {
    -fx-graphic: url("preview_hover.png");
}

#preview-btn:selected {
    -fx-graphic: url("preview_hover.png");
}

#zoomin-btn {
    -fx-graphic: url("zoomin_normal.png");
}

#zoomin-btn:hover {
    -fx-graphic: url("zoomin_hover.png");
}

#zoomout-btn {
    -fx-graphic: url("zoomout_normal.png");
}

#zoomout-btn:hover {
    -fx-graphic: url("zoomout_hover.png");
}

#restore-btn {
    -fx-graphic: url("restore_normal.png");
}

#restore-btn:hover {
    -fx-graphic: url("restore_hover.png");
}

#graph-bottom-toolbar {
    -fx-spacing: 20;
    -fx-background-color: #fcfcfc;
}

.scroll-pane .viewport {
    -fx-background-color: #fcfcfc;
}

.list-view {
    -fx-padding: 0;
    -fx-background-color: white;
    -fx-background-insets: 0;
}

.list-view:focused {
    -fx-padding: 0;
    -fx-background-color: white;
    -fx-background-insets: 0;
    -fx-backgroudn-radius: 0;
}

.list-view > .virtual-flow > .corner {
    -fx-background-color: white;
    -fx-background-insets: 0;
}

.list-cell {
    -fx-cell-size: 32;
    -fx-text-fill: black;
    -fx-padding: 0;
}

.list-cell:even, .list-cell:odd {
    -fx-background-color: white;
}

.list-cell:even:hover, .list-cell:odd:hover {
    -fx-background-color: #e5e5e5;
}

.list-cell:even:selected, .list-cell:odd:selected {
    -fx-background-color: #cccccc;
    -fx-text-fill: black;
}

.list-view:focused > .virtual-flow > .clipped-container > .sheet > .list-cell:filled:selected:hover {
    -fx-background-color: #e5e5e5;
}

.titled-pane {
    -fx-padding: 0;

}

.titled-pane > *.content {
    -fx-background-color: transparent;
    -fx-background-insets: 0;
    -fx-padding: 0;
    -fx-border-width: 0;
}


.titled-pane .title {
    -fx-background-radius: 0;
    -fx-border-style: null;
    -fx-pref-height: 32;
    -fx-padding: 2 10 7 10;
}

.titled-pane:expanded .title {
    -fx-background-color: #d9d9d9;
}

.titled-pane:collapsed .title {
    -fx-background-color: white;
}

.titled-pane > .title > .arrow-button {
    -fx-padding: 8 11 7 0;

}

.titled-pane > .title > .arrow-button > .arrow {
    -fx-background-color: transparent;
    -fx-background-insets: 0;
    -fx-padding: 0;
    -fx-shape: null;
    -fx-background-image: url("arrow.png");
    -fx-background-position: center;
    -fx-background-repeat: no-repeat;
    -fx-pref-width: 8;
    -fx-pref-height: 8;
}
