package com.mc.tool.framework.systemedit.view;

import com.mc.common.control.TextWrapper;
import com.mc.graph.AbstractConnectorSkin;
import com.mc.graph.interfaces.Connector;
import com.mc.graph.interfaces.ConnectorIdentifier;
import com.mc.graph.util.LinkUtil;
import com.mc.tool.framework.systemedit.datamodel.SystemEditDefinition;
import java.io.IOException;
import java.net.URL;
import java.util.ResourceBundle;
import javafx.beans.binding.DoubleBinding;
import javafx.beans.binding.ObjectBinding;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.geometry.Pos;
import javafx.scene.Node;
import javafx.scene.Parent;
import javafx.scene.layout.HBox;
import javafx.scene.layout.Pane;
import javafx.scene.paint.Paint;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public final class MatrixConnectorSkin extends AbstractConnectorSkin implements Initializable {
  public static final int SKIN_WIDTH = 70;
  public static final int SKIN_HEIGHT = 20;
  public static final int LABEL_PADDING = 10;
  public static final int LABEL_WIDTH = 50;
  public static final int LABEL_HEIGHT = 15;
  private Pane node;
  @FXML
  private TextWrapper portIndex;

  private BooleanProperty leftAlignment;

  public MatrixConnectorSkin(Connector connector, Parent parent, Parent container) {
    super(connector, parent, container);
  }

  private BooleanProperty leftAlignmentProperty() {
    if (leftAlignment == null) {
      leftAlignment = new SimpleBooleanProperty(true);
    }
    return leftAlignment;
  }

  @Override
  public Node getNode() {
    return node;
  }

  protected void createNodeByCode() {
    node = new NoCssPane();
    node.setPrefWidth(SKIN_WIDTH);
    node.setMinWidth(SKIN_WIDTH);
    node.setPrefHeight(SKIN_HEIGHT);
    node.setMinHeight(SKIN_HEIGHT);

    portIndex = new TextWrapper(true);
    portIndex.setMinWidth(LABEL_WIDTH);
    portIndex.setMaxWidth(LABEL_WIDTH);
    portIndex.setPrefWidth(LABEL_WIDTH);
    portIndex.setMinHeight(LABEL_HEIGHT);
    portIndex.setMaxHeight(LABEL_HEIGHT);
    portIndex.setPrefHeight(LABEL_HEIGHT);
    portIndex.setAlignment(Pos.CENTER_RIGHT);
    portIndex.setLayoutY((SKIN_HEIGHT - LABEL_HEIGHT) / 2);
    node.getChildren().add(portIndex);

    initialize(null, null);
  }

  protected void createNodeByFxml() {
    FXMLLoader loader = new FXMLLoader(
        getClass().getResource("/com/mc/tool/framework/systemedit/matrix_connector_skin.fxml"));
    loader.setController(this);
    try {
      node = (HBox) loader.load();

    } catch (IOException exc) {
      log.warn("Can not load matrix_connector_skin.fxml", exc);
    }
  }

  @Override
  protected void initNode() {
    createNodeByCode();

    if (node != null) {
      node.setUserData(this);

    }

    updatePortIndexText();

    DoubleBinding xposBinding = new DoubleBinding() {
      {
        super.bind(getNode().parentProperty(), parent.layoutXProperty(),
            parent.translateXProperty(), getNode().layoutXProperty(),
            getNode().translateXProperty(), leftAlignmentProperty());
      }

      @Override
      protected double computeValue() {
        return LinkUtil.localToGrandParent(parent, container, getNode().getLayoutX()
                + getNode().getTranslateX()
                + (leftAlignmentProperty().getValue() ? 0 : SKIN_WIDTH),
            true);
      }
    };

    DoubleBinding yposBinding = new DoubleBinding() {
      {
        super.bind(getNode().parentProperty(), parent.layoutYProperty(),
            parent.translateYProperty(), getNode().layoutYProperty(),
            getNode().translateYProperty());

      }

      @Override
      protected double computeValue() {
        return LinkUtil.localToGrandParent(parent, container,
            getNode().getLayoutY() + getNode().getTranslateY() + SKIN_HEIGHT / 2,
            false);
      }
    };

    containerXposProperty.bind(xposBinding);
    containerYposProperty.bind(yposBinding);
  }

  @Override
  public void setConnector(Connector connector) {
    super.setConnector(connector);
    updatePortIndexText();
  }

  protected void updatePortIndexText() {
    if (portIndex != null && getConnector() != null) {
      ConnectorIdentifier id = getConnector().getId();
      portIndex.textProperty().bind(id.textProperty());
    }
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {
    portIndex.fillProperty().bind(new ObjectBinding<Paint>() {
      {
        super.bind(connector.highLightProperty());
      }

      @Override
      protected Paint computeValue() {
        if (connector.highLightProperty().get()) {
          if (connector.highLightColorProperty().get() != null
              && connector.highLightColorProperty().get().getMergedColor() != null) {
            return connector.highLightColorProperty().get().getMergedColor();
          } else {
            return SystemEditDefinition.LABEL_HIGHLIGHT_COLOR;
          }
        } else {
          return SystemEditDefinition.LABEL_NORMAL_COLOR;
        }
      }
    });

  }

  @Override
  public void destroy() {
    super.destroy();
    portIndex.fillProperty().unbind();
  }

  /**
   * Set the connetor at the left side of the matrix view.
   */
  public void setLeft() {
    leftAlignmentProperty().set(true);
    portIndex.setLayoutX(LABEL_PADDING);
    portIndex.setAlignment(Pos.CENTER_LEFT);
  }

  /**
   * Set the connector at the right side of the matrix view.
   */
  public void setRight() {
    leftAlignmentProperty().set(false);
    portIndex.setLayoutX(SKIN_WIDTH - LABEL_WIDTH - LABEL_PADDING);
    portIndex.setAlignment(Pos.CENTER_RIGHT);
  }

}
