package com.mc.common.validation.constraints;

import com.mc.common.validation.constraints.impl.StringLengthImpl;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import javax.validation.Constraint;
import javax.validation.Payload;

@Documented
@Constraint(validatedBy = {StringLengthImpl.class})
@Target({ElementType.METHOD, ElementType.FIELD, ElementType.ANNOTATION_TYPE,
    ElementType.CONSTRUCTOR, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
public @interface StringLength {
  
  /**
   * .
   */
  int min() default 0;

  /**
   * .
   */
  int max() default Integer.MAX_VALUE;

  /**
   * .
   */
  String message() default "{com.mc.common.validation.constraints.stringlength.message}";

  /**
   * .
   */
  Class<?>[] groups() default {};

  /**
   * .
   */
  Class<? extends Payload>[] payload() default {};

}
