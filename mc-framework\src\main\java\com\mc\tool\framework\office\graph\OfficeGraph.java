package com.mc.tool.framework.office.graph;

import com.mc.graph.McGraph;
import com.mc.graph.handler.KeyboardHandler;
import com.mc.graph.interfaces.CellSkin;
import com.mc.graph.interfaces.SkinFactory;

/**
 * .
 */
public class OfficeGraph extends McGraph {

  /**
   * 构造函数.
   */
  public OfficeGraph() {

  }

  @Override
  public void init() {
    super.init();
    KeyboardHandler handler = new KeyboardHandler(this);
    handler.install();
  }

  @Override
  public SkinFactory createSkinFactory() {
    return new OsSkinfactory();
  }

  @Override
  public void createCellBehavior(CellSkin cellSkin) {
    if (cellSkin == null) {
      return;
    }
    super.createCellBehavior(cellSkin);
  }

}
