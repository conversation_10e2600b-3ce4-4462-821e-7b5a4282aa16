#-*- coding=utf-8 -*-
import json
import os
command="mvn install:install-file -Dfile=%s -DgroupId=%s -DartifactId=%s -Dpackaging=jar -Dversion=%s"
command_with_pom="mvn install:install-file -Dfile=%s -DpomFile=%s"
jsonfile="./libs/libs.json"

file = open(jsonfile, 'r')
content = json.load(file)
libs = content[u'libs']

success = []
fail = []
skip = []
if len(libs) == 0:
	print "No lib to install"
else:
	for lib in libs:
		file_path = u'./libs/' + lib[u'file']
		group=lib.get(u'group')
		artifact=lib.get(u'artifact')
		version=lib.get(u'version')
		mvn_ver = lib.get(u'mvn_ver')
		pom = lib.get(u'pom')
		if mvn_ver is not None:
			skip.append(lib[u'file'])
			continue;
		cmd = ''
		if pom is not None:
			pom_path = u'./libs/' + pom
			cmd = command_with_pom%(file_path, pom_path)
		else:
			cmd = command%(file_path,group,artifact,version)
		result = os.system(cmd)
		if result == 0:
			success.append(lib[u'file'])
		else:
			fail.append(lib[u'file'])
	print ''
	print "Successful List:"
	for item in success:
		print '\t' + item
	print "Fail List:"
	for item in fail:
		print '\t' + item
	print "Skip List:"
	for item in skip:
		print '\t' + item
file.close()