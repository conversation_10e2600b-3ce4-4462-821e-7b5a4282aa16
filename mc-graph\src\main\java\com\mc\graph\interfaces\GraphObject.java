package com.mc.graph.interfaces;

import com.mc.graph.connector.ConnectorIntegration;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.ObjectProperty;

public interface GraphObject {
  BooleanProperty visibleProperty();
  
  BooleanProperty highLightProperty();
  
  BooleanProperty hoverProperty();
  
  ObjectProperty<ColorComponent> highLightColorProperty();
  
  /**
   * 获取关联的connector的融合，方便绑定hightlight与color的属性. 
   */
  ConnectorIntegration getConnectorIntegration();
  
  void destroy();
}
