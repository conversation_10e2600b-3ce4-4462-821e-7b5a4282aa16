package com.mc.tool.framework.operation.view;

import com.mc.tool.framework.systemedit.datamodel.VisualEditNode;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import com.mc.tool.framework.utility.TargetDeviceLogoBinding;
import javafx.scene.control.TreeCell;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;
import javafx.scene.input.ClipboardContent;
import javafx.scene.input.Dragboard;
import javafx.scene.input.MouseEvent;
import javafx.scene.input.TransferMode;

/**
 * .
 */
public class VideoSourceTreeCell extends TreeCell<VisualEditNode> {

  @Override
  protected void updateItem(VisualEditNode item, boolean empty) {
    super.updateItem(item, empty);
    if (!empty) {
      textProperty().bind(item.nameProperty());
      if (item instanceof VisualEditTerminal) {
        VisualEditTerminal terminal = (VisualEditTerminal) item;
        graphicProperty().bind(new TargetDeviceLogoBinding(terminal));
        this.setStyle("-fx-font-weight: normal;");
      } else {
        graphicProperty().unbind();
        setGraphic(null);
        this.setStyle("-fx-font-weight: bold;");
      }
      setOnDragDetected((event) -> onDragDetected(event));
    } else {
      graphicProperty().unbind();
      setGraphic(null);
      textProperty().unbind();
      setText(null);
    }
  }

  protected void onDragDetected(MouseEvent event) {
    if (getItem() != null && getItem() instanceof VisualEditTerminal) {
      Dragboard dbDragboard = this.startDragAndDrop(TransferMode.ANY);
      ClipboardContent content = new ClipboardContent();
      content.putString(getItem().getGuid());
      Image image = getDragImage();
      dbDragboard.setDragView(image, image.getWidth() / 2, image.getHeight() / 2);
      dbDragboard.setContent(content);
    }
    event.consume();
  }

  protected Image getDragImage() {
    return ((ImageView) this.getGraphic()).getImage();
  }
}
