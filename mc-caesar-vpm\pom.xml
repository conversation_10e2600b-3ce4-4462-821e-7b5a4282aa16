<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>com.mc.tool.caesar</groupId>
    <artifactId>mc-caesar-build</artifactId>
    <version>${revision}</version>
  </parent>
  <artifactId>mc-caesar-vpm</artifactId>
  <packaging>jar</packaging>

  <name>mc-caesar-vpm</name>
  <url>https://maven.apache.org</url>
  <description>Caesar VPM</description>
  <inceptionYear>2017</inceptionYear>
  <organization>
    <name>mediacomm</name>
  </organization>

  <properties>
    <checkstyle.violation.ignore>LineLength</checkstyle.violation.ignore>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <maven.jxr.version>3.1.1</maven.jxr.version>
    <maven.checkstyle.version>3.1.2</maven.checkstyle.version>
    <maven.pmd.version>3.20.0</maven.pmd.version>
    <maven.findbugs.version>3.0.5</maven.findbugs.version>
    <lombok.version>1.18.26</lombok.version>
    <slf4j.version>2.0.7</slf4j.version>
    <maven.compiler.version>3.9.0</maven.compiler.version>
    <junit.version>4.13.2</junit.version>
    <commons.compress.version>1.22</commons.compress.version>
    <proguard.version>7.2.2</proguard.version>
    <raw.jar>${project.artifactId}-${project.version}.jar</raw.jar>
    <obfuscated.jar>${project.artifactId}-${project.version}-obfuscated.jar</obfuscated.jar>
    <final.file>CaesarVPM-v${project.version}.exe</final.file>
    <itext.asian.version>5.2.0</itext.asian.version>
    <itextpdf.version>5.5.13.3</itextpdf.version>
    <joda.time.version>2.10.14</joda.time.version>
    <easyexcel.version>3.2.1</easyexcel.version>
    <jfoenix.version>8.0.10</jfoenix.version>
    <hutool.version>5.8.29</hutool.version>
    <feign-version>13.2.1</feign-version>
    <feign-form-version>3.8.0</feign-form-version>
    <jackson-version>2.16.1</jackson-version>
    <jackson-databind-version>2.16.1</jackson-databind-version>
    <jackson-databind-nullable-version>0.2.6</jackson-databind-nullable-version>
    <oltu-version>1.0.2</oltu-version>
  </properties>
  <reporting>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jxr-plugin</artifactId>
        <version>${maven.jxr.version}</version>
      </plugin>
    </plugins>
  </reporting>
  <build>
    <plugins>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>templating-maven-plugin</artifactId>
        <version>3.0.0</version>
        <executions>
          <execution>
            <id>filter-src</id>
            <goals>
              <goal>filter-sources</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>${maven.compiler.version}</version>
        <configuration>
          <source>1.8</source>
          <target>1.8</target>
          <encoding>utf-8</encoding>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-checkstyle-plugin</artifactId>
        <version>${maven.checkstyle.version}</version>
        <dependencies>
          <dependency>
            <groupId>com.puppycrawl.tools</groupId>
            <artifactId>checkstyle</artifactId>
            <version>9.0.1</version>
          </dependency>
        </dependencies>
        <configuration>
          <configLocation>../google_checks9.0.1.xml</configLocation>
          <encoding>UTF-8</encoding>
          <failsOnError>true</failsOnError>
          <failOnViolation>true</failOnViolation>
          <violationSeverity>info</violationSeverity>
        </configuration>
        <executions>
          <execution>
            <id>checkstyle</id>
            <phase>validate</phase>
            <goals>
              <goal>check</goal>
            </goals>
            <configuration>
              <failOnViolation>true</failOnViolation>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-pmd-plugin</artifactId>
        <version>${maven.pmd.version}</version>
        <configuration>
          <analysisCache>true</analysisCache>
          <linkXRef>true</linkXRef>
        </configuration>
        <executions>
          <execution>
            <id>pmd</id>
            <phase>compile</phase>
            <goals>
              <goal>check</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>findbugs-maven-plugin</artifactId>
        <version>${maven.findbugs.version}</version>
        <configuration>
          <effort>Max</effort>
          <threshold>Low</threshold>
          <xmlOutput>true</xmlOutput>
          <excludeFilterFile>findbugs-exclude.xml</excludeFilterFile>
        </configuration>
        <executions>
          <execution>
            <phase>compile</phase>
            <goals>
              <goal>check</goal>
            </goals>
          </execution>
        </executions>
      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-shade-plugin</artifactId>
        <version>3.4.1</version>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>shade</goal>
            </goals>
            <configuration>
              <transformers>
                <transformer implementation="io.github.edwgiz.log4j.maven.plugins.shade.transformer.Log4j2PluginCacheFileTransformer"/>
                <transformer implementation="org.apache.maven.plugins.shade.resource.ManifestResourceTransformer">
                  <mainClass>com.mc.tool.caesar.vpm.CaesarApp</mainClass>
                </transformer>
              </transformers>
            </configuration>
          </execution>
        </executions>
        <dependencies>
          <dependency>
            <groupId>io.github.edwgiz</groupId>
            <artifactId>log4j-maven-shade-plugin-extensions</artifactId>
            <version>2.20.0</version>
          </dependency>
        </dependencies>
      </plugin>
      <plugin>
        <groupId>com.github.wvengen</groupId>
        <artifactId>proguard-maven-plugin</artifactId>
        <version>2.6.0</version>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>proguard</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <proguardVersion>${proguard.version}</proguardVersion>
          <injar>${raw.jar}</injar>
          <outjar>${obfuscated.jar}</outjar>
          <libs>
            <lib>${java.home}/lib/rt.jar</lib>
            <lib>${java.home}/lib/ext/jfxrt.jar</lib>
          </libs>
          <proguardInclude>proguard.conf</proguardInclude>
        </configuration>
        <dependencies>
          <dependency>
            <groupId>com.guardsquare</groupId>
            <artifactId>proguard-base</artifactId>
            <version>${proguard.version}</version>
            <scope>runtime</scope>
          </dependency>
        </dependencies>
      </plugin>
      <plugin>
        <groupId>com.akathist.maven.plugins.launch4j</groupId>
        <artifactId>launch4j-maven-plugin</artifactId>
        <version>2.3.3</version>
        <executions>

          <!-- GUI exe -->
          <execution>
            <id>l4j-gui</id>
            <phase>package</phase>
            <goals>
              <goal>launch4j</goal>
            </goals>
            <configuration>
              <headerType>gui</headerType>
              <outfile>target/${final.file}</outfile>
              <jar>target/${obfuscated.jar}</jar>
              <errTitle>App Err</errTitle>
              <classPath>
                <mainClass>com.mc.tool.caesar.vpm.CaesarApp</mainClass>
              </classPath>
              <icon>src/main/resources/icons/Logo.ico</icon>
              <downloadUrl>https://bell-sw.com/pages/downloads/#downloads</downloadUrl>
              <priority>high</priority>
              <jre>
                <path>jre</path>
                <minVersion>1.8.0_191</minVersion>
                <maxVersion>1.9.0</maxVersion>
                <opts>
                  <opt>-XX:MaxRAMPercentage=25.0</opt>
                </opts>
              </jre>
              <versionInfo>
                <fileVersion>*******</fileVersion>
                <txtFileVersion>*******</txtFileVersion>
                <fileDescription>CaesarVPM</fileDescription>
                <copyright>C</copyright>
                <productVersion>*******</productVersion>
                <txtProductVersion>*******</txtProductVersion>
                <productName>Caesar</productName>
                <internalName>Caesar</internalName>
                <originalFilename>${final.file}</originalFilename>
              </versionInfo>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
  <dependencies>
    <dependency>
      <groupId>joda-time</groupId>
      <artifactId>joda-time</artifactId>
      <version>${joda.time.version}</version>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>${junit.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.testfx</groupId>
      <artifactId>testfx-core</artifactId>
      <version>4.0.16-alpha</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.testfx</groupId>
      <artifactId>testfx-junit</artifactId>
      <version>4.0.15-alpha</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.testfx</groupId>
      <artifactId>openjfx-monocle</artifactId>
      <version>8u76-b04</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <version>3.1.0</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.mc</groupId>
      <version>${mc.framework.version}</version>
      <artifactId>mc-framework</artifactId>
    </dependency>
    <dependency>
      <groupId>com.mc.tool.caesar</groupId>
      <artifactId>mc-caesar-api</artifactId>
      <version>${revision}</version>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-compress</artifactId>
      <version>${commons.compress.version}</version>
    </dependency>
    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>easyexcel</artifactId>
      <version>${easyexcel.version}</version>
    </dependency>
    <dependency>
      <groupId>com.jfoenix</groupId>
      <artifactId>jfoenix</artifactId>
      <version>${jfoenix.version}</version>
    </dependency>
    <dependency>
      <groupId>cn.hutool</groupId>
      <artifactId>hutool-http</artifactId>
      <version>${hutool.version}</version>
    </dependency>
    <!-- HTTP client: Netflix Feign -->
    <dependency>
      <groupId>io.github.openfeign</groupId>
      <artifactId>feign-core</artifactId>
      <version>${feign-version}</version>
    </dependency>
    <dependency>
      <groupId>io.github.openfeign</groupId>
      <artifactId>feign-jackson</artifactId>
      <version>${feign-version}</version>
    </dependency>
    <dependency>
      <groupId>io.github.openfeign</groupId>
      <artifactId>feign-slf4j</artifactId>
      <version>${feign-version}</version>
    </dependency>
    <dependency>
      <groupId>io.github.openfeign.form</groupId>
      <artifactId>feign-form</artifactId>
      <version>${feign-form-version}</version>
    </dependency>

    <!-- JSON processing: jackson -->
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-core</artifactId>
      <version>${jackson-version}</version>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-annotations</artifactId>
      <version>${jackson-version}</version>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-databind</artifactId>
      <version>${jackson-databind-version}</version>
    </dependency>
    <dependency>
      <groupId>org.openapitools</groupId>
      <artifactId>jackson-databind-nullable</artifactId>
      <version>${jackson-databind-nullable-version}</version>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.datatype</groupId>
      <artifactId>jackson-datatype-jsr310</artifactId>
      <version>${jackson-version}</version>
    </dependency>
  </dependencies>
</project>
