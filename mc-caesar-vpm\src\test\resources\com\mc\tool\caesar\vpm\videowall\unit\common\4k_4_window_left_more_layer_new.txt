osd:[t(200) l(200) w(80) h(400) c(a4ff) bc(80000000) a(63) id(0x0 0x1 0x0 0x0 0x0 0x0 0x0 0x0)]
input(iw ih ow oh):[0(1920 2160 1900 100) 1(1920 2160 1900 100) 2(1920 2160 1900 100) 3(0 0 0 0) 4(1920 2160 1900 100) 5(1920 2160 1900 100) 6(1920 2160 1900 100) 7(1920 2160 1900 100) ]
vert_cut(start_line, video_src):[0(0 0) 1(0 1) 2(0 2) 3(0 4) 4(0 5) 5(0 6) 6(0 7) 7(0 0) 8(0 0) 9(0 0) 10(0 0) 11(0 0) 12(0 0) 13(0 0) 14(0 0) 15(0 0) 16(0 0) 17(0 0) 18(0 0) 19(0 0) ]
horz_cut(start_px, end_px, horz_cut_cnt, vert_cut_index):[0(0 1899 0 0) 1(0 1899 0 1) 2(0 1899 0 2) 3(0 19 1 3) 4(20 1899 1 3) 5(0 19 1 4) 6(20 1899 1 4) 7(0 19 1 5) 8(20 1899 1 5) 9(20 1899 0 6) 10(0 0 0 20) 11(0 0 0 20) 12(0 0 0 20) 13(0 0 0 20) 14(0 0 0 20) 15(0 0 0 20) 16(0 0 0 20) 17(0 0 0 20) 18(0 0 0 20) 19(0 0 0 20) ]
output(oport olayer iw ih ow oh):[0(0 0 1900 100 1900 100) 1(0 2 1900 100 1900 100) 2(0 4 1900 100 1900 100) 3(0 1 20 100 20 100) 4(1 0 1880 100 1880 100) 5(0 3 20 100 20 100) 6(1 1 1880 100 1880 100) 7(0 5 20 100 20 100) 8(1 2 1880 100 1880 100) 9(1 3 1880 100 1880 100) 10(0 6 0 0 0 0) 11(0 6 0 0 0 0) 12(0 6 0 0 0 0) 13(0 6 0 0 0 0) 14(0 6 0 0 0 0) 15(0 6 0 0 0 0) 16(0 6 0 0 0 0) 17(0 6 0 0 0 0) 18(0 6 0 0 0 0) 19(0 6 0 0 0 0) ]
layer(start_line start_px w h a):10[0(0 0 1900 100 127) 1(0 1900 20 100 127) 2(100 0 1900 100 127) 3(100 1900 20 100 127) 4(200 0 1900 100 127) 5(200 1900 20 100 127) 6(0 0 1880 100 127) 7(100 0 1880 100 127) 8(200 0 1880 100 127) 9(300 0 1880 100 127) ]
