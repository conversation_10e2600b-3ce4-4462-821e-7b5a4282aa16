package com.mc.tool.framework.utility;

import com.google.gson.TypeAdapter;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import com.mc.common.collections.ObservableListWrapperEx;
import java.io.IOException;
import java.util.ArrayList;

/**
 * ObservableListWrapperExTypeAdapter.
 */
public class ObservableListWrapperExTypeAdapter<T> extends TypeAdapter<ObservableListWrapperEx<T>> {
  private final TypeAdapter<T> delegate;

  public ObservableListWrapperExTypeAdapter(TypeAdapter<T> delegate) {
    this.delegate = delegate;
  }

  @Override
  public void write(JsonWriter out, ObservableListWrapperEx<T> value) throws IOException {
    out.beginArray();
    for (T item : value) {
      delegate.write(out, item);
    }
    out.endArray();
  }

  @Override
  public ObservableListWrapperEx<T> read(<PERSON><PERSON>Reader in) throws IOException {
    ObservableListWrapperEx<T> result = new ObservableListWrapperEx<>(new ArrayList<>());
    in.beginArray();
    while (in.hasNext()) {
      result.add(delegate.read(in));
    }
    in.endArray();
    return result;
  }

}
