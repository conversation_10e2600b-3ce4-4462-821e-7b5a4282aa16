/* 
    Document   : Undecorator
    Created on : Dec 19, 2014
    Author     : In-SideFX
    Description:
        Demo app purpose
*/
.logo{
    -fx-image:url("./in-sidefx.png");
}
.logo-label{
    -fx-font-weight:bold;
    -fx-text-fill: black;
}
.clientarea-background{
    -fx-background-color: white;
}

.decoration-background{
    -fx-stroke-width: 0;
    -fx-arc-width:5px;
    -fx-arc-height:5px;
}
/*
    Defines the rectangle responsible for the shadow
*/
.decoration-shadow{
    -fx-fill: black;
    -fx-stroke-width: 0;
    -fx-arc-width:20px;
    -fx-arc-height:20px;
}
/*
 Used to declare the definition of stage resize zone
*/
.decoration-resize{
    -fx-fill:null;  /* avoid mouse events in the middle*/
    -fx-border-color: transparent;
    -fx-border-width: 7;
    -fx-border-insets: 0;
}
