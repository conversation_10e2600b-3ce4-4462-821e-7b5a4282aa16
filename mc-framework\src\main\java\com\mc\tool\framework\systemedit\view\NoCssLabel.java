package com.mc.tool.framework.systemedit.view;

import java.util.ArrayList;
import java.util.List;
import javafx.beans.value.WritableValue;
import javafx.scene.control.Label;
import javafx.scene.text.Font;

/**
 * .
 */
public class NoCssLabel extends Label {
  private static Font labelFont = new Font("Microsoft YaHei", 12);

  public NoCssLabel() {
    skinProperty().set(createDefaultSkin());
    setFont(labelFont);
  }

  @Override
  public List<String> impl_getAllParentStylesheets() {
    return new ArrayList<>();
  }

  @Override
  protected void impl_processCSS(WritableValue<Boolean> unused) {
  }
}
