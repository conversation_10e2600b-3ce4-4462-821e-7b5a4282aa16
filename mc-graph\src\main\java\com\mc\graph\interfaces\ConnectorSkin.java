package com.mc.graph.interfaces;

import javafx.beans.property.BooleanProperty;
import javafx.beans.property.DoubleProperty;
import javafx.scene.Node;

public interface ConnectorSkin extends Skin {
  DoubleProperty getContainerXposProperty();
  
  DoubleProperty getContainerYposProperty();
  
  BooleanProperty linkableProperty();
  
  void setConnector(Connector connector);
  
  Connector getConnector();
  
  Node getNode();
  
  void addBehavior(ConnectorBehavior behavior);
  
}
