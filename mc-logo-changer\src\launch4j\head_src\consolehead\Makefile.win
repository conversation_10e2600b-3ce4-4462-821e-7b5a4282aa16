# Project: consolehead
# Makefile created by Dev-C++ 4.9.9.2

CPP  = g++.exe
CC   = gcc.exe
WINDRES = windres.exe
RES  = 
OBJ  = ../../head/consolehead.o ../../head/head.o $(RES)
LINKOBJ  = ../../head/consolehead.o ../../head/head.o $(RES)
LIBS =  -L"C:/Dev-Cpp/lib" -n  -s 
INCS =  -I"C:/Dev-Cpp/include" 
CXXINCS =  -I"C:/Dev-Cpp/lib/gcc/mingw32/3.4.2/include"  -I"C:/Dev-Cpp/include/c++/3.4.2/backward"  -I"C:/Dev-Cpp/include/c++/3.4.2/mingw32"  -I"C:/Dev-Cpp/include/c++/3.4.2"  -I"C:/Dev-Cpp/include" 
BIN  = consolehead.exe
CXXFLAGS = $(CXXINCS)   -fexpensive-optimizations -O3
CFLAGS = $(INCS)   -fexpensive-optimizations -O3
RM = rm -f

.PHONY: all all-before all-after clean clean-custom

all: all-before consolehead.exe all-after


clean: clean-custom
	${RM} $(OBJ) $(BIN)

$(BIN): $(OBJ)
#	$(CC) $(LINKOBJ) -o "consolehead.exe" $(LIBS)

../../head/consolehead.o: consolehead.c
	$(CC) -c consolehead.c -o ../../head/consolehead.o $(CFLAGS)

../../head/head.o: ../head.c
	$(CC) -c ../head.c -o ../../head/head.o $(CFLAGS)
