package com.mc.tool.caesar.vpm.util;

import com.mc.common.control.IpInputDialog;
import com.mc.common.control.IpInputInfo;
import com.mc.common.io.ResourceFile;
import com.mc.common.util.PlatformUtility;
import com.mc.tool.caesar.api.CaesarConstants;
import com.mc.tool.caesar.api.CaesarConstants.User;
import com.mc.tool.caesar.api.CaesarSwitchDataModel;
import com.mc.tool.caesar.api.datamodel.LoginResponse;
import com.mc.tool.caesar.api.exception.BusyException;
import com.mc.tool.caesar.api.exception.ConfigException;
import com.mc.tool.caesar.api.utils.CfgError;
import com.mc.tool.caesar.vpm.CaesarAuthorityEntity;
import com.mc.tool.caesar.vpm.CaesarEntity;
import com.mc.tool.caesar.vpm.device.CaesarDeviceFactory;
import com.mc.tool.caesar.vpm.devices.CaesarDeviceController;
import com.mc.tool.framework.interfaces.ApplicationBase;
import com.mc.tool.framework.interfaces.Entity;
import com.mc.tool.framework.systemedit.view.ViewUtility;
import com.mc.tool.framework.utility.SymmetricEncryptionUtils;
import com.mc.tool.framework.utility.UndecoratedAlert;
import com.mc.tool.framework.utility.UndecoratedHeavyWeightDialog;
import java.io.File;
import java.nio.charset.StandardCharsets;
import java.text.MessageFormat;
import java.util.Objects;
import java.util.function.Consumer;
import javafx.application.Platform;
import javafx.concurrent.Task;
import javafx.scene.control.AlertEx.AlertExType;
import javafx.scene.control.FxDialogEx;
import javafx.stage.Modality;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class ConnectDeviceUtility {
  private ConnectDeviceUtility() {
  }

  public static final String DEMO_IP = "s0.0.0.0".substring(1);
  private static final String DEMO_STATUS_PATH = "com/mc/tool/caesar/vpm/demo/demo.status";

  /**
   * 连接到设备.
   *
   * @param app applicationbase
   * @param postAction 连接后的action
   */
  public static void connectToDevice(ApplicationBase app, Consumer<Entity> postAction, String ip) {
    String defaultUser = "";
    String defaultPwd = "";
    if (app.getConfigBean().authInfo.isRemember()) {
      if (Objects.equals(app.getConfigBean().authInfo.getIp(), ip)) {
        defaultUser = app.getConfigBean().authInfo.getUser();
        defaultPwd = SymmetricEncryptionUtils.decrypt(
            app.getConfigBean().authInfo.getPwd());
      } else if (ip.isEmpty()) {
        ip = app.getConfigBean().authInfo.getIp();
        defaultUser = app.getConfigBean().authInfo.getUser();
        defaultPwd = SymmetricEncryptionUtils.decrypt(
            app.getConfigBean().authInfo.getPwd());
      }
    }
    IpInputDialog ipInputDialog =
        new IpInputDialog(ip, defaultUser, defaultPwd, app.getConfigBean().authInfo.isRemember()) {

          @Override
          protected FxDialogEx createDialog() {
            return new UndecoratedHeavyWeightDialog(this);
          }
        };
    ipInputDialog.setHeaderText(null);
    ipInputDialog.initOwner(app.getMainWindow());
    ipInputDialog.initModality(Modality.APPLICATION_MODAL);
    ipInputDialog.showAndWait();
    IpInputInfo address = ipInputDialog.getResult();
    if (address == null) {
      log.warn("Empty address!");
      return;
    }

    byte[] username = new byte[CaesarConstants.NAME_BYTE_LEN];
    byte[] user;
    user = address.getUser().getBytes(StandardCharsets.UTF_8);
    if (user.length > CaesarConstants.NAME_BYTE_LEN) {
      log.warn("Fail to connect");
      loginAlert(app, postAction);
      return;
    }
    System.arraycopy(user, 0, username, 0, user.length);
    byte[] password = new byte[20];
    byte[] pwd;
    pwd = address.getPwd().getBytes(StandardCharsets.UTF_8);
    if (pwd.length > 20) {
      log.warn("Fail to connect");
      loginAlert(app, postAction);
      return;
    }
    System.arraycopy(pwd, 0, password, 0, pwd.length);
    if (address.getIp().equals(DEMO_IP)) {
      log.info("login: demo");
      loadFile(app, DEMO_STATUS_PATH, postAction, address);
    } else {
      log.info("login, ip: {}, user: {}.", address.getIp(), new String(user, StandardCharsets.UTF_8));
      connectToDeviceImpl(app, postAction, address, username, password, address.isRemember());
    }
  }

  protected static void connectToDeviceImpl(
      ApplicationBase app,
      Consumer<Entity> postAction,
      IpInputInfo address,
      byte[] username,
      byte[] password,
      boolean remember) {
    String hostName = address.getIp();
    String str = CaesarResources.getString("connect_device.connecting");
    final String message = MessageFormat.format(str, hostName);
    Task<CaesarDeviceController> task =
        new Task<CaesarDeviceController>() {

          @Override
          protected CaesarDeviceController call() throws Exception {
            this.updateMessage(message);
            CaesarDeviceController result = null;
            try {
              result =
                  CaesarDeviceFactory.createDevice(
                      address.getIp(), address.getUser(), address.getPwd());
              final CaesarSwitchDataModel model = result.getDataModel();
              LoginResponse lr = result.getDataModel().login(username, password);
              if (lr != LoginResponse.SUCCESS) {
                log.warn("Fail to connect!reason:" + lr.name());
                throw new Exception("Fail to connect!reason:" + lr.name());
              }
              String loginUser = result.getLoginUser();
              //保存登录信息
              if (remember) {
                app.getConfigBean().setAuthInfo(address.getIp(), address.getUser(),
                    SymmetricEncryptionUtils.encrypt(address.getPwd()), true);
              } else {
                app.getConfigBean().setAuthInfo("", "", "", false);
              }
              result
                  .submit(
                      () -> {
                        try {
                          if (User.ADMINISTRATOR_NAME.equals(loginUser)) {
                            model.reloadConfigData();
                          } else {
                            model.reloadAll();
                          }
                        } catch (ConfigException exc) {
                          log.warn("[ConfigException] Fail to reload all.", exc);
                          if (exc.getError() == CfgError.VERSION) {
                            onVersionError(app, postAction, exc.getMessage());
                          }
                        } catch (BusyException exc) {
                          log.warn("[BusyException] Fail to reload all.", exc);
                        } catch (Exception exc) {
                          log.warn("[Exception] Fail to reload all.", exc);
                        }
                      })
                  .get();
            } catch (ConfigException | BusyException | RuntimeException exception) {
              log.warn("Fail to reload all.", exception);
              if (exception instanceof ConfigException
                  && ((ConfigException) exception).getError() == CfgError.VERSION) {
                onVersionError(app, postAction, exception.getMessage());
                return null;
              }
            }
            if (result != null) {
              if (result.getDataModel().isHwVersionIncompatible()) {
                PlatformUtility.runInFxThreadLater(() -> ViewUtility.showAlert(
                    null, CaesarI18nCommonResource.getString("check.matrix_version"),
                    AlertExType.ERROR));
                log.warn("HW version incompatible.");
              }
              log.info("Create device controller successfully.");
              return result;
            } else {
              Platform.runLater(
                  () -> {
                    UndecoratedAlert connectError = new UndecoratedAlert(AlertExType.ERROR);
                    connectError.initOwner(app.getMainWindow());
                    connectError.setContentText(
                        MessageFormat.format(
                            CaesarResources.getString("connect_device.error.message"), hostName));
                    connectError.setHeaderText(null);
                    connectError.setTitle(CaesarResources.getString("connect_device.error.title"));
                    connectError.showAndWait();
                    postAction.accept(null);
                  });
            }
            return null;
          }
        };

    task.setOnFailed((event) -> loginAlert(app, postAction));

    task.setOnSucceeded(
        (state) -> {
          if (task.getValue() != null) {
            if (User.ADMINISTRATOR_NAME.equals(task.getValue().getLoginUser())) {
              postAction.accept(new CaesarAuthorityEntity(task.getValue()));
            } else {
              postAction.accept(new CaesarEntity(task.getValue()));
            }
          } else {
            postAction.accept(null);
          }
        });

    app.getTaskManager().addForegroundTask(task);
  }

  private static void loginAlert(ApplicationBase app, Consumer<Entity> postAction) {
    Platform.runLater(
        () -> {
          UndecoratedAlert connectError = new UndecoratedAlert(AlertExType.ERROR);
          connectError.initOwner(app.getMainWindow());
          connectError.setContentText(
              CaesarResources.getString("connect_device.wrong_username_password"));
          connectError.setHeaderText(null);
          connectError.setTitle(CaesarResources.getString("connect_device.error.title"));
          connectError.showAndWait();
          postAction.accept(null);
        });
  }

  private static void onVersionError(
      ApplicationBase app, Consumer<Entity> postAction, String errorMsg) {
    Platform.runLater(
        () -> {
          UndecoratedAlert connectError = new UndecoratedAlert(AlertExType.ERROR);
          connectError.initOwner(app.getMainWindow());
          connectError.setContentText(
              MessageFormat.format(
                  CaesarResources.getString("connect_device.error_version"), errorMsg));
          connectError.setHeaderText(null);
          connectError.setTitle(CaesarResources.getString("connect_device.error.title"));
          connectError.showAndWait();
          postAction.accept(null);
        });
  }

  /**
   * load file.
   */
  public static void loadFile(
      ApplicationBase app, File file, Consumer<Entity> postAction, IpInputInfo address) {
    log.info(String.format("Load file, name:%s.", file.getName()));
    app.getTaskManager()
        .addForegroundTask(new LoadFileTask(app, ResourceFile.fromFile(file), postAction, address));
  }

  protected static void loadFile(
      ApplicationBase app, String resource, Consumer<Entity> postAction, IpInputInfo address) {
    app.getTaskManager()
        .addForegroundTask(
            new LoadFileTask(app, ResourceFile.fromResource(resource), postAction, address));
  }

  static class LoadFileTask extends Task<Void> {
    private final ResourceFile file;
    private final Consumer<Entity> postAction;
    private final ApplicationBase app;
    private final IpInputInfo inputInfo;

    public LoadFileTask(
        ApplicationBase app, ResourceFile file, Consumer<Entity> postAction, IpInputInfo address) {
      this.app = app;
      this.file = file;
      this.postAction = postAction;
      this.inputInfo = address;
    }

    @Override
    protected Void call() {
      try {
        String message =
            MessageFormat.format(
                CaesarI18nCommonResource.getString("connect_device.loading"), file.getName());
        updateMessage(message);
        CaesarDeviceController controller =
            CaesarDeviceFactory.createDevice(file, inputInfo.getUser(), inputInfo.getPwd());
        Platform.runLater(
            () -> {
              if (controller == null) {
                UndecoratedAlert alert = new UndecoratedAlert(AlertExType.WARNING);
                alert.initOwner(app.getMainWindow());
                alert.setContentText(
                    CaesarI18nCommonResource.getString("connect_device.load_error"));
                alert.showAndWait();
                return;
              }
              CaesarEntity entity = new CaesarEntity(controller);
              postAction.accept(entity);
            });
        return null;
      } catch (Exception exception) {
        log.warn("Fail to load file.", exception);
        return null;
      }
    }
  }
}
