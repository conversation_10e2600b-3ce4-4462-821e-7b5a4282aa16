package com.mc.tool.framework;

import java.util.List;
import lombok.Getter;

/**
 * .
 */
public class OemInfo {
  @Getter
  private String company = null;
  @Getter
  private String shortName = null;
  @Getter
  private String fullName = null;
  @Getter
  private String productName = null;
  @Getter
  private String copyright = null;
  @Getter
  private String versionAppend = null;
  @Getter
  private String title = null;
  @Getter
  private List<String> phones = null;

  /**
   * 获取矩阵默认名称.
   */
  public String getDefaultMatrixName() {
    if (shortName != null) {
      return shortName;
    } else {
      return "MC";
    }
  }
}
