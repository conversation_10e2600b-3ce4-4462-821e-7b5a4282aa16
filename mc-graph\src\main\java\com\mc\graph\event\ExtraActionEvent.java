package com.mc.graph.event;

import com.mc.graph.interfaces.CellObject;
import lombok.Getter;
import lombok.Setter;

import java.util.Collection;

public class ExtraActionEvent {
  @Getter @Setter
  private String actionId;
  
  @Getter @Setter
  private Collection<CellObject> actionTargets;
  
  public ExtraActionEvent(String actionId, Collection<CellObject> actionTargets) {
    this.actionId = actionId;
    this.actionTargets = actionTargets;
  }
}
