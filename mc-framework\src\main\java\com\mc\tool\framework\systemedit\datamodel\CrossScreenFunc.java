package com.mc.tool.framework.systemedit.datamodel;

import com.mc.common.beans.SimpleObservable;
import com.mc.tool.framework.operation.crossscreen.datamodel.CrossScreenObject;
import javafx.collections.ObservableList;
import javafx.util.Pair;

/**
 * .
 */
public abstract class CrossScreenFunc extends VisualEditFunc {

  public abstract CrossScreenObject getCrossScreenData();

  public abstract void moveScreen(int fromRow, int fromColumn, int toRow, int toColumn);

  public abstract boolean isScreenMovable();

  public abstract Pair<Integer, Integer> posOfScreen(VisualEditTerminal terminal);

  public abstract boolean addScenario(CrossScreenObject object);

  public abstract boolean removeScenario(CrossScreenObject object);

  public abstract ObservableList<? extends CrossScreenObject> getScenarios();

  public abstract SimpleObservable getResetObservable();

  @Override
  public String getNodeType() {
    return SystemEditDefinition.SEAT_CELL;
  }

  public abstract int getMaxRow();

  public abstract int getMaxColumn();

}
