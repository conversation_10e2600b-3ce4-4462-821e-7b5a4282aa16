<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>net.sf.launch4j</groupId>
	<artifactId>launch4j</artifactId>
	<version>${launch4j.version}</version>
	<name>Launch4j</name>
	<description>Cross-platform Java executable wrapper for creating lightweight Windows native EXEs. Provides advanced JRE search, application startup configuration and better user experience.</description>
	<url>http://sourceforge.net/projects/launch4j/</url>

	<licenses>
		<license>
			<name>BSD 3-Clause License</name>
			<url>http://opensource.org/licenses/BSD-3-Clause</url>
			<comments>Launch4j is licensed under the BSD 3-Clause License.</comments>
		</license>
		<license>
			<name>MIT License</name>
			<url>http://www.opensource.org/licenses/mit-license.php</url>
			<comments>Launch4j code (head) which is attached to the wrapped jars is licensed under the MIT License.</comments>
		</license>
	</licenses>

	<developers>
		<developer>
			<name>Grzegorz Kowal</name>
			<url>http://sourceforge.net/u/grzegok/profile/</url>
		</developer>
	</developers>

	<scm>
		<connection>scm:git:http://git.code.sf.net/p/launch4j/git</connection>
		<developerConnection>scm:git:http://git.code.sf.net/p/launch4j/git</developerConnection>
		<url>http://sourceforge.net/p/launch4j/git/ci/master/tree/</url>
	</scm>

	<distributionManagement>
		<snapshotRepository>
			<id>ossrh</id>
			<url>https://oss.sonatype.org/content/repositories/snapshots</url>
		</snapshotRepository>
	</distributionManagement>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<versionNumber>${launch4j.version}.0.0</versionNumber>
		<version>${launch4j.version}</version>
	</properties>

	<dependencies>
		<dependency>
			<groupId>commons-beanutils</groupId>
			<artifactId>commons-beanutils</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>commons-collections</artifactId>
					<groupId>commons-collections</groupId>
				</exclusion>
			</exclusions>
			<version>1.7.0</version>
		</dependency>
		<dependency>
			<groupId>commons-logging</groupId>
			<artifactId>commons-logging</artifactId>
			<version>1.2</version>
		</dependency>
		<dependency>
			<groupId>net.java.abeille</groupId>
			<artifactId>abeille</artifactId>
			<version>3.0</version>
			<exclusions>
				<exclusion>
					<artifactId>colt</artifactId>
					<groupId>colt</groupId>
				</exclusion>
				<exclusion>
					<artifactId>
						com.springsource.org.apache.batik.ext.awt
					</artifactId>
					<groupId>org.apache.batik</groupId>
				</exclusion>
				<exclusion>
					<artifactId>
						com.springsource.org.apache.batik.util
					</artifactId>
					<groupId>org.apache.batik</groupId>
				</exclusion>
				<exclusion>
					<artifactId>dom4j</artifactId>
					<groupId>dom4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>dsol-xml</artifactId>
					<groupId>dsol</groupId>
				</exclusion>
				<exclusion>
					<artifactId>javahelp</artifactId>
					<groupId>javax.help</groupId>
				</exclusion>
				<exclusion>
					<artifactId>jfreechart</artifactId>
					<groupId>jfree</groupId>
				</exclusion>
				<exclusion>
					<artifactId>jcommon</artifactId>
					<groupId>jfree</groupId>
				</exclusion>
				<exclusion>
					<artifactId>mahout-collections</artifactId>
					<groupId>org.apache.mahout</groupId>
				</exclusion>
				<exclusion>
					<artifactId>optimization</artifactId>
					<groupId>com.github.rwl</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>foxtrot</groupId>
			<artifactId>foxtrot</artifactId>
			<version>2.0</version>
		</dependency>
		<dependency>
			<groupId>com.jgoodies</groupId>
			<artifactId>jgoodies-common</artifactId>
			<version>1.7.0</version>
		</dependency>
		<dependency>
			<groupId>com.jgoodies</groupId>
			<artifactId>forms</artifactId>
			<version>1.2.1</version>
		</dependency>
		<dependency>
			<groupId>com.jgoodies</groupId>
			<artifactId>looks</artifactId>
			<version>2.2.2</version>
		</dependency>
		<dependency>
			<groupId>com.thoughtworks.xstream</groupId>
			<artifactId>xstream</artifactId>
			<version>1.4.8</version>
		</dependency>
		<dependency>
			<groupId>org.apache.ant</groupId>
			<artifactId>ant</artifactId>
			<version>1.8.2</version>
		</dependency>

	</dependencies>

	<build>
		<sourceDirectory>src</sourceDirectory>
		<resources>
			<resource>
				<directory>src</directory>
				<excludes>
					<exclude>**/*.java</exclude>
				</excludes>
			</resource>
		</resources>
		<plugins>
			<plugin>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.1</version>
				<configuration>
					<source>1.6</source>
					<target>1.6</target>
				</configuration>
			</plugin>
			<plugin>
				<groupId>com.github.taxone.plugins</groupId>
				<artifactId>classpath-maven-plugin</artifactId>
				<version>1.0.0</version>
				<executions>
					<execution>
						<id>set-classpath-property</id>
						<phase>prepare-package</phase>
						<goals>
							<goal>setClassPath</goal>
						</goals>
						<configuration>
							<classpathPrefix>lib/</classpathPrefix>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jar-plugin</artifactId>
				<version>2.5</version>
				<executions>
					<execution>
						<id>default-jar</id>
						<phase>package</phase>
						<goals>
							<goal>jar</goal>
						</goals>
						<configuration>
							<archive>
								<manifest>
									<mainClass>net.sf.launch4j.Main</mainClass>
								</manifest>
								<manifestEntries>
									<Class-Path>. ${taxone.classpath.propertyName}</Class-Path>
								</manifestEntries>
							</archive>
						</configuration>
					</execution>
					<execution>
						<id>core-jar</id>
						<phase>package</phase>
						<goals>
							<goal>jar</goal>
						</goals>
						<configuration>
							<finalName>${project.build.finalName}</finalName>
							<excludes>
								<exclude>net/sf/launch4j/form/</exclude>
								<exclude>net/sf/launch4j/formimpl</exclude>
							</excludes>
							<classifier>core</classifier>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<artifactId>maven-assembly-plugin</artifactId>
				<version>2.4.1</version>
				<executions>
					<execution>
						<id>assemble-workdir</id>
						<phase>package</phase>
						<goals>
							<goal>attached</goal>
						</goals>
						<configuration>
							<descriptors>
								<descriptor>assembly/assemble-linux.xml</descriptor>
								<descriptor>assembly/assemble-linux64.xml</descriptor>
								<descriptor>assembly/assemble-win32.xml</descriptor>
								<descriptor>assembly/assemble-mac.xml</descriptor>
							</descriptors>
						</configuration>
					</execution>
					<execution>
						<id>assemble-distrib</id>
						<phase>package</phase>
						<goals>
							<goal>single</goal>
						</goals>
						<configuration>
							<finalName>lib</finalName>
							<outputDirectory>${basedir}</outputDirectory>
							<appendAssemblyId>false</appendAssemblyId>
							<descriptors>
								<descriptor>assembly/assemble-dist.xml</descriptor>
							</descriptors>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<artifactId>maven-resources-plugin</artifactId>
				<version>2.6</version>
				<executions>
					<execution>
						<id>copy-resources</id>
						<phase>package</phase>
						<goals>
							<goal>copy-resources</goal>
						</goals>
						<configuration>
							<outputDirectory>${basedir}</outputDirectory>
							<resources>
								<resource>
									<directory>target</directory>
									<includes>
										<include>${project.build.finalName}.jar</include>
									</includes>
								</resource>
							</resources>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<artifactId>maven-clean-plugin</artifactId>
				<version>2.6</version>
				<configuration>
					<filesets>
						<fileset>
							<directory>${basedir}</directory>
							<includes>
								<include>${project.build.finalName}.jar</include>
							</includes>
						</fileset>
					</filesets>
				</configuration>
			</plugin>
		</plugins>
	</build>

	<profiles>
		<profile>
			<id>full-release</id>
			<build>
				<plugins>
					<plugin>
						<groupId>org.apache.maven.plugins</groupId>
						<artifactId>maven-source-plugin</artifactId>
						<version>2.3</version>
						<executions>
							<execution>
								<id>attach-sources</id>
								<goals>
									<goal>jar-no-fork</goal>
								</goals>
							</execution>
						</executions>
					</plugin>
					<plugin>
						<groupId>org.apache.maven.plugins</groupId>
						<artifactId>maven-javadoc-plugin</artifactId>
						<version>2.9.1</version>
						<executions>
							<execution>
								<id>attach-javadocs</id>
								<goals>
									<goal>jar</goal>
								</goals>
							</execution>
						</executions>
					</plugin>
					<plugin>
						<groupId>org.apache.maven.plugins</groupId>
						<artifactId>maven-gpg-plugin</artifactId>
						<version>1.5</version>
						<executions>
							<execution>
								<id>sign-artifacts</id>
								<goals>
									<goal>sign</goal>
								</goals>
							</execution>
						</executions>
					</plugin>
					<plugin>
						<groupId>org.sonatype.plugins</groupId>
						<artifactId>nexus-staging-maven-plugin</artifactId>
						<version>1.6.2</version>
						<extensions>true</extensions>
						<configuration>
							<serverId>ossrh</serverId>
							<nexusUrl>https://oss.sonatype.org/</nexusUrl>
							<!-- Release has to be executed manually -->
							<autoReleaseAfterClose>false</autoReleaseAfterClose>
						</configuration>
					</plugin>

				</plugins>
			</build>
		</profile>
		<!-- Macosx-x86 Profile -->
		<profile>
			<id>macosx-x86</id>
			<properties>
				<bin.dir>bin-macosx-x86</bin.dir>
			</properties>
		</profile>
		<!-- Linux Profile -->
		<profile>
			<id>linux</id>
			<properties>
				<bin.dir>bin-linux</bin.dir>
			</properties>
		</profile>
		<!-- Windows Profile -->
		<profile>
			<id>win32</id>
			<properties>
				<bin.dir>bin-win32</bin.dir>
			</properties>
			<build>
				<plugins>					
					<!-- Creates Launch4j executable -->
					<plugin>
						<groupId>com.akathist.maven.plugins.launch4j</groupId>
						<artifactId>launch4j-maven-plugin</artifactId>
						<version>1.6</version>
						<executions>
							<execution>
								<id>launch4j</id>
								<phase>package</phase>
								<goals>
									<goal>launch4j</goal>
								</goals>
								<configuration>
									<headerType>gui</headerType>
									<dontWrapJar>true</dontWrapJar>
									<jar>${project.build.finalName}.jar</jar>
									<outfile>launch4j.exe</outfile>
									<errTitle>launch4j</errTitle>
									<chdir>.</chdir>
									<stayAlive>false</stayAlive>

									<jre>
										<minVersion>1.6.0</minVersion>
									</jre>
									<versionInfo>
										<fileVersion>${versionNumber}</fileVersion>
										<txtFileVersion>${version}</txtFileVersion>
										<productVersion>${versionNumber}</productVersion>
										<txtProductVersion>${version}</txtProductVersion>
										<fileDescription>Cross-platform Java application wrapper</fileDescription>
										<copyright>Copyright (C) 2004, 2017 Grzegorz Kowal</copyright>
										<productName>launch4j</productName>
										<companyName>http://launch4j.sourceforge.net/</companyName>
										<internalName>GUI launcher</internalName>
										<originalFilename>launch4j.exe</originalFilename>
									</versionInfo>
								</configuration>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>
		</profile>
		<!-- Generic distribution profile -->
		<profile>
			<id>dist</id>			
			<build>
				<plugins>
					<!-- Clean -->
					<plugin>
						<artifactId>maven-clean-plugin</artifactId>
						<version>2.6</version>
						<configuration>
							<filesets>								
								<fileset>
									<directory>${basedir}/bin</directory>
									<includes>
										<incude>*</incude>
									</includes>
									<excludes>
										<exclude>COPYING</exclude>
									</excludes>
								</fileset>
								<fileset>
									<directory>${basedir}</directory>
									<includes>
										<incude>launch4j.exe</incude>
									</includes>
								</fileset>		
								<fileset>
									<directory>${basedir}</directory>
									<includes>
										<incude>lib/</incude>
									</includes>
								</fileset>
							</filesets>
						</configuration>
					</plugin>
					<!-- Copies executables to "bin" directory -->
					<plugin>
						<artifactId>maven-resources-plugin</artifactId>
						<version>2.6</version>
						<executions>
							<execution>
								<id>copy-binutils</id>
								<phase>prepare-package</phase>
								<goals>
									<goal>copy-resources</goal>
								</goals>
								<configuration>
									<outputDirectory>${basedir}/bin</outputDirectory>
									<resources>
										<resource>
											<directory>${basedir}/bin/${bin.dir}</directory>
										</resource>
									</resources>
								</configuration>
							</execution>
						</executions>
					</plugin>
					<!-- Change Permissions -->
					<plugin>
						<artifactId>maven-antrun-plugin</artifactId>
						<version>1.7</version>
						<executions>
							<execution>
								<phase>package</phase>
								<configuration>
									<target>
										<chmod file="launch4j" perm="+x" />
										<chmod file="bin/windres" perm="+x" />
										<chmod file="bin/ld" perm="+x" />
									</target>
								</configuration>
								<goals>
									<goal>run</goal>
								</goals>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>
		</profile>
	
	
	</profiles>

</project>