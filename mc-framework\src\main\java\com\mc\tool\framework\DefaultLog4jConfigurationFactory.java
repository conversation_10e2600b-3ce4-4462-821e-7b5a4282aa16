package com.mc.tool.framework;

import com.fasterxml.jackson.dataformat.yaml.YAMLMapper;
import com.google.common.net.InetAddresses;
import java.io.File;
import java.io.IOException;
import java.net.URI;
import org.apache.logging.log4j.Level;
import org.apache.logging.log4j.core.Filter;
import org.apache.logging.log4j.core.LoggerContext;
import org.apache.logging.log4j.core.appender.ConsoleAppender;
import org.apache.logging.log4j.core.config.Configuration;
import org.apache.logging.log4j.core.config.ConfigurationFactory;
import org.apache.logging.log4j.core.config.ConfigurationSource;
import org.apache.logging.log4j.core.config.Order;
import org.apache.logging.log4j.core.config.builder.api.AppenderComponentBuilder;
import org.apache.logging.log4j.core.config.builder.api.ComponentBuilder;
import org.apache.logging.log4j.core.config.builder.api.ConfigurationBuilder;
import org.apache.logging.log4j.core.config.builder.api.FilterComponentBuilder;
import org.apache.logging.log4j.core.config.builder.api.LayoutComponentBuilder;
import org.apache.logging.log4j.core.config.builder.api.RootLoggerComponentBuilder;
import org.apache.logging.log4j.core.config.builder.impl.BuiltConfiguration;
import org.apache.logging.log4j.core.config.plugins.Plugin;
import org.apache.logging.log4j.core.layout.Rfc5424Layout;

/**
 * .
 */
@Plugin(name = "DefaultLog4jConfigurationFactory", category = ConfigurationFactory.CATEGORY)
@Order(50)
public class DefaultLog4jConfigurationFactory extends ConfigurationFactory {

  public static final String CONFIG_FILE = "config.yaml";

  static Configuration createConfiguration(final String name, ConfigurationBuilder<BuiltConfiguration> builder) {
    builder.setConfigurationName(name);
    // create a console appender
    AppenderComponentBuilder appenderBuilder = builder.newAppender("Stdout", "CONSOLE")
        .addAttribute("target", ConsoleAppender.Target.SYSTEM_OUT);
    appenderBuilder.add(builder.newLayout("PatternLayout")
        .addAttribute("pattern", "[%d{yyyy-MM-dd HH:mm:ss:SSS}] [%p] - %l - %m%n"));
    builder.add(appenderBuilder);

    // create a rolling file appender
    ComponentBuilder triggeringPolicy = builder.newComponent("Policies")
        .addComponent(builder.newComponent("TimeBasedTriggeringPolicy"))
        .addComponent(builder.newComponent("SizeBasedTriggeringPolicy").addAttribute("size", "10M"));
    LayoutComponentBuilder layoutBuilder = builder.newLayout("PatternLayout")
        .addAttribute("pattern", "[%d{yyyy-MM-dd HH:mm:ss:SSS}] [%p] - %l - %m%n");
    FilterComponentBuilder filter =
        builder.newFilter("ThresholdFilter", Filter.Result.ACCEPT, Filter.Result.DENY)
            .addAttribute("level", Level.INFO);
    AppenderComponentBuilder rollingAppender =
        builder.newAppender("RollingFileInfo", "RollingFile").addAttribute("fileName", "app.log")
            .addAttribute("filePattern", "VPM-$${date:yyyy-MM}/app-%d{yyyy-MM-dd}-%i.log")
            .add(filter).add(layoutBuilder).addComponent(triggeringPolicy);
    builder.add(rollingAppender);

    RootLoggerComponentBuilder rootLoggerComponentBuilder =
        builder.newRootLogger(Level.INFO).add(builder.newAppenderRef("RollingFileInfo"))
            .add(builder.newAppenderRef("Stdout"));
    // 读取yaml配置文件
    try {
      File configFile = new File(CONFIG_FILE);
      if (configFile.exists()) {
        YAMLMapper mapper = new YAMLMapper();
        VpmConfig data = mapper.readValue(configFile, VpmConfig.class);
        // create a syslog appender
        if (data != null && data.getSyslog() != null && data.getSyslog().getHost() != null
            && InetAddresses.isInetAddress(data.getSyslog().getHost()) && data.getSyslog().getPort() > 0) {
          String host = data.getSyslog().getHost();
          int port = data.getSyslog().getPort();
          AppenderComponentBuilder syslogAppender =
              builder.newAppender("RFC5424", "Syslog").addAttribute("format", "RFC5424")
                  .addAttribute("host", host).addAttribute("port", port)
                  .addAttribute("protocol", "UDP")
                  .addAttribute("appName", "CaesarVPM")
                  .addAttribute("mdcId", Rfc5424Layout.DEFAULT_MDCID).addAttribute("enterpriseNumber",
                      String.valueOf(Rfc5424Layout.DEFAULT_ENTERPRISE_NUMBER))
                  .addAttribute("newLine", "true")
                  .addAttribute("messageId", "Audit").addAttribute("id", "App");
          builder.add(syslogAppender);
          rootLoggerComponentBuilder.add(builder.newAppenderRef("RFC5424"));
        }
      }
    } catch (IOException ex) {
      System.out.println(ex.getLocalizedMessage());
      ex.printStackTrace();
    }
    builder.add(rootLoggerComponentBuilder);
    return builder.build();
  }

  @Override
  public Configuration getConfiguration(final LoggerContext loggerContext,
                                        final ConfigurationSource source) {
    return getConfiguration(loggerContext, source.toString(), null);
  }

  @Override
  public Configuration getConfiguration(final LoggerContext loggerContext, final String name,
                                        final URI configLocation) {
    ConfigurationBuilder<BuiltConfiguration> builder = newConfigurationBuilder();
    return createConfiguration(name, builder);
  }

  @Override
  protected String[] getSupportedTypes() {
    return new String[] {"*"};
  }
}
