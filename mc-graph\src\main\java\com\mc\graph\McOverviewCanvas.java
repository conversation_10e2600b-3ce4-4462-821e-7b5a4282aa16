package com.mc.graph;

import com.mc.common.util.WeakAdapter;
import com.mc.graph.interfaces.CellObject;
import com.mc.graph.interfaces.CellSkin;
import com.mc.graph.interfaces.GraphModel;
import com.mc.graph.interfaces.LinkObject;
import com.mc.graph.interfaces.LinkSkin;
import com.mc.graph.interfaces.OverviewableGraphCanvas;
import com.mc.graph.interfaces.SkinFactory;
import com.mc.graph.interfaces.SkinManager;
import com.mc.graph.util.EventHandlerGroup;
import com.mc.graph.util.MouseControlUtil;
import com.mc.graph.util.MouseControlUtil.DraggableParams;
import com.mc.graph.util.SkinUtil;
import javafx.beans.value.ChangeListener;
import javafx.beans.value.ObservableValue;
import javafx.collections.ListChangeListener;
import javafx.event.EventHandler;
import javafx.geometry.Bounds;
import javafx.scene.layout.Pane;
import javafx.scene.paint.Color;
import javafx.scene.shape.Rectangle;
import javafx.scene.transform.Scale;

public class McOverviewCanvas extends Pane {
  private OverviewableGraphCanvas.OverviewCanvas innerCanvas;
  private GraphModel model;
  private SkinFactory skinFactory;
  private SkinManager skinManager;
  private OverviewableGraphCanvas canvas;
  private double width = 300;
  private double height = 180;
  private Scale scale = new Scale();
  private Rectangle viewportRect = new Rectangle();
  private DraggableParams params = new DraggableParams();

  private ChangeListener<Number> canvasViewportChangeListener;
  private WeakAdapter weakAdapter = new WeakAdapter();
  
  /**
   * Contructor.
   * 
   * @param model graph model
   * @param skinFactory skin factory
   * @param canvas the canvas the overview
   */
  public McOverviewCanvas(GraphModel model, SkinFactory skinFactory,
      OverviewableGraphCanvas canvas) {
    this.model = model;
    this.skinFactory = skinFactory;
    this.canvas = canvas;
    this.skinManager = new DefaultSkinManager();
    innerCanvas = canvas.createOverviewCanvas();
    init();
  }

  private void init() {
    this.setStyle("-fx-background-color:white");
    this.setPrefWidth(width);
    this.setPrefHeight(height);
    this.setMinWidth(width);
    this.setMinHeight(height);
    this.setMaxWidth(width);
    this.setMaxHeight(height);
    this.getChildren().add(innerCanvas.getContainer());

    initViewportRect();
    innerCanvas.getContainer().getTransforms().add(scale);

    model.getObservableCells()
        .addListener(weakAdapter.wrap(new ListChangeListener<CellObject>() {

          @Override
          public void onChanged(ListChangeListener.Change<? extends CellObject> change) {
            while (change.next()) {
              for (CellObject cellObject : change.getAddedSubList()) {
                SkinUtil.addCellSkin(canvas, innerCanvas, cellObject, skinFactory, skinManager);
              }

              for (CellObject cellObject : change.getRemoved()) {
                CellSkin skin = skinManager.getCellSkin(cellObject);
                if (skin != null) {
                  skin.remove();
                }
                skinManager.removeCellSkin(cellObject);
              }
            }
          }
        }));

    this.model.getObservableLinks()
        .addListener(weakAdapter.wrap(new ListChangeListener<LinkObject>() {

          @Override
          public void onChanged(ListChangeListener.Change<? extends LinkObject> change) {
            while (change.next()) {
              for (LinkObject linkObject : change.getAddedSubList()) {
                SkinUtil.addLinkSkin(innerCanvas, linkObject, skinFactory, skinManager);
              }

              for (LinkObject linkObject : change.getRemoved()) {
                LinkSkin skin = skinManager.getLinkSkin(linkObject);
                if (skin != null) {
                  skin.remove();
                }
                skinManager.removeLinkSkin(linkObject);
              }
            }
          }
        }));

    initAutoResizing();
  }

  private void initViewportRect() {
    viewportRect.setFill(new Color(0, 0, 0, 0));
    viewportRect.setStroke(Color.GREEN);
    viewportRect.setStrokeWidth(1);
    viewportRect.setWidth(100);
    viewportRect.setHeight(100);
    viewportRect.setLayoutX(0);
    viewportRect.setLayoutY(0);
    this.getChildren().add(viewportRect);
    MouseControlUtil.makeDraggable(viewportRect, params);

    if (viewportRect.getOnMouseDragged() instanceof EventHandlerGroup<?>) {
      EventHandlerGroup<?> group = (EventHandlerGroup<?>) viewportRect.getOnMouseDragged();
      group.addHandler(weakAdapter.wrap((EventHandler)(event) -> {
        updateCanvasViewport();
      }));
    }

    canvasViewportChangeListener = weakAdapter.wrap(new ChangeListener<Number>() {

      @Override
      public void changed(ObservableValue<? extends Number> observable, Number oldValue,
          Number newValue) {
        updateViewportRect();
      }
    });
    canvas.getHorizontalPosProperty().addListener(canvasViewportChangeListener);
    canvas.getVerticalPosProperty().addListener(canvasViewportChangeListener);
    canvas.getHorizontalVisibleProperty().addListener(canvasViewportChangeListener);
    canvas.getVerticalVisibleProperty().addListener(canvasViewportChangeListener);

  }

  protected void updateViewportRestriction() {
    // update the viewport rect's layout restriction.
    double width = innerCanvas.getContainer().getBoundsInLocal().getWidth();
    double height = innerCanvas.getContainer().getBoundsInLocal().getHeight();
    double minX = innerCanvas.getContainer().getBoundsInParent().getMinX();
    double minY = innerCanvas.getContainer().getBoundsInParent().getMinY();
    double maxX = minX + width * scale.getX() - viewportRect.getWidth();
    double maxY = minY + height * scale.getY() - viewportRect.getHeight();
    maxX = Math.max(maxX, minX);
    maxY = Math.max(maxY, minY);
    params.getMinXposProperty().set(minX);
    params.getMaxXposProperty().set(maxX);
    params.getMinYposProperty().set(minY);
    params.getMaxYposProperty().set(maxY);
  }

  protected void updateViewportRect() {
    double xpos = 0;
    double ypos = 0;
    double width = innerCanvas.getContainer().getBoundsInLocal().getWidth();
    double height = innerCanvas.getContainer().getBoundsInLocal().getHeight();
    double layoutX = innerCanvas.getContainer().getBoundsInParent().getMinX();
    double layoutY = innerCanvas.getContainer().getBoundsInParent().getMinY();
    xpos = (1 - canvas.getHorizontalVisibleProperty().get()) * width * scale.getX()
        * canvas.getHorizontalPosProperty().get() + layoutX;
    ypos = (1 - canvas.getVerticalVisibleProperty().get()) * height * scale.getY()
        * canvas.getVerticalPosProperty().get() + layoutY;
    viewportRect.setLayoutX(xpos);
    viewportRect.setLayoutY(ypos);
    viewportRect.setWidth(Math.min(width * scale.getX(),
        canvas.getHorizontalVisibleProperty().get() * width * scale.getX()));
    viewportRect.setHeight(Math.min(height * scale.getY(),
        canvas.getVerticalVisibleProperty().get() * height * scale.getY()));

    updateViewportRestriction();
  }

  protected void updateCanvasViewport() {
    double xpos = viewportRect.getLayoutX();
    double ypos = viewportRect.getLayoutY();
    double layoutX = innerCanvas.getContainer().getBoundsInParent().getMinX();
    double layoutY = innerCanvas.getContainer().getBoundsInParent().getMinY();
    double width = innerCanvas.getContainer().getBoundsInLocal().getWidth();
    double height = innerCanvas.getContainer().getBoundsInLocal().getHeight();
    double hvalue = (xpos - layoutX)
        / ((1 - canvas.getHorizontalVisibleProperty().get()) * width * scale.getX());
    double vvvalue = (ypos - layoutY)
        / ((1 - canvas.getVerticalVisibleProperty().get()) * height * scale.getY());
    if (Double.isNaN(vvvalue)) {
      vvvalue = 0;
    }
    if (Double.isNaN(hvalue)) {
      hvalue = 0;
    }
    canvas.getHorizontalPosProperty().set(hvalue);
    canvas.getVerticalPosProperty().set(vvvalue);
  }

  private void initAutoResizing() {
    innerCanvas.getContainer().boundsInLocalProperty()
        .addListener(weakAdapter.wrap(new ChangeListener<Bounds>() {

          @Override
          public void changed(ObservableValue<? extends Bounds> observable, Bounds oldValue,
              Bounds newValue) {
            double margin = 20;
            double contentWidth = newValue.getWidth();
            double contentHeight = newValue.getHeight();
            double scaleFactor = 1;
            double xpos = 0;
            double ypos = 0;

            double viewWidth = width - margin * 2;
            double viewHeight = height - margin * 2;
            // 如果content过宽，就按宽度的缩小比例来缩放
            if (viewWidth / viewHeight < contentWidth / contentHeight) {
              scaleFactor = viewWidth / contentWidth;
              ypos = (viewHeight - contentHeight * scaleFactor) / 2;
              xpos = 0;
            } else {
              scaleFactor = viewHeight / contentHeight;
              xpos = (viewWidth - contentWidth * scaleFactor) / 2;
              ypos = 0;
            }

            scale.setX(scaleFactor);
            scale.setY(scaleFactor);
            scale.setPivotX(0);
            scale.setPivotY(0);
            innerCanvas.getContainer().setTranslateX(0);
            innerCanvas.getContainer().setTranslateY(0);
            innerCanvas.relocateContainer(xpos + margin, ypos + margin, scaleFactor);
            updateViewportRect();
          }
        }));
  }
  
  /**
   * 销毁数据.
   */
  public void destroy() {
    canvas.getHorizontalPosProperty().removeListener(canvasViewportChangeListener);
    canvas.getVerticalPosProperty().removeListener(canvasViewportChangeListener);
    canvas.getHorizontalVisibleProperty().removeListener(canvasViewportChangeListener);
    canvas.getVerticalVisibleProperty().removeListener(canvasViewportChangeListener);
    skinManager.destroy();
  }

}
