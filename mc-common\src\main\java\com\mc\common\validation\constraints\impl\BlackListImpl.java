package com.mc.common.validation.constraints.impl;

import com.mc.common.validation.constraints.BlackList;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

public class BlackListImpl implements ConstraintValidator<BlackList, String> {
  private String[] items;

  @Override
  public void initialize(BlackList constraintAnnotation) {
    items = constraintAnnotation.blackItems();
  }

  @Override
  public boolean isValid(String value, ConstraintValidatorContext context) {
    if (value == null) {
      return true;
    }
    for (String item : items) {
      if (item == null || item.isEmpty()) {
        continue;
      }
      if (item.equalsIgnoreCase(value)) {
        return false;
      }
    }
    return true;
  }

}
