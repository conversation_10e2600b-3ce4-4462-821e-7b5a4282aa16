osd:[t(200) l(200) w(80) h(400) c(a4ff) bc(80000000) a(63) id(0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0)]
input(iw ih ow oh):[0(1920 2160 480 510) 1(1920 1080 960 520) 2(1920 1080 960 530) 3(1920 1080 960 540) 4(1920 2160 479 510) 5(1920 1080 960 510) 6(0 0 0 0) 7(0 0 0 0) ]
vert_cut(start_line, video_src):[0(0 5) 1(0 1) 2(0 2) 3(0 3) 4(0 0) 5(0 4) 6(0 0) 7(0 0) 8(0 0) 9(0 0) 10(0 0) 11(0 0) 12(0 0) 13(0 0) 14(0 0) 15(0 0) 16(0 0) 17(0 0) 18(0 0) 19(0 0) ]
horz_cut(start_px, end_px, horz_cut_cnt, vert_cut_index):[0(0 959 0 0) 1(0 959 0 1) 2(0 959 0 2) 3(0 959 0 3) 4(0 479 0 4) 5(0 478 0 5) 6(0 0 0 20) 7(0 0 0 20) 8(0 0 0 20) 9(0 0 0 20) 10(0 0 0 20) 11(0 0 0 20) 12(0 0 0 20) 13(0 0 0 20) 14(0 0 0 20) 15(0 0 0 20) 16(0 0 0 20) 17(0 0 0 20) 18(0 0 0 20) 19(0 0 0 20) ]
output(oport olayer iw ih ow oh):[0(0 0 960 510 960 510) 1(0 3 960 520 960 520) 2(0 4 960 530 960 530) 3(0 5 960 540 960 540) 4(0 1 480 510 480 510) 5(0 2 479 510 479 510) 6(0 6 0 0 0 0) 7(0 6 0 0 0 0) 8(0 6 0 0 0 0) 9(0 6 0 0 0 0) 10(0 6 0 0 0 0) 11(0 6 0 0 0 0) 12(0 6 0 0 0 0) 13(0 6 0 0 0 0) 14(0 6 0 0 0 0) 15(0 6 0 0 0 0) 16(0 6 0 0 0 0) 17(0 6 0 0 0 0) 18(0 6 0 0 0 0) 19(0 6 0 0 0 0) ]
layer(start_line start_px w h a):6[0(0 0 960 510 127) 1(0 0 480 510 127) 2(0 480 479 510 127) 3(0 960 960 520 127) 4(540 0 960 530 127) 5(540 960 960 540 127) ]
