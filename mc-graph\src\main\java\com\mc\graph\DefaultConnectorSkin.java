package com.mc.graph;

import com.mc.graph.interfaces.Connector;
import com.mc.graph.util.LinkUtil;
import javafx.beans.binding.DoubleBinding;
import javafx.scene.CacheHint;
import javafx.scene.Node;
import javafx.scene.Parent;
import javafx.scene.paint.Color;
import javafx.scene.shape.Circle;

public class DefaultConnector<PERSON>kin extends AbstractConnectorSkin {

  public static final double RADIUS = 10;
  public Circle circle;

  /**
   * Constructor.
   * 
   * @param connector connector to be skin
   * @param parent parent to add components
   * @param container TODO
   */
  public DefaultConnectorSkin(Connector connector, Parent parent, Parent container) {
    super(connector, parent, container);

  }

  @Override
  public Node getNode() {
    return circle;
  }

  @Override
  protected void initNode() {
    circle = new Circle();
    circle.setRadius(RADIUS);

    circle.setManaged(true);
    circle.setCache(true);
    circle.setCacheHint(CacheHint.SPEED);
    circle.setFill(Color.GREEN);
    circle.setStyle("-fx-border-width: 2px;");
    circle.setStyle("-fx-border-color: rgba(120, 140, 255, 0.72);");
    circle.setUserData(this);

    DoubleBinding xposBinding = new DoubleBinding() {
      {
        super.bind(circle.parentProperty(), parent.layoutXProperty(), parent.translateXProperty(),
            circle.layoutXProperty(), circle.translateXProperty());
      }

      @Override
      protected double computeValue() {
        return LinkUtil.localToGrandParent(parent, container,
            circle.getLayoutX() + circle.getTranslateX(), true);
      }
    };
    containerXposProperty.bind(xposBinding);

    DoubleBinding yposBinding = new DoubleBinding() {
      {
        super.bind(circle.parentProperty(), parent.layoutYProperty(), parent.translateYProperty(),
            circle.layoutYProperty(), circle.translateYProperty());
      }

      @Override
      protected double computeValue() {
        return LinkUtil.localToGrandParent(parent, container,
            circle.getLayoutY() + circle.getTranslateY(), false);
      }
    };
    containerYposProperty.bind(yposBinding);
  }
}
