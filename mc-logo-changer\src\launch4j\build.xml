<project name="launch4j" default="compile" basedir=".">
	<property name="src" location="src" />
	<property name="lib" location="lib" />
	<property name="build" location="build" />
	<property name="jar" location="./${ant.project.name}.jar" />
	<property name="launch4j.dir" location="." />
	<property name="maven" location="maven" />
	<property file="${src}/launch4j.properties"/>

	<path id="dist.classpath">
		<pathelement path="${build}" />
		<fileset dir="${lib}">
			<include name="**/*.jar" />
		</fileset>
	</path>

	<target name="init">
		<tstamp />
		<mkdir dir="${build}" />
	</target>

	<target name="compile" depends="init" description="compile the source">
		<javac srcdir="${src}" destdir="${build}" classpathref="dist.classpath" source="1.6" debug="on" includeantruntime="false" />
		<copy todir="${build}/images">
			<fileset dir="${src}/images">
				<include name="**/*" />
			</fileset>
		</copy>
		<copy todir="${build}">
			<fileset dir="${src}">
				<include name="**/*.properties" />
			</fileset>
		</copy>
	</target>

	<target name="jar" depends="compile" description="create jar">
		<fileset dir="${lib}" id="lib.dist.fileset">
			<include name="**/*.jar" />
		</fileset>
		<pathconvert pathsep=" " property="dist.classpath" refid="lib.dist.fileset">
			<map from="${lib}" to="./lib" />
		</pathconvert>
		<!-- Put everything in ${build} into a jar file -->
		<jar jarfile="${jar}">
			<fileset dir="${build}" excludes="**/messages_es.properties" />
			<manifest>
				<attribute name="Main-Class" value="net.sf.launch4j.Main" />
				<attribute name="Class-Path" value=". ${dist.classpath}" />
			</manifest>
		</jar>
	</target>

	<target name="demo" depends="jar" description="build the demos">
		<ant dir="./demo/ConsoleApp" inheritAll="false" />
		<ant dir="./demo/SimpleApp" inheritAll="false" />
	</target>

	<target name="clean" description="clean up">
		<delete dir="${build}" />
		<delete file="${jar}" />
		<ant dir="./demo/ConsoleApp" target="clean" inheritAll="false" />
		<ant dir="./demo/SimpleApp" target="clean" inheritAll="false" />
	</target>
	
	<target name="switch-to-maven" description="switch project to maven">
		<copy todir="." overwrite="true">
			<fileset dir="${maven}">
				<include name="**/*" />
			</fileset>
		</copy>
		<replace file="./pom.xml">
			<replacefilter token="$${launch4j.version}" value="${version}"/>
		</replace>
		<delete dir="${lib}" />
		<mkdir dir="./target" />
	</target>
</project>
