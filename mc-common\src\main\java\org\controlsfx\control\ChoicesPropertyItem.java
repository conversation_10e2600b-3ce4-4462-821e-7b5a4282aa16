package org.controlsfx.control;

import javafx.beans.property.ObjectProperty;
import javafx.collections.ObservableList;
import javafx.util.StringConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.function.Predicate;

public class ChoicesPropertyItem<T> extends ObjectPropertyItem<T> {
  @Getter
  private final ObservableList<T> choices;
  @Setter
  @Getter
  private StringConverter<T> converter = null;
  @Setter
  @Getter
  private Predicate<T> filter = null;

  public ChoicesPropertyItem(ObjectProperty<T> value, Class<T> clazz, ObservableList<T> choices) {
    this(value, clazz, choices, false);
  }
  
  public ChoicesPropertyItem(ObjectProperty<T> value, Class<T> clazz, ObservableList<T> choices,
      boolean needToUnbindValue) {
    super(value, clazz, needToUnbindValue);
    this.choices = choices;
  }

  @Override
  public void setValue(Object value) {
    if (clazz.isInstance(value)) {
      this.value.set(clazz.cast(value));
    }
  }
}
