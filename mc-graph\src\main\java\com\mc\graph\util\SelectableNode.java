/*
 * Copyright 2012-2016 <PERSON> <info@micha<PERSON><PERSON>er.de>. All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without modification, are permitted
 * provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this list of conditions
 * and the following disclaimer.
 *
 * 2. Redistributions in binary form must reproduce the above copyright notice, this list of
 * conditions and the following disclaimer in the documentation and/or other materials provided with
 * the distribution.
 *
 * Please cite the following publication(s):
 *
 * <PERSON><PERSON>, C.<PERSON>, G.<PERSON>. Visual Reflection Library - A Framework for Declarative GUI
 * Programming on the Java Platform. Computing and Visualization in Science, 2011, in press.
 *
 * THIS SOFTWARE IS PROVIDED BY <PERSON> <<EMAIL>> "AS IS" AND ANY EXPRESS OR
 * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND
 * FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL <PERSON>
 * <info@micha<PERSON><PERSON>er.de> OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
 * EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE
 * GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON
 * ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY
 * OF SUCH DAMAGE.
 *
 * The views and conclusions contained in the software and documentation are those of the authors
 * and should not be interpreted as representing official policies, either expressed or implied, of
 * Michael Hoffer <<EMAIL>>.
 */

package com.mc.graph.util;

import javafx.beans.property.BooleanProperty;

/**
 * A node must implement this interface to be selectable. Usually, nodes/windows are selected via
 * selection rectangle gesture.
 *
 * <AUTHOR> Hoffer &lt;<EMAIL>&gt;
 *
 */
public interface SelectableNode {

  /**
   * Requests selection/deselection.
   *
   * @param select defines whether to select or deselect the node
   * @return <code>true</code> if request is accepted;<code>false</code> otherwise
   */
  boolean requestSelection(boolean select);

  /**
   * Indicates whether this node is selected.
   *
   * @return {@code true} if this node is selected; {@code false} otherwise
   */
  boolean isSelected();

  /**
   * Indicates whether this node is selected.
   *
   * @return {@code true} if this node is selected; {@code false} otherwise
   */
  BooleanProperty selectedProperty();
}
