package com.mc.common.form;

import com.sun.javafx.binding.ExpressionHelper;
import javafx.beans.InvalidationListener;
import javafx.beans.Observable;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.Property;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.beans.value.ChangeListener;
import javafx.beans.value.ObservableValue;

import java.lang.ref.WeakReference;

public class BooleanFormProperty extends BooleanProperty implements FormProperty<Boolean> {
  private Boolean value = null;
  private Property<Boolean> observable = null;
  private InvalidationListener listener = null;
  private boolean valid = true;
  private ExpressionHelper<Boolean> helper = null;

  protected BooleanProperty changed = new SimpleBooleanProperty(false);

  public BooleanFormProperty(Property<Boolean> binding) {
    bindImpl(binding);
  }

  @Override
  public void addListener(InvalidationListener listener) {
    helper = ExpressionHelper.addListener(helper, this, listener);
  }

  @Override
  public void addListener(ChangeListener<? super Boolean> listener) {
    helper = ExpressionHelper.addListener(helper, this, listener);
  }
  
  @Override
  public void removeListener(InvalidationListener listener) {
    helper = ExpressionHelper.removeListener(helper, listener);
  }

  @Override
  public void removeListener(ChangeListener<? super Boolean> listener) {
    helper = ExpressionHelper.removeListener(helper, listener);
  }

  /**
   * Sends notifications to all attached {@link javafx.beans.InvalidationListener
   * InvalidationListeners} and {@link javafx.beans.value.ChangeListener ChangeListeners}.
   * This method is called when the value is changed, either manually by calling
   * {@link #set(boolean)} or in case of a bound property, if the binding becomes invalid.
   */
  protected void fireValueChangedEvent() {
    ExpressionHelper.fireValueChangedEvent(helper);
  }

  private void markInvalid() {
    if (valid) {
      valid = false;
      invalidated();
      fireValueChangedEvent();
    }
  }

  /**
   * The method {@code invalidated()} can be overridden to receive invalidation notifications. This
   * is the preferred option in {@code Objects} defining the property, because it requires less
   * memory.
   * The default implementation is empty.
   */
  protected void invalidated() {}

  @Override
  public boolean get() {
    valid = true;
    if (value != null) {
      return value;
    } else if (observable != null) {
      return observable.getValue();
    } else {
      return false;
    }
  }

  @Override
  public void set(boolean newValue) {
    if (value != null) {
      if (observable.getValue() != null && observable.getValue().equals(newValue)) {
        value = null;
        changed.set(false);
        markInvalid();
      } else if (!value.equals(newValue)) {
        value = newValue;
        markInvalid();
      }
    } else if (observable.getValue() != null && !observable.getValue().equals(newValue)) {
      value = newValue;
      changed.set(true);
      markInvalid();
    }
  }

  @Override
  public boolean isBound() {
    return observable != null;
  }

  @Override
  public void bind(final ObservableValue<? extends Boolean> rawObservable) {
    throw new UnsupportedOperationException();
  }

  protected void bindImpl(final Property<Boolean> newObservable) {
    if (newObservable == null) {
      throw new NullPointerException("Cannot bind to null");
    }

    if (!newObservable.equals(observable)) {
      unbind();
      observable = newObservable;
      if (listener == null) {
        listener = new Listener(this);
      }
      observable.addListener(listener);
      markInvalid();
    }
  }

  @Override
  public void unbind() {}

  protected void unbindImpl() {
    if (observable != null) {
      value = observable.getValue();
      observable.removeListener(listener);
      observable = null;
    }
  }

  /**
   * Returns a string representation of this {@code BooleanPropertyBase} object.
   * @return a string representation of this {@code BooleanPropertyBase} object.
   */
  @Override
  public String toString() {
    final Object bean = getBean();
    final String name = getName();
    final StringBuilder result = new StringBuilder("BooleanProperty [");
    if (bean != null) {
      result.append("bean: ").append(bean).append(", ");
    }
    if (name != null && !name.equals("")) {
      result.append("name: ").append(name).append(", ");
    }
    if (isBound()) {
      result.append("bound, ");
      if (valid) {
        result.append("value: ").append(get());
      } else {
        result.append("invalid");
      }
    } else {
      result.append("value: ").append(get());
    }
    result.append("]");
    return result.toString();
  }

  private static class Listener implements InvalidationListener {

    private final WeakReference<BooleanFormProperty> wref;

    public Listener(BooleanFormProperty ref) {
      this.wref = new WeakReference<>(ref);
    }

    @Override
    public void invalidated(Observable observable) {
      BooleanFormProperty ref = wref.get();
      if (ref == null) {
        observable.removeListener(this);
      } else {
        ref.markInvalid();
      }
    }
  }

  @Override
  public Object getBean() {
    return null;
  }

  @Override
  public String getName() {
    return null;
  }

  @Override
  public boolean isChanged() {
    return changed.get();
  }

  @Override
  public void setToBinding() {
    if (value != null) {
      observable.setValue(value);
      value = null;
      changed.set(false);
      markInvalid();
    }
  }

  @Override
  public void reset() {
    if (value != null) {
      value = null;
      changed.set(false);
      markInvalid();
    }
  }

  @Override
  public BooleanProperty changedProperty() {
    return changed;
  }
}
