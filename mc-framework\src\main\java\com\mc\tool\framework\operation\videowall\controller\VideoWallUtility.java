package com.mc.tool.framework.operation.videowall.controller;

import com.mc.graph.canvas.PageCanvasUtility;
import com.mc.tool.framework.operation.videowall.datamodel.IVideoWallLayout;
import com.mc.tool.framework.operation.videowall.datamodel.ScreenData;
import com.mc.tool.framework.operation.videowall.datamodel.ScreenLayers;
import com.mc.tool.framework.operation.videowall.datamodel.ScreenObjectLayers;
import com.mc.tool.framework.operation.videowall.datamodel.VideoClipData;
import com.mc.tool.framework.operation.videowall.datamodel.interfaces.ScreenObject;
import com.mc.tool.framework.operation.videowall.datamodel.interfaces.VideoObject;
import com.mc.tool.framework.operation.videowall.datamodel.interfaces.VideoWallObject;
import com.mc.tool.framework.systemedit.datamodel.Resolution;
import com.mc.tool.framework.systemedit.datamodel.VideoWallFunc;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import javafx.geometry.Rectangle2D;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class VideoWallUtility {

  /**
   * 计算每个rx的视频图层数据.
   *
   * @param videoWallObject 视频墙数据.
   * @return rx的图层信息.
   */
  public static ScreenLayers computeLayerDatas(VideoWallObject videoWallObject) {
    ScreenLayers screenLayers = new ScreenLayers();
    for (Pair<ScreenObject, List<VideoClipData>> item : computeBasicLayerDatas(videoWallObject,
        true)) {
      ScreenObject screenObject = item.getKey();
      if (screenObject instanceof ScreenData) {
        screenLayers.add(new Pair<>((ScreenData) screenObject, item.getValue()));
      }
    }
    return screenLayers;
  }


  /**
   * 计算每个rx的视频图层数据.
   *
   * @param videoWallData        视频墙数据.
   * @param ignoreCoveredLayered 忽略被覆盖的图层
   * @return rx的图层信息.
   */
  public static ScreenObjectLayers computeBasicLayerDatas(VideoWallObject videoWallData,
                                                          boolean ignoreCoveredLayered) {
    return computeBasicLayerDatas(videoWallData, ignoreCoveredLayered, true, true);
  }

  /**
   * 计算每个rx的视频图层数据.
   *
   * @param videoWallData        视频墙数据.
   * @param ignoreCoveredLayered 忽略被覆盖的图层
   * @param ignoreEmptyScreen    忽略rx为空的屏幕
   * @param ignoreEmptyVideo     忽略空的信号源
   * @return rx的图层信息.
   */
  public static ScreenObjectLayers computeBasicLayerDatas(VideoWallObject videoWallData,
                                                          boolean ignoreCoveredLayered, boolean ignoreEmptyScreen,
                                                          boolean ignoreEmptyVideo) {
    ScreenObjectLayers screenLayers = new ScreenObjectLayers();

    List<Integer> widths = videoWallData.getLayoutData().getWidths();
    List<Integer> heights = videoWallData.getLayoutData().getHeights();
    for (int i = 0; i < videoWallData.getLayoutData().getRows(); i++) {
      for (int j = 0; j < videoWallData.getLayoutData().getColumns(); j++) {
        int index = i * videoWallData.getLayoutData().getColumns() + j;
        if (index >= videoWallData.getScreenCount()) {
          break;
        }

        ScreenObject screenData = videoWallData.getScreens().get(index);
        if (screenData.isEmpty() && ignoreEmptyScreen) {
          continue;
        }

        Rectangle2D screenRect = new Rectangle2D(PageCanvasUtility.getColumnStartPos(widths, j),
            PageCanvasUtility.getRowStartPos(heights, i), widths.get(j), heights.get(i));

        List<VideoClipData> clips = new ArrayList<>();
        screenLayers.add(new Pair<>(screenData, clips));
        // 获取所有的intersections
        List<Rectangle2D> intersections = new ArrayList<>();
        List<Integer> videoIndexs = new ArrayList<>();
        int videoIndex = -1;
        for (VideoObject videoData : videoWallData.getVideos()) {
          videoIndex++;
          if (videoData.getHeight().get() < 0 || videoData.getWidth().get() < 0) {
            log.warn("width or height < 0! width : {}, height : {}.", videoData.getHeight().get(),
                videoData.getWidth().get());
            continue;
          }
          Rectangle2D videoRect = new Rectangle2D(videoData.getXpos().get(),
              videoData.getYpos().get(), videoData.getWidth().get(), videoData.getHeight().get());
          Rectangle2D intersection = getIntersection(screenRect, videoRect);
          if (intersection == null) {
            continue;
          }
          intersections.add(intersection);
          videoIndexs.add(videoIndex);
        }

        if (ignoreCoveredLayered) {
          // 去掉被覆盖掉的intersections
          List<Integer> ignoreVideoIndexs = new ArrayList<>();
          for (int k = 0; k < intersections.size(); k++) {
            if (isRectCovered(intersections.get(k),
                intersections.subList(k + 1, intersections.size()))) {
              ignoreVideoIndexs.add(videoIndexs.get(k));
            }
          }
          videoIndexs.removeAll(ignoreVideoIndexs);
        }
        // 计算参数
        for (Integer validIndex : videoIndexs) {
          VideoObject videoData = videoWallData.getVideos().get(validIndex);
          videoIndex = validIndex;

          VisualEditTerminal videoTerminal = videoData.getSource().get();

          if (ignoreEmptyVideo) {
            if (videoTerminal == null) {
              continue;
            }

            if (!(videoTerminal.isTx())) {
              log.warn("Not tx terminal for video !");
              continue;
            }
          }
          Resolution resolution = null;
          if (videoTerminal != null) {
            resolution = videoTerminal.getResolution(0);
            if (resolution.getWidth() <= 0 || resolution.getHeight() <= 0 && ignoreEmptyVideo) {
              if (ignoreEmptyVideo) {
                log.warn("{}'s resolution is error : {}*{}", videoTerminal.getName(),
                    resolution.getWidth(), resolution.getHeight());
                continue;
              } else {
                resolution = null;
              }
            }
          }

          Rectangle2D videoRect = new Rectangle2D(videoData.getXpos().get(),
              videoData.getYpos().get(), videoData.getWidth().get(), videoData.getHeight().get());
          Rectangle2D intersection = getIntersection(screenRect, videoRect);

          VideoClipData videoClipData = new VideoClipData();
          videoClipData.setAlpha(videoData.getAlpha().get());
          videoClipData.setOutX((int) (intersection.getMinX() - screenRect.getMinX()));
          videoClipData.setOutY((int) (intersection.getMinY() - screenRect.getMinY()));
          videoClipData.setOutWidth((int) intersection.getWidth());
          videoClipData.setOutHeight((int) intersection.getHeight());


          if (resolution != null) {
            double scaleW = videoRect.getWidth() / resolution.getWidth();
            double scaleH = videoRect.getHeight() / resolution.getHeight();
            double clipX = (intersection.getMinX() - videoRect.getMinX()) / scaleW;
            double clipY = (intersection.getMinY() - videoRect.getMinY()) / scaleH;
            videoClipData.setOriginWidth(resolution.getWidth());
            videoClipData.setOriginHeight(resolution.getHeight());
            videoClipData.setOriginClipX((int) clipX);
            videoClipData.setOriginClipY((int) clipY);
            videoClipData.setOriginClipWidth((int) (intersection.getWidth() / scaleW));
            videoClipData.setOriginClipHeight((int) (intersection.getHeight() / scaleH));
          }

          videoClipData.setScaledWidth((int) videoRect.getWidth());
          videoClipData.setScaledHeight((int) videoRect.getHeight());
          videoClipData.setScaledClipX((int) (intersection.getMinX() - videoRect.getMinX()));
          videoClipData.setScaledClipY((int) (intersection.getMinY() - videoRect.getMinY()));
          videoClipData.setScaledClipWidth((int) intersection.getWidth());
          videoClipData.setScaledClipHeight((int) intersection.getHeight());

          videoClipData.setVideoIndex(videoIndex);
          videoClipData.setSource(videoTerminal);
          clips.add(videoClipData);
        }
      }
    }

    return screenLayers;
  }

  /**
   * 判断一个rect是否能被多个rect覆盖住.
   *
   * @param first    要被覆盖的rect
   * @param rectList 要覆盖first的多个rect.
   * @return 如果能覆盖住，返回true，否则返回false.
   */
  public static boolean isRectCovered(Rectangle2D first, Collection<Rectangle2D> rectList) {
    List<Rectangle2D> uncoveredRects = new ArrayList<>();
    uncoveredRects.add(first);
    for (Rectangle2D rect : rectList) {
      List<Rectangle2D> newUncoveredRects = new ArrayList<>();
      for (Rectangle2D uncoveredRect : uncoveredRects) {
        newUncoveredRects.addAll(getSutraction(uncoveredRect, rect));
      }
      if (newUncoveredRects.isEmpty()) {
        return true;
      }
      uncoveredRects = newUncoveredRects;
    }
    return false;
  }

  /**
   * 判断一个rect是否能被另外一个rect覆盖住.
   *
   * @param coveree 被覆盖的rect
   * @param coverer 要覆盖的rect
   * @return 如果能覆盖住，返回true，否则返回false
   */
  public static boolean isRectCovered(Rectangle2D coveree, Rectangle2D coverer) {
    return coverer.getMinX() <= coveree.getMinX() && coverer.getMinY() <= coveree.getMinY()
        && coverer.getMaxX() >= coveree.getMaxX() && coverer.getMaxY() >= coveree.getMaxY();
  }

  /**
   * 获取一个rect减去另外一个rect剩下的rect的集合.
   *
   * @param input    被减的rect.
   * @param subtract 要减去的rect.
   * @return rect的集合
   */
  public static Collection<Rectangle2D> getSutraction(Rectangle2D input, Rectangle2D subtract) {
    List<Rectangle2D> result = new ArrayList<>();
    if (isRectCovered(input, subtract)) {
      return result;
    }
    Rectangle2D intersection = getIntersection(input, subtract);
    if (intersection == null) {
      result.add(input);
    } else {
      // 把rect划分成3列，去掉intersection部分，剩下的rect保存下来
      double[] xstart = new double[4];
      xstart[0] = input.getMinX();
      xstart[1] = intersection.getMinX();
      xstart[2] = intersection.getMaxX();
      xstart[3] = input.getMaxX();

      double[] ystart = new double[4];
      ystart[0] = input.getMinY();
      ystart[1] = intersection.getMinY();
      ystart[2] = intersection.getMaxY();
      ystart[3] = input.getMaxY();

      for (int i = 0; i < 3; i++) {
        double width = xstart[i + 1] - xstart[i];
        if (width < 1e-10) {
          continue;
        }

        if (i == 1) {
          // 第二列特殊处理
          for (int j = 0; j < 3; j++) {
            // 去掉intersection部分
            if (j == 1) {
              continue;
            }
            double xpos = xstart[i];
            double ypos = ystart[j];
            double height = ystart[j + 1] - ystart[j];
            if (height > 1e-10) {
              Rectangle2D rect = new Rectangle2D(xpos, ypos, width, height);
              result.add(rect);
            }
          }
        } else {
          // 第一列和第3列
          double xpos = xstart[i];
          double ypos = ystart[0];
          double height = input.getHeight();
          if (height > 1e-10) {

            Rectangle2D rect = new Rectangle2D(xpos, ypos, width, height);
            result.add(rect);
          }
        }
      }
    }
    return result;

  }

  /**
   * 获取两个矩形的重叠的部分.
   *
   * @param first  第一个矩形.
   * @param second 第二个矩形.
   * @return 重叠的矩形.
   */
  public static Rectangle2D getIntersection(Rectangle2D first, Rectangle2D second) {
    if (!first.intersects(second)) {
      return null;
    }
    double minX = Math.max(first.getMinX(), second.getMinX());
    double minY = Math.max(first.getMinY(), second.getMinY());
    double maxX = Math.min(first.getMaxX(), second.getMaxX());
    double maxY = Math.min(first.getMaxY(), second.getMaxY());
    return new Rectangle2D(minX, minY, maxX - minX, maxY - minY);
  }

  /**
   * 根据屏幕布局来切割窗口.
   *
   * @param func      视频墙控制
   * @param videoData 视频窗口数据
   * @return 切割后的视频窗口
   */
  public static Collection<VideoObject> splitVideoByScreen(VideoWallFunc func,
                                                           VideoObject videoData) {
    List<VideoObject> result = new ArrayList<>();
    IVideoWallLayout layout = func.getVideoWallObject().getUserLayout();
    List<Integer> widths = layout.getWidths();
    List<Integer> heights = layout.getHeights();
    Set<String> exculdeItems = new HashSet<>();
    for (int i = 0; i < layout.getRows(); i++) {
      for (int j = 0; j < layout.getColumns(); j++) {
        Rectangle2D screenRect = new Rectangle2D(PageCanvasUtility.getColumnStartPos(widths, j),
            PageCanvasUtility.getRowStartPos(heights, i), widths.get(j), heights.get(i));
        Rectangle2D videoRect = new Rectangle2D(videoData.getXpos().get(),
            videoData.getYpos().get(),
            videoData.getWidth().get(), videoData.getHeight().get());
        Rectangle2D intersection = getIntersection(screenRect, videoRect);
        if (intersection == null) {
          continue;
        }

        VideoObject newVideo = func.createVideo();
        videoData.copyTo(newVideo);
        newVideo.getName().set(func.getUniqueName(exculdeItems));
        exculdeItems.add(newVideo.getName().get());
        newVideo.getWidth().set((int) intersection.getWidth());
        newVideo.getHeight().set((int) intersection.getHeight());
        newVideo.getXpos().set((int) intersection.getMinX());
        newVideo.getYpos().set((int) intersection.getMinY());
        result.add(newVideo);
      }
    }
    return result;
  }

  /**
   * 分割视频，分割成网格状.
   *
   * @param videoData 要被分割的视频窗口
   * @param rows      分割的行
   * @param columns   分割的列
   * @return 分割出来的视频窗口
   */
  public static Collection<VideoObject> splitVideo(VideoWallFunc func, VideoObject videoData,
                                                   int rows, int columns) {
    List<VideoObject> result = new ArrayList<>();
    int width = videoData.getWidth().get();
    int height = videoData.getHeight().get();
    int splitWidth = width / columns;
    int splitHeight = height / rows;
    if (splitWidth < 100 || splitHeight < 100) {
      log.warn("Width or height is too small!");
      return result;
    }

    Set<String> exculdeItems = new HashSet<>();
    int leftHeight = height;
    int ypos = videoData.getYpos().get();
    for (int i = 0; i < rows; i++) {
      int xpos = videoData.getXpos().get();
      int targetHeight = splitHeight;
      if (i == rows - 1) {
        targetHeight = leftHeight;
      }
      int leftWidth = width;
      for (int j = 0; j < columns; j++) {
        int targetWidth = splitWidth;
        if (j == columns - 1) {
          targetWidth = leftWidth;
        }
        VideoObject newVideo = func.createVideo();
        videoData.copyTo(newVideo);
        newVideo.getName().set(func.getUniqueName(exculdeItems));
        exculdeItems.add(newVideo.getName().get());
        newVideo.getWidth().set(targetWidth);
        newVideo.getHeight().set(targetHeight);
        newVideo.getXpos().set(xpos);
        newVideo.getYpos().set(ypos);
        result.add(newVideo);
        xpos += targetWidth;
        leftWidth -= targetWidth;
      } // end for j

      ypos += targetHeight;
      leftHeight -= targetHeight;
    } // end for i

    return result;
  }
}
