package com.mc.tool.framework.interfaces;

import com.mc.tool.framework.OemInfo;
import java.io.File;
import java.util.Collection;
import javafx.scene.image.Image;
import javafx.stage.Window;

/**
 * .
 */
public interface ApplicationBase extends AppConfig, AppManager {

  EntityFactory getEntityFactory();

  String getAppName();

  String getAppTitle();

  Image getIcon();

  Collection<String> getAppAdditionalStyleSheet();

  String getFullVersion();

  int getMainVersion();

  int getSubVersion();

  int getModifyVersion();

  String getExtraVersionInfo();

  Window getMainWindow();

  void setMainWindow(Window mainWindow);

  void importFile(File file);

  OemInfo getOemInfo();
}
