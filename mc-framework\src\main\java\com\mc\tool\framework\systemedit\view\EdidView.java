package com.mc.tool.framework.systemedit.view;

import com.mc.tool.framework.utility.I18nUtility;
import com.mc.tool.framework.utility.UndecoratedDialog;
import javafx.beans.binding.Bindings;
import javafx.beans.property.ObjectProperty;
import javafx.scene.Node;
import javafx.scene.control.Button;
import javafx.scene.control.ButtonType;
import javafx.scene.control.TextArea;
import javafx.scene.input.MouseEvent;
import javafx.scene.layout.VBox;
import javafx.stage.Window;
import lombok.Getter;

/**
 * .
 */
public class EdidView extends VBox {
  @Getter
  private final ObjectProperty<String> edidProperty;

  /**
   * Constructor.
   *
   * @param edidProperty edid的属性.
   */
  public EdidView(ObjectProperty<String> edidProperty) {
    this.edidProperty = edidProperty;

    TextArea textArea = new TextArea();
    textArea.setPrefSize(400, 200);
    textArea.setEditable(false);
    textArea.setWrapText(true);
    textArea.textProperty().bind(this.edidProperty);
    getChildren().add(textArea);
  }


  /**
   * 显示edid对话框.
   *
   * @param owner        owner
   * @param name         edid对应的设备的名称
   * @param edidProperty edid属性
   * @param load         是否可加载.
   */
  public static void showView(Window owner, String name, ObjectProperty<String> edidProperty,
                              boolean load) {
    UndecoratedDialog<ButtonType> dialog = new UndecoratedDialog<>();
    EdidView edidView = new EdidView(edidProperty);
    dialog.initOwner(owner);
    dialog.getDialogPane().setContent(edidView);
    dialog.getDialogPane().getButtonTypes().add(ButtonType.CLOSE);
    dialog.getDialogPane().getButtonTypes().add(ButtonType.APPLY);
    dialog.setTitle(name);

    Node node = dialog.getDialogPane().lookupButton(ButtonType.APPLY);
    if (node instanceof Button) {
      Button button = (Button) node;
      button.addEventFilter(MouseEvent.MOUSE_PRESSED, (event) -> {
        if (load) {
          String value = ViewUtility.loadEdid(owner);
          if (value != null) {
            edidProperty.set(value);
          }
        } else {
          ViewUtility.saveEdid(owner, edidProperty.get());
        }
        event.consume();
      });
      if (load) {
        button.setText(I18nUtility.getI18nBundle("systemedit").getString("view.load"));
      } else {
        button.setText(I18nUtility.getI18nBundle("systemedit").getString("view.save"));
        button.disableProperty()
            .bind(Bindings.isNull(edidProperty).or(Bindings.equal(edidProperty, "")));
      }
    }
    dialog.showAndWait();
  }
}
