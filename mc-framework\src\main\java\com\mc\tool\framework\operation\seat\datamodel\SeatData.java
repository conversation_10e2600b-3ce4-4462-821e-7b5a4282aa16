package com.mc.tool.framework.operation.seat.datamodel;

import com.google.gson.annotations.Expose;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import javafx.beans.property.ObjectProperty;
import javafx.beans.property.ReadOnlyStringProperty;
import javafx.beans.property.SimpleObjectProperty;
import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.StringProperty;
import javafx.beans.value.ChangeListener;
import javafx.beans.value.ObservableValue;
import javafx.collections.FXCollections;
import javafx.collections.ObservableMap;
import lombok.Getter;
import lombok.Setter;

/**
 * .
 */
public class SeatData {

  @Getter
  @Expose
  private StringProperty name = new SimpleStringProperty();

  @Getter
  @Expose
  private ObservableMap<VisualEditTerminal, SeatConnection> connections =
      FXCollections.observableHashMap();

  /**
   * .
   */
  public static class SeatConnection {
    @Getter
    @Setter
    @Expose
    private ObjectProperty<VisualEditTerminal> tx = new SimpleObjectProperty<>();
    @Getter
    @Expose
    private VisualEditTerminal rx;
    @Getter
    @Expose
    private ObjectProperty<SeatMode> mode = new SimpleObjectProperty<>(SeatMode.DISCONNECT);

    private StringProperty nameWithSource = new SimpleStringProperty();

    private final ChangeListener<VisualEditTerminal> changeListener;

    public SeatConnection() {
      changeListener = (obs, oldVal, newVal) -> updateNameWithSource();
    }

    public void setRx(VisualEditTerminal rx) {
      this.rx = rx;
      updateNameWithSource();
    }

    /**
     * 获取包含名字与源的字符串.
     */
    public ReadOnlyStringProperty getNameWithSource() {
      //由于要考虑到反序列化，不能在构造函数添加监听，只能在这里监听
      tx.removeListener(changeListener);
      tx.addListener(changeListener);
      updateNameWithSource();
      return nameWithSource;
    }

    protected void updateNameWithSource() {
      if (rx == null) {
        nameWithSource.unbind();
        nameWithSource.set("");
      } else {
        ObservableValue<String> value;
        if (tx.get() == null) {
          value = rx.nameProperty();
        } else {
          value = rx.nameProperty().concat("[").concat(tx.get().nameProperty()).concat("]");
        }
        nameWithSource.bind(value);
      }
    }
  }

  /**
   * .
   */
  public enum SeatMode {
    DISCONNECT, VIDEO, FULL
  }

  /**
   * 复制数据.
   *
   * @param seatData 数据
   */
  public void copyTo(SeatData seatData) {
    seatData.getName().set(getName().get());
    for (SeatConnection connection : connections.values()) {
      SeatConnection newConnection = new SeatConnection();
      newConnection.setRx(connection.getRx());
      newConnection.getTx().set(connection.getTx().get());
      newConnection.getMode().set(connection.getMode().get());
      seatData.getConnections().put(newConnection.getRx(), newConnection);
    }
  }
}
