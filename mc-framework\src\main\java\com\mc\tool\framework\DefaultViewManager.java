package com.mc.tool.framework;

import com.mc.tool.framework.event.PageVisibilityChangeEvent;
import com.mc.tool.framework.event.SwitchPageEvent;
import com.mc.tool.framework.interfaces.Entity;
import com.mc.tool.framework.interfaces.ViewManager;
import com.mc.tool.framework.utility.EventBusProvider;

/**
 * .
 */
public class DefaultViewManager implements ViewManager {

  @Override
  public void switchToPage(Entity entity, String pageName, Object showObject) {
    SwitchPageEvent event = new SwitchPageEvent();
    event.setEntity(entity);
    event.setPageName(pageName);
    event.setShowObject(showObject);
    EventBusProvider.getEventBus().post(event);
  }

  @Override
  public void setPageVisible(String pageName, boolean visible) {
    PageVisibilityChangeEvent event = new PageVisibilityChangeEvent();
    event.setPageName(pageName);
    event.setVisible(visible);
    EventBusProvider.getEventBus().post(event);
  }

}
