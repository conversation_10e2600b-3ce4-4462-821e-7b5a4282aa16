package com.mc.graph.interfaces;

import javafx.beans.property.DoubleProperty;
import javafx.geometry.Rectangle2D;
import javafx.scene.Parent;

public interface OverviewableGraphCanvas extends GraphCanvas {
  DoubleProperty getHorizontalPosProperty();

  DoubleProperty getVerticalPosProperty();

  DoubleProperty getHorizontalVisibleProperty();

  DoubleProperty getVerticalVisibleProperty();
  
  OverviewCanvas createOverviewCanvas();
  
  boolean isCellScaleEnable();
  
  DoubleProperty getCellScaleProperty();
  
  void centerRegion(Rectangle2D region);
  
  interface OverviewCanvas {
    /**
     * 获取用于存放graph的container.
     * @return container
     */
    Parent getGraphContainer();
    
    /**
     * 获取用于作为scroll content的container.
     * @return container
     */
    Parent getContainer();
    
    void relocateContainer(double xpos, double ypos, double scaleFactor);
  }
}
