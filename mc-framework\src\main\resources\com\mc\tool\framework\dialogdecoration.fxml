<?xml version="1.0" encoding="UTF-8"?>


<?import javafx.geometry.Point3D?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.Cursor?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.HBox?>
<AnchorPane id="decorationRoot" fx:id="decorationRoot"
            maxHeight="-1.0" maxWidth="-1.0" minHeight="-1.0" minWidth="-1.0"
            pickOnBounds="false" snapToPixel="true"
            styleClass="decoration-resize" stylesheets="@dialogdecoration.css"
            xmlns:fx="http://javafx.com/fxml/1">

  <HBox alignment="CENTER_LEFT" AnchorPane.leftAnchor="0"
        AnchorPane.topAnchor="-6" styleClass="decorator-sub-container-left">
    <Label prefWidth="16" prefHeight="16" styleClass="dialog-logo"/>
    <Label fx:id="title" mouseTransparent="true">
    </Label>
  </HBox>


  <HBox styleClass="decorator-sub-container-right" alignment="CENTER_RIGHT"
        AnchorPane.rightAnchor="9" AnchorPane.topAnchor="-6">
    <Button fx:id="close" mnemonicParsing="false" pickOnBounds="true"
            styleClass="decoration-button-close" text="">
      <rotationAxis>
        <Point3D/>
      </rotationAxis>
      <cursor>
        <Cursor fx:constant="DEFAULT"/>
      </cursor>
    </Button>
  </HBox>
</AnchorPane>
