package com.mc.tool.framework.operation.crossscreen.controller;

import com.mc.tool.framework.operation.interfaces.OperationControllable;
import com.mc.tool.framework.systemedit.datamodel.VisualEditFunc;
import com.mc.tool.framework.systemedit.datamodel.VisualEditModel;
import com.mc.tool.framework.systemedit.datamodel.VisualEditTerminal;
import com.mc.tool.framework.utility.TypeWrapper;
import java.util.Collection;

/**
 * .
 */
public interface CrossScreenControllable extends OperationControllable {
  void init(VisualEditModel model, VisualEditFunc currentFunction);

  boolean isConnetable(VisualEditTerminal tx, VisualEditTerminal rx);

  void connectForCrossScreen(VisualEditTerminal tx, VisualEditTerminal rx);

  void beginUpdate();

  void endUpdate();

  Collection<TypeWrapper> getMultiviewChannel(VisualEditTerminal rx);

  void connectMultiview(VisualEditTerminal tx, VisualEditTerminal rx, int mode, int channel);
}
