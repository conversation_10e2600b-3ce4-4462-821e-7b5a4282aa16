package com.mc.tool.framework.systemedit.controller;

import com.mc.graph.interfaces.CellObject;
import com.mc.graph.interfaces.CellPropertyUpdater;
import com.mc.graph.interfaces.ConnectorSkin;
import com.mc.tool.framework.systemedit.datamodel.SystemEditDefinition;
import java.util.Collection;
import java.util.HashSet;
import java.util.Set;
import javafx.beans.value.ChangeListener;

/**
 * .
 */
public class CellYposUpdater implements CellPropertyUpdater {
  private CellObject cellObject;

  private Set<ConnectorSkin> relatedMatrixConnectorSkins = new HashSet<>();

  private ChangeListener<Number> changeListener;

  private boolean attached = false;

  public CellYposUpdater(CellObject cell) {
    this.cellObject = cell;
    changeListener = (obs, oldVal, newVal) -> cellObject.getYProperty().set(calculateYpos());
  }

  protected double calculateYpos() {
    double sum = 0;
    for (ConnectorSkin skin : relatedMatrixConnectorSkins) {
      sum += skin.getContainerYposProperty().get();
    }
    return sum / relatedMatrixConnectorSkins.size() - SystemEditDefinition.CELL_Y_OFFSET;
  }

  /**
   * 设置相关的矩阵的connector的skin的集合.
   */
  public void setRelatedMatrixConnectorSkins(Collection<ConnectorSkin> skins) {
    if (relatedMatrixConnectorSkins.containsAll(skins)
        && relatedMatrixConnectorSkins.size() == skins.size()) {
      attach();
      return;
    }
    detach();
    relatedMatrixConnectorSkins.clear();
    relatedMatrixConnectorSkins.addAll(skins);
    attach();
  }

  @Override
  public void destroy() {
    detach();
    relatedMatrixConnectorSkins.clear();
    cellObject = null;
    changeListener = null;
  }

  @Override
  public void detach() {
    for (ConnectorSkin skin : relatedMatrixConnectorSkins) {
      skin.getContainerYposProperty().removeListener(changeListener);
    }
    attached = false;
  }

  @Override
  public void attach() {
    if (attached) {
      return;
    }

    for (ConnectorSkin skin : relatedMatrixConnectorSkins) {
      skin.getContainerYposProperty().addListener(changeListener);
    }

    cellObject.getYProperty().set(calculateYpos());
    attached = true;
  }

}
