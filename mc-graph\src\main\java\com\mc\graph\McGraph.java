package com.mc.graph;

import com.google.common.eventbus.EventBus;

import com.mc.graph.interfaces.CellBehavior;
import com.mc.graph.interfaces.CellBindedObject;
import com.mc.graph.interfaces.CellObject;
import com.mc.graph.interfaces.CellSkin;
import com.mc.graph.interfaces.Connector;
import com.mc.graph.interfaces.ConnectorBehavior;
import com.mc.graph.interfaces.ConnectorIdentifier;
import com.mc.graph.interfaces.ConnectorSkin;
import com.mc.graph.interfaces.GraphCanvas;
import com.mc.graph.interfaces.GraphController;
import com.mc.graph.interfaces.GraphModel;
import com.mc.graph.interfaces.LinkBehavior;
import com.mc.graph.interfaces.LinkObject;
import com.mc.graph.interfaces.NewLinkSkin;
import com.mc.graph.interfaces.OverviewableGraphCanvas;
import com.mc.graph.interfaces.OverviewableGraphCanvas.OverviewCanvas;
import com.mc.graph.interfaces.SkinFactory;
import com.mc.graph.interfaces.SkinManager;
import com.mc.graph.util.GraphSelectionModel;
import com.mc.graph.util.GraphSelectionModelImpl;
import com.mc.graph.util.MouseControlUtil;
import com.mc.graph.util.SelectableNode;
import com.mc.graph.util.SkinUtil;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.collections.ListChangeListener;
import javafx.geometry.Bounds;
import javafx.geometry.Rectangle2D;
import javafx.scene.Node;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.scene.SnapshotParameters;
import javafx.scene.image.Image;
import javafx.scene.image.WritableImage;
import javafx.scene.input.MouseEvent;
import javafx.scene.paint.Color;
import javafx.scene.shape.Rectangle;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Slf4j
public class McGraph implements GraphController {
  protected OverviewableGraphCanvas canvas;
  protected McOverviewCanvas overviewCanvas;
  protected McGraphModel graphModel;

  protected SkinFactory skinFactory;
  protected SkinManager skinManager;

  protected GraphSelectionModel selectionModel = new GraphSelectionModelImpl();

  private SimpleBooleanProperty cellResizableProperty = new SimpleBooleanProperty(true);
  private SimpleBooleanProperty cellMovableProperty = new SimpleBooleanProperty(true);
  private SimpleBooleanProperty cellDeletableProperty = new SimpleBooleanProperty(true);
  private SimpleBooleanProperty cellRotableProperty = new SimpleBooleanProperty(true);
  private SimpleBooleanProperty cellLinkableProperty = new SimpleBooleanProperty(true);
  private SimpleBooleanProperty cellCloneablePropety = new SimpleBooleanProperty(true);
  private SimpleBooleanProperty cellOrderableProperty = new SimpleBooleanProperty(false);

  private EventBus eventBus = new EventBus("mc-graph");

  private ListChangeListener<CellObject> cellChangeListener;
  private ListChangeListener<LinkObject> linkChangeListener;

  public McGraph() {}

  /**
   * 初始化graph,创建后必须先初始化.
   */
  public void init() {
    skinFactory = createSkinFactory();
    skinManager = createSkinManager();
    graphModel = createGraphModel();
    canvas = createGraphCanvas();
    overviewCanvas = new McOverviewCanvas(graphModel, skinFactory, canvas);

    Rectangle rect = new Rectangle();
    rect.setStroke(new Color(1, 1, 1, 1));
    rect.setFill(new Color(0, 0, 0, 0.5));
    MouseControlUtil.addSelectionRectangleGesture((Parent) canvas.getContainer(), rect,
        selectionModel);

    graphModel.getObservableCells()
        .addListener(cellChangeListener = new ListChangeListener<CellObject>() {

          @Override
          public void onChanged(Change<? extends CellObject> change) {
            while (change.next()) {
              for (CellObject cell : change.getAddedSubList()) {
                CellSkin skin = skinManager.getCellSkin(cell);
                if (skin == null) {
                  skin = createCellSkin(cell);
                }
                skin.add();
              }

              for (CellObject cell : change.getRemoved()) {
                // 删除时取消选择
                if (skinManager.getCellSkin(cell) == null) {
                  continue;
                }
                getSelectionModel().select(skinManager.getCellSkin(cell), false);
                skinManager.getCellSkin(cell).remove();
                skinManager.removeCellSkin(cell);
              }
            }
            
            //重新排序skin，用这种方式实现是为了避免对mcgraph做重构来实现cell的order调整
            //这种方式并不适合于有超过100个cell的情况.
            if (isCellOrderable()) {
              for (CellObject cellObject : getGraphModel().getObservableCells()) {
                CellSkin skin = skinManager.getCellSkin(cellObject);
                skin.remove();
              }
              
              for (CellObject cellObject : getGraphModel().getObservableCells()) {
                CellSkin skin = skinManager.getCellSkin(cellObject);
                skin.add();
              }
            }
          }
        });

    graphModel.getObservableLinks()
        .addListener(linkChangeListener = new ListChangeListener<LinkObject>() {

          @Override
          public void onChanged(Change<? extends LinkObject> change) {
            while (change.next()) {
              for (LinkObject link : change.getAddedSubList()) {
                skinManager.getLinkSkin(link).add();
              }

              for (LinkObject link : change.getRemoved()) {
                skinManager.getLinkSkin(link).remove();
                skinManager.removeLinkSkin(link);
              }
            }
          }

        });
  }

  public GraphCanvas getCanvas() {
    return canvas;
  }

  public Node getOverviewCanvas() {
    return overviewCanvas;
  }

  /**
   * Insert a cell to graph.
   * 
   * @param id cell's id
   * @param value cell's value
   * @param xpos cell's x position
   * @param ypos cell's y position
   * @param width cell's width
   * @param height cell's height
   * @param type cell's type
   * @param connectorCnt default created connector cnt, the ids of the created connectors are 0 ~
   *        connectorCnt - 1
   * @return cell's instance
   */
  public CellObject insertCell(String id, Object value, double xpos, double ypos, double width,
      double height, String type, int connectorCnt) {
    CellObject cell = insertCell(id, value, xpos, ypos, width, height, type);
    Connector[] connectorArray = new Connector[connectorCnt];
    for (int i = 0; i < connectorCnt; i++) {
      McConnector connector = new McConnector(ConnectorIdentifier.getIdentifier(id), cell);
      connectorArray[i] = connector;
    }
    cell.addConnector(connectorArray);
    return cell;
  }

  /**
   * Insert a cell to graph.
   * 
   * @param id cell's id
   * @param value cell's value
   * @param xpos cell's x position
   * @param ypos cell's y position
   * @param width cell's width
   * @param height cell's height
   * @param type cell's type
   * @param cellBindedObject binded object
   * @return cell instance
   */
  public CellObject insertCell(String id, Object value, double xpos, double ypos, double width,
      double height, String type, CellBindedObject cellBindedObject) {
    CellObject cell = createCell(id, value, xpos, ypos, width, height, type, cellBindedObject);
    graphModel.appendCells(cell);
    return cell;
  }



  /**
   * Insert a cell to graph.
   * 
   * @param id cell's id
   * @param value cell's value
   * @param xpos cell's x position
   * @param ypos cell's y position
   * @param width cell's width
   * @param height cell's height
   * @param type cell's type
   * @return cell's instance
   */
  public CellObject insertCell(String id, Object value, double xpos, double ypos, double width,
      double height, String type) {
    CellObject cell = createCell(id, value, xpos, ypos, width, height, type);
    graphModel.appendCells(cell);
    return cell;
  }

  /**
   * Overload Insert a cell to graph.
   * 
   * @param id cell's id
   * @param value cell's value
   * @param xpos cell's x position
   * @param ypos cell's y position
   * @param width cell's width
   * @param height cell's height
   * @param angle cell's angle
   * @param type cell's type
   * @param cellBindedObject binded object
   * @return cell instance
   */
  public CellObject insertCell(String id, Object value, double xpos, double ypos, double width,
      double height, double angle, String type, CellBindedObject cellBindedObject) {
    CellObject cell =
        createCell(id, value, xpos, ypos, width, height, angle, type, cellBindedObject);
    graphModel.appendCells(cell);
    return cell;
  }

  /**
   * Create a cell but not add to the graph.
   * 
   * @param id cell's id
   * @param value cell's value
   * @param xpos cell's x position
   * @param ypos cell's y position
   * @param width cell's width
   * @param height cell's height
   * @param type cell's type
   * @param cellBindedObject binded object
   * @return cell instance
   */
  public CellObject createCell(String id, Object value, double xpos, double ypos, double width,
      double height, String type, CellBindedObject cellBindedObject) {
    CellObject cell = createCell(id, value, xpos, ypos, width, height, type);
    cell.setBindedObject(cellBindedObject);
    return cell;
  }

  /**
   * Create a cell but not add to the graph.
   * 
   * @param id cell's id
   * @param value cell's value
   * @param xpos cell's x position
   * @param ypos cell's y position
   * @param width cell's width
   * @param height cell's height
   * @param type cell's type
   * @return cell's instance
   */
  public CellObject createCell(String id, Object value, double xpos, double ypos, double width,
      double height, String type) {
    CellObject cell = new McCell();
    cell.getXProperty().set(xpos);
    cell.getYProperty().set(ypos);
    cell.getWidthProperty().set(width);
    cell.getHeightProperty().set(height);
    cell.getValueProperty().set(value);
    cell.setType(type);

    createCellSkin(cell);
    return cell;
  }

  /**
   * Overload create a cell.
   * 
   * @param id cell's id
   * @param value cell's value
   * @param xpos cell's x position
   * @param ypos cell's y position
   * @param width cell's width
   * @param height cell's height
   * @param angle cell's angle
   * @param type cell's type
   * @param cellBindedObject binded object
   * @return cell instance
   */
  public CellObject createCell(String id, Object value, double xpos, double ypos, double width,
      double height, double angle, String type, CellBindedObject cellBindedObject) {
    CellObject cell = createCell(id, value, xpos, ypos, width, height, type, cellBindedObject);
    cell.getAngleProperty().set(angle);
    return cell;
  }
  
  protected CellSkin createCellSkin(CellObject cell) {
    CellSkin skin;
    if (canvas.isCellScaleEnable()) {
      skin = skinFactory.createScaleCellSkin(cell, canvas.getContainer(), canvas.getContainer(),
          skinManager, canvas.getCellScaleProperty());
    } else {
      skin = skinFactory.createCellSkin(cell, canvas.getContainer(), canvas.getContainer(),
          skinManager);
    }
    createCellBehavior(skin);
    return skin;
  }

  @Override
  public void createCellBehavior(CellSkin cellSkin) {
    CellBehavior behavior = new DefaultCellBehavior(this, cellSkin);
    cellSkin.addCellBehavior(behavior);
  }

  @Override
  public void createConnectorBehavior(ConnectorSkin connectorSkin) {
    ConnectorBehavior behavior = new DefaultConnectorBehavior(this, connectorSkin);
    connectorSkin.addBehavior(behavior);
  }

  @Override
  public LinkBehavior createNewLinkBehavior(NewLinkSkin linkSkin) {
    LinkBehavior behavior = new DefaultNewLinkBehavior(this, linkSkin);
    linkSkin.setBehavior(behavior);
    return behavior;
  }

  @Override
  public SkinFactory getSkinFactory() {
    return skinFactory;
  }

  @Override
  public LinkObject connect(Connector first, Connector second, boolean addToGraph) {
    LinkObject linkObject = graphModel.findLinks(first, second, true);
    if (linkObject == null) {
      linkObject = new McLink();
      linkObject.setConnectors(first, second);

      getSkinFactory().createLinkSkin(linkObject, canvas.getContainer(), getCanvas().getContainer(),
          skinManager);
      if (addToGraph) {
        graphModel.appendLinks(linkObject);
      }
    }
    return linkObject;
  }

  @Override
  public LinkObject disconnect(Connector first, Connector second) {
    LinkObject link = graphModel.findLinks(first, second, false);
    graphModel.removeLinks(link);
    return link;
  }

  @Override
  public CellSkin[] getHittedCellskin(MouseEvent event) {
    List<CellSkin> list = new ArrayList<>();
    Node root = getCanvas().getContainer();
    final double parentScaleX = root.localToSceneTransformProperty().getValue().getMxx();
    final double parentScaleY = root.localToSceneTransformProperty().getValue().getMyy();

    final double translateX = -root.localToSceneTransformProperty().getValue().getTx();
    final double translateY = -root.localToSceneTransformProperty().getValue().getTy();

    double xpos = event.getSceneX() / parentScaleX + translateX / parentScaleX;
    double ypos = event.getSceneY() / parentScaleY + translateY / parentScaleY;
    for (CellSkin cellSkin : getSkinManager().getAllCellSkin()) {
      if (cellSkin.getRegion().getBoundsInParent().contains(xpos, ypos)) {
        list.add(cellSkin);
      }
    }
    return list.toArray(new CellSkin[0]);
  }

  @Override
  public GraphModel getGraphModel() {
    return graphModel;
  }

  @Override
  public boolean isCellDeletable() {
    return cellDeletableProperty.get();
  }

  @Override
  public boolean isCellMovable() {
    return cellMovableProperty.get();
  }

  @Override
  public boolean isCellResizable() {
    return cellResizableProperty.get();
  }

  @Override
  public boolean isCellLinkable() {
    return cellLinkableProperty.get();
  }

  @Override
  public boolean isCellRotatable() {
    return cellRotableProperty.get();
  }

  @Override
  public boolean isCellCloneable() {
    return cellCloneablePropety.get();
  }

  @Override
  public boolean isCellOrderable() {
    return cellOrderableProperty.get();
  }

  public void setCellDeletable(boolean deletable) {
    cellDeletableProperty.set(deletable);
  }

  public void setCellMovable(boolean movable) {
    cellMovableProperty.set(movable);
  }

  public void setCellResizable(boolean resizable) {
    cellResizableProperty.set(resizable);
  }

  public void setCellLinkable(boolean linkable) {
    cellLinkableProperty.set(linkable);
  }

  public void setCellRotatable(boolean rotatable) {
    cellRotableProperty.set(rotatable);
  }

  public void setCellOrderable(boolean orderable) {
    cellOrderableProperty.set(orderable);
  }

  public BooleanProperty cellDeletable() {
    return cellDeletableProperty;
  }

  public BooleanProperty cellMovable() {
    return cellMovableProperty;
  }

  public BooleanProperty cellResizable() {
    return cellResizableProperty;
  }

  public BooleanProperty cellLinkable() {
    return cellLinkableProperty;
  }

  public BooleanProperty cellRotatable() {
    return cellRotableProperty;
  }

  public BooleanProperty cellOrderable() {
    return cellOrderableProperty;
  }

  @Override
  public CellObject[] removeCells(CellObject... cells) {
    for (CellObject cell : cells) {
      LinkObject[] links = graphModel.getRelatedLink(cell);
      graphModel.removeLinks(links);
    }
    return graphModel.removeCells(cells);
  }

  @Override
  public void orderCell(CellObject cell, int index) {
    graphModel.orderCell(cell, index);
  }
  
  @Override
  public void centerCell(CellObject cell) {
    CellSkin skin = getSkinManager().getCellSkin(cell);
    if (skin == null) {
      log.warn("Fail to find skin!");
      return;
    }

    Bounds bounds = skin.getRegion().getBoundsInParent();
    Rectangle2D rectangle2d =
        new Rectangle2D(bounds.getMinX(), bounds.getMinY(), bounds.getWidth(), bounds.getHeight());
    canvas.centerRegion(rectangle2d);
  }

  @Override
  public CellObject[] retainCells(CellObject... cells) {
    Set<CellObject> allCells = new HashSet<>(graphModel.getObservableCells());
    Set<CellObject> inputCells = new HashSet<>(Arrays.asList(cells));
    if (allCells.equals(inputCells)) {
      return new CellObject[0];
    }
    
    allCells.removeAll(inputCells);

    inputCells.removeAll(graphModel.getObservableCells());
    if (!inputCells.isEmpty()) {
      graphModel.appendCells(inputCells.toArray(new CellObject[0]));
    }
    return removeCells(allCells.toArray(new CellObject[0]));
  }

  @Override
  public LinkObject[] retainLinks(LinkObject... links) {
    Set<LinkObject> allLinks = new HashSet<>(graphModel.getObservableLinks());
    Set<LinkObject> inputLinks = new HashSet<>(Arrays.asList(links));
    if (allLinks.equals(inputLinks)) {
      return new LinkObject[0];
    }
    allLinks.removeAll(inputLinks);

    inputLinks.removeAll(graphModel.getObservableLinks());
    if (!inputLinks.isEmpty()) {
      graphModel.appendLinks(inputLinks.toArray(new LinkObject[0]));
    }
    return graphModel.removeLinks(allLinks.toArray(new LinkObject[0]));
  }

  @Override
  public SkinManager getSkinManager() {
    return skinManager;
  }

  @Override
  public GraphSelectionModel getSelectionModel() {
    return selectionModel;
  }

  @Override
  public GraphCanvas getGraphCanvas() {
    return canvas;
  }

  @Override
  public CellObject[] removeSelectedCells() {
    List<CellObject> needToRemoveCells = new ArrayList<>();
    for (SelectableNode node : selectionModel.getSelectedItems()) {
      CellSkin skin = (CellSkin) node;
      needToRemoveCells.add(skin.getCell());
    }
    return removeCells(needToRemoveCells.toArray(new CellObject[0]));
  }

  @Override
  public EventBus getEventBus() {
    return eventBus;
  }

  protected SkinFactory createSkinFactory() {
    return new DefaultSkinFactory();
  }

  protected SkinManager createSkinManager() {
    return new DefaultSkinManager();
  }

  protected McGraphModel createGraphModel() {
    return new McGraphModel(this);
  }

  protected OverviewableGraphCanvas createGraphCanvas() {
    return new McGraphCanvas();
  }



  @Override
  public Image generateImage() {
    OverviewCanvas overviewCanvas = canvas.createOverviewCanvas();
    SkinManager skinManager = createSkinManager();
    for (CellObject cellObject : graphModel.getObservableCells()) {
      SkinUtil.addCellSkin(canvas, overviewCanvas, cellObject, skinFactory, skinManager);
    }
    for (LinkObject linkObject : graphModel.getObservableLinks()) {
      SkinUtil.addLinkSkin(overviewCanvas, linkObject, skinFactory, skinManager);
    }

    Scene scene = new Scene(overviewCanvas.getContainer());
    scene.getStylesheets().addAll(canvas.getNode().getScene().getStylesheets());
    Bounds bounds = overviewCanvas.getContainer().getLayoutBounds();
    int width = (int) bounds.getWidth();
    int height = (int) bounds.getHeight();
    log.info("Ready to take graph snapshot. Size: {} * {}.", width, height);

    int minX = (int) bounds.getMinX();
    int minY = (int) bounds.getMinY();
    int maxX = minX + width;
    int maxY = minY + height;
    WritableImage result = new WritableImage(width, height);
    int idealWidth = 1920;
    int idealHeight = 1080;
    int xpos = minX;
    int ypos = 0;
    // 切割后组合图片
    while (xpos < maxX) {
      ypos = minY;
      int targetWidth = Math.min(idealWidth, maxX - xpos);
      while (ypos < maxY) {
        int targetHeight = Math.min(idealHeight, maxY - ypos);
        SnapshotParameters param = new SnapshotParameters();
        param.setViewport(new Rectangle2D(xpos, ypos, targetWidth, targetHeight));
        Image item = overviewCanvas.getContainer().snapshot(param, null);
        result.getPixelWriter()
            .setPixels(xpos - minX, ypos - minY,
                targetWidth, targetHeight, item.getPixelReader(), 0, 0);
        ypos += targetHeight;
      }
      xpos += targetWidth;
    }

    return result;
  }

  /**
   * 销毁数据.
   */
  public void destroy() {
    graphModel.getObservableCells().removeListener(cellChangeListener);
    graphModel.getObservableLinks().removeListener(linkChangeListener);
    overviewCanvas.destroy();
    skinManager.destroy();
    graphModel.destroy();
    
    cellCloneablePropety.unbind();
    cellDeletableProperty.unbind();
    cellLinkableProperty.unbind();
    cellOrderableProperty.unbind();
    cellMovableProperty.unbind();
    cellResizableProperty.unbind();
    cellRotableProperty.unbind();
  }
  
}
