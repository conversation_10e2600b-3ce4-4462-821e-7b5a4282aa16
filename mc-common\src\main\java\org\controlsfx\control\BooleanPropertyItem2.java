package org.controlsfx.control;

import javafx.beans.property.BooleanProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.beans.value.ObservableValue;

import java.util.Optional;

public class BooleanPropertyItem2 extends AbstractPropertyItem {

  private final BooleanProperty value;
  private BooleanProperty observableValue = null;

  public BooleanPropertyItem2(BooleanProperty value) {
    this.value = value;
  }

  @Override
  public Class<?> getType() {
    return Boolean.class;
  }

  @Override
  public Object getValue() {
    if (value == null) {
      return null;
    }
    return value.get();
  }

  @Override
  public void setValue(Object value) {
    if (this.value != null && value instanceof Boolean) {
      this.value.set((Boolean) value);
    }
  }

  @Override
  public Optional<ObservableValue<? extends Object>> getObservableValue() {
    if (observableValue == null) {
      observableValue = new SimpleBooleanProperty();
      observableValue.bind(value);
    }
    return Optional.of(observableValue);
  }

  @Override
  public void delete() {
    if (observableValue != null) {
      observableValue.unbind();
      observableValue = null;
    }
  }

}
