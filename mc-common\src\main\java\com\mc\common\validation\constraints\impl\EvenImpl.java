package com.mc.common.validation.constraints.impl;

import com.mc.common.validation.constraints.Even;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

public class EvenImpl implements ConstraintValidator<Even, Integer> {

  @Override
  public void initialize(Even constraintAnnotation) {}

  @Override
  public boolean isValid(Integer value, ConstraintValidatorContext context) {
    return value != null && value % 2 == 0;
  }

}
