package com.mc.tool.framework.operation.videowall.menu;

import com.mc.tool.framework.operation.videowall.controller.VideoWallControllable;
import com.mc.tool.framework.operation.videowall.datamodel.LayoutData;
import com.mc.tool.framework.operation.videowall.datamodel.interfaces.VideoObject;
import com.mc.tool.framework.utility.I18nUtility;
import java.util.Collection;

/**
 * .
 */
public class MenuMoveTo extends VideoWallMenuBase {
  private MoveAction action;

  /**
   * Constructor.
   *
   * @param controllable controllable
   * @param action       action
   */
  public MenuMoveTo(VideoWallControllable controllable, MoveAction action) {
    super(controllable);
    this.action = action;
    setText(action.getName());
  }

  @Override
  protected void onAction() {
    Collection<VideoObject> videos = controllable.getSelectedVideos();
    controllable.beginUpdate();
    try {
      for (VideoObject video : videos) {
        action.move(controllable.getVideoWallFunction().getVideoWallObject().getLayoutData(),
            video);
      }
    } finally {
      controllable.endUpdate();
    }

  }

  @Override
  protected String getMenuText() {
    return "";
  }

  /**
   * .
   */
  public enum MoveAction {
    LEFT_TOP_CORNOR(I18nUtility.getI18nBundle("operation").getString("menu.left_top_cornor")) {
      @Override
      public void move(LayoutData layoutData, VideoObject videoData) {
        videoData.getXpos().set(0);
        videoData.getYpos().set(0);
      }
    },
    RIGHT_TOP_CORNOR(I18nUtility.getI18nBundle("operation").getString("menu.right_top_cornor")) {
      @Override
      public void move(LayoutData layoutData, VideoObject videoData) {
        videoData.getXpos().set(layoutData.getTotalWidth() - videoData.getWidth().get());
        videoData.getYpos().set(0);
      }
    },
    LEFT_BOTTOM_CORNOR(
        I18nUtility.getI18nBundle("operation").getString("menu.left_bottom_cornor")) {
      @Override
      public void move(LayoutData layoutData, VideoObject videoData) {
        videoData.getXpos().set(0);
        videoData.getYpos().set(layoutData.getTotalHeight() - videoData.getHeight().get());
      }
    },
    RIGHT_BOTTOM_CORNOR(
        I18nUtility.getI18nBundle("operation").getString("menu.right_bottom_cornor")) {
      @Override
      public void move(LayoutData layoutData, VideoObject videoData) {
        videoData.getXpos().set(layoutData.getTotalWidth() - videoData.getWidth().get());
        videoData.getYpos().set(layoutData.getTotalHeight() - videoData.getHeight().get());
      }
    },
    CENTER(I18nUtility.getI18nBundle("operation").getString("menu.center")) {
      @Override
      public void move(LayoutData layoutData, VideoObject videoData) {
        videoData.getXpos().set((layoutData.getTotalWidth() - videoData.getWidth().get()) / 2);
        videoData.getYpos().set((layoutData.getTotalHeight() - videoData.getHeight().get()) / 2);
      }
    };

    private String name;

    MoveAction(String name) {
      this.name = name;
    }

    public String getName() {
      return name;
    }

    public void move(LayoutData layoutData, VideoObject videoData) {

    }
  }
}
