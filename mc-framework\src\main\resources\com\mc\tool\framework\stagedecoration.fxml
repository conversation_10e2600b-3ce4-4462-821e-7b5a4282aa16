<?xml version="1.0" encoding="UTF-8"?>


<?import javafx.geometry.Point3D?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.ContextMenu?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.Menu?>
<?import javafx.scene.control.MenuBar?>
<?import javafx.scene.control.MenuItem?>
<?import javafx.scene.Cursor?>
<?import javafx.scene.effect.Glow?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.HBox?>
<AnchorPane id="decorationRoot" fx:id="decorationRoot"
            maxHeight="-1.0" maxWidth="-1.0" minHeight="-1.0" minWidth="-1.0"
            pickOnBounds="false" prefHeight="400.0" prefWidth="600.0" snapToPixel="true"
            styleClass="decoration-resize" xmlns="http://javafx.com/javafx/8"
            xmlns:fx="http://javafx.com/fxml/1">

  <HBox alignment="CENTER_LEFT" AnchorPane.leftAnchor="0"
        AnchorPane.topAnchor="-7" styleClass="decorator-sub-container-left">
    <Button id="StageMenu" fx:id="menu" mnemonicParsing="false"
            pickOnBounds="true" styleClass="decoration-button-menu" text="">
      <cursor>
        <Cursor fx:constant="DEFAULT"/>
      </cursor>
      <contextMenu>
        <ContextMenu fx:id="contextMenu"/>
      </contextMenu>
    </Button>
  </HBox>
  <Label id="TitleLabel" fx:id="title" mouseTransparent="true"
         prefWidth="0" style="-fx-alignment: center;" text="Title bar">
    <effect>
      <Glow/>
    </effect>
  </Label>

  <HBox id="menu-container" AnchorPane.leftAnchor="200"
        AnchorPane.topAnchor="0" alignment="CENTER_LEFT">
    <MenuBar fx:id="menuBar">
      <Menu mnemonicParsing="false" text="%framework.menu.file" fx:id="menuFile">

        <items>
          <MenuItem mnemonicParsing="false" text="%framework.menu.close"
                    onAction="#onClose" fx:id="menuClose"/>
          <Menu text="%framework.menu.export" fx:id="exportMenu">
          </Menu>
        </items>
      </Menu>
      <Menu mnemonicParsing="false" text="%framework.menu.help" fx:id="menuHelp">
        <items>
          <MenuItem mnemonicParsing="false" text="%framework.menu.config"
                    onAction="#onConfig" fx:id="menuConfig"/>
          <MenuItem mnemonicParsing="false" text="%framework.menu.about"
                    onAction="#onAbout" fx:id="menuAbout"/>
        </items>
      </Menu>
    </MenuBar>
  </HBox>
  <HBox styleClass="decorator-sub-container-right" alignment="CENTER_RIGHT" AnchorPane.rightAnchor="9"
        AnchorPane.topAnchor="-7">
    <Button fx:id="minimize" mnemonicParsing="false" pickOnBounds="true"
            style="" styleClass="decoration-button-minimize" text="">
      <cursor>
        <Cursor fx:constant="DEFAULT"/>
      </cursor>
    </Button>
    <Button fx:id="maximize" mnemonicParsing="false" pickOnBounds="true"
            styleClass="decoration-button-maximize" text="">
      <cursor>
        <Cursor fx:constant="DEFAULT"/>
      </cursor>
    </Button>
    <Button fx:id="close" mnemonicParsing="false" pickOnBounds="true"
            styleClass="decoration-button-close" text="">
      <rotationAxis>
        <Point3D/>
      </rotationAxis>
      <cursor>
        <Cursor fx:constant="DEFAULT"/>
      </cursor>
    </Button>
  </HBox>
</AnchorPane>
