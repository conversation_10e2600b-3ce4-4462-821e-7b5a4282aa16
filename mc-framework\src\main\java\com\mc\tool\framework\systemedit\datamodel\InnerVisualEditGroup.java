package com.mc.tool.framework.systemedit.datamodel;

import com.google.gson.annotations.Expose;
import java.util.Collection;

/**
 * 内部使用的group，添加child的parent并不是而是parent.
 */
public class InnerVisualEditGroup extends VisualEditGroup {
  @Expose
  private final VisualEditNode parent;

  public InnerVisualEditGroup(VisualEditNode parent) {
    this.parent = parent;
  }

  public InnerVisualEditGroup() {
    parent = null;
  }

  @Override
  public void addChildren(int index, VisualEditNode... nodes) {
    Collection<VisualEditNode> list = addPreprocessing(nodes);
    for (VisualEditNode item : list) {
      item.setParent(parent);
    }
    if (index >= 0) {
      children.addAll(index, list);
    } else {
      children.addAll(list);
    }

  }

  @Override
  public void addChildren(VisualEditNode... node) {
    Collection<VisualEditNode> list = addPreprocessing(node);
    for (VisualEditNode item : list) {
      item.setParent(parent);
    }
    children.addAll(list);
  }

  @Override
  public void removeAndAdd(Collection<VisualEditNode> removeItems,
                           Collection<VisualEditNode> addItems, int addIndex) {
    for (VisualEditNode item : addItems) {
      item.setParent(parent);
    }
    children.removeAndAdd(removeItems, addItems, addIndex);
  }

  @Override
  public void recursiveInit() {
    for (VisualEditNode node : getChildren()) {
      node.recursiveInit();
      node.setParent(parent);
    }
    init();
  }
}
