package com.mc.tool.framework.operation.videowall.menu;

import com.mc.tool.framework.operation.videowall.controller.VideoWallControllable;
import com.mc.tool.framework.utility.I18nUtility;

/**
 * .
 */
public class MenuToTop extends VideoWallMenuBase {

  public MenuToTop(VideoWallControllable controllable) {
    super(controllable);
    setDisable(controllable.getSelectedVideos().size() != 1);
  }

  @Override
  protected void onAction() {
    controllable.moveToTop(controllable.getSelectedVideos().iterator().next());
  }

  @Override
  protected String getMenuText() {
    return I18nUtility.getI18nBundle("operation").getString("menu.to_top");
  }

}
