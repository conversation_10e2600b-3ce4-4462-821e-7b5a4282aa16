#### 简介
凯撒中文主机PC端管理软件。
#### 代码注意事项
1. 所有FXForm用到的bean类都必须以Bean为后缀
2. 所有FXML引用的自定义的控件都必须在proguard的配置文件中加上keep
3. 所有要用json序列化或者反序列化的字段都要加上@Expose
4. 引用资源时需要指定相对于根目录的路径，而不是相对于class的路径

#### 提交注意事项
1. 一次提交的所有修改必须在同一个项目下
2. 提交前需要执行maven install 来检查是否有错误，没有错误才能提交
3. 提交时必须写日志，格式为 catogory:message.
   catogory为提交的内容的类别，message为日志信息。
   catogory有如下几种：
   feat:添加、修改功能
   fix:修复bug
   refactor:重构
   test:测试
   chore:项目构建相关
   具体参考以前的日志。
   4.checkstyle问题可在IDEA中安装Checkstyle-IDEA插件提前检查，避免打包时检查浪费时间。

#### IDEA导入注意事项
1. Import Project， 选择mc-tool目录
2. Import project from external model，选择eclipse， 然后后面的按默认配置，直到完成
3. 在Project列表中找各个项目的pom.xml文件，右击pom.xml，选择Add as Maven project
4. 到菜单File -> Settings->Plugins，安装插件Lombok与Maven Helper
5. 此时应该所有项目都可以编译成功，如果遇到找不到包或者符号的问题，右击项目->Maven->Reimport

#### 运行前
1. 执行mvn templating:filter-sources -f mc-caesar-vpm/pom.xml,根据pom的revision生成Versions类
2. 将mc-caesar-vpm/target/generated-sources/设置为Generated Sources Root(在idea中,右键->Mark Directory as->Generated Sources Root)
3. 不要将mc-caesar-vpm/src/main/java-templates设置为Resources Root,防止与生成的Versions重复
