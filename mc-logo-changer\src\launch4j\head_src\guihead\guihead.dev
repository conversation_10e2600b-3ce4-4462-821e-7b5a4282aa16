[Project]
FileName=guihead.dev
Name=guihead
UnitCount=5
Type=0
Ver=1
ObjFiles=
Includes=
Libs=
PrivateResource=
ResourceIncludes=
MakeIncludes=
Compiler=
CppCompiler=
Linker=-n_@@_
IsCpp=0
Icon=
ExeOutput=
ObjectOutput=..\..\head
OverrideOutput=0
OverrideOutputName=guihead.exe
HostApplication=
Folders=
CommandLine=
UseCustomMakefile=1
CustomMakefile=Makefile.win
IncludeVersionInfo=0
SupportXPThemes=0
CompilerSet=0
CompilerSettings=0000000001001000000100

[Unit1]
FileName=guihead.c
CompileCpp=0
Folder=guihead
Compile=1
Link=1
Priority=1000
OverrideBuildCmd=0
BuildCmd=$(CC) -c guihead.c -o ../../head/guihead.o $(CFLAGS)

[Unit2]
FileName=guihead.h
CompileCpp=0
Folder=guihead
Compile=1
Link=1
Priority=1000
OverrideBuildCmd=0
BuildCmd=

[VersionInfo]
Major=0
Minor=1
Release=1
Build=1
LanguageID=1033
CharsetID=1252
CompanyName=
FileVersion=
FileDescription=Developed using the Dev-C++ IDE
InternalName=
LegalCopyright=
LegalTrademarks=
OriginalFilename=
ProductName=
ProductVersion=
AutoIncBuildNr=0

[Unit4]
FileName=..\head.h
CompileCpp=0
Folder=guihead
Compile=1
Link=1
Priority=1000
OverrideBuildCmd=0
BuildCmd=

[Unit6]
FileName=..\resid.h
CompileCpp=0
Folder=guihead
Compile=1
Link=1
Priority=1000
OverrideBuildCmd=0
BuildCmd=

[Unit3]
FileName=..\head.c
CompileCpp=0
Folder=guihead
Compile=1
Link=1
Priority=1000
OverrideBuildCmd=0
BuildCmd=

[Unit5]
FileName=..\resource.h
CompileCpp=0
Folder=guihead
Compile=1
Link=1
Priority=1000
OverrideBuildCmd=0
BuildCmd=

