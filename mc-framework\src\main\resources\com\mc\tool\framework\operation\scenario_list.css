
.scenario-left-btn {
    -fx-graphic: url("scenario_left_normal.png");
}

.scenario-left-btn:hover {
    -fx-graphic: url("scenario_left_hover.png");
}

.scenario-right-btn {
    -fx-graphic: url("scenario_right_normal.png");
}

.scenario-right-btn:hover {
    -fx-graphic: url("scenario_right_hover.png");
}

.scenario-list {
    -fx-background-color: transparent;
}

.scenario-list .scroll-bar:horizontal .increment-arrow,
.scenario-list .scroll-bar:horizontal .decrement-arrow,
.scenario-list .scroll-bar:horizontal .increment-button,
.scenario-list .scroll-bar:horizontal .decrement-button {
    -fx-padding: 0;
}

.scenario-list .list-cell {
    -fx-background-color: transparent;
}

.scenario-list-container {
    -fx-padding: 7;
    -fx-background-color: #f7f7f7;
}

