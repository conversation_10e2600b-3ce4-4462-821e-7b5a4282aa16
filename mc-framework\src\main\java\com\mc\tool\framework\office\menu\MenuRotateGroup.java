package com.mc.tool.framework.office.menu;

import com.mc.tool.framework.office.controller.OfficeControllable;
import com.mc.tool.framework.office.datamodel.OfficeData;
import com.mc.tool.framework.utility.FormDialog;
import com.mc.tool.framework.utility.I18nUtility;
import java.util.Optional;
import javafx.beans.property.IntegerProperty;
import javafx.beans.property.SimpleIntegerProperty;
import javafx.scene.control.ButtonType;
import javafx.scene.control.Menu;
import javafx.scene.control.MenuItem;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

/**
 * .
 */
public class MenuRotateGroup extends Menu {
  /**
   * Constructor.
   *
   * @param controllable controllable
   */
  public MenuRotateGroup(OfficeControllable controllable) {
    setText(I18nUtility.getI18nBundle("office").getString("menu.rotate"));
    getItems().add(new MenuRotate(controllable, 0));
    getItems().add(new MenuRotate(controllable, 45));
    getItems().add(new MenuRotate(controllable, 90));
    getItems().add(new MenuRotate(controllable, 135));
    getItems().add(new MenuRotate(controllable, 180));
    getItems().add(new MenuRotate(controllable, 225));
    getItems().add(new MenuRotate(controllable, 270));
    getItems().add(new MenuRotate(controllable, 315));
    getItems().add(new MenuRotateAny(controllable));

  }

  /**
   * .
   */
  public static class MenuRotate extends MenuItem {

    /**
     * Constructor.
     *
     * @param controllable controllable
     * @param angle        角度
     */
    public MenuRotate(OfficeControllable controllable, int angle) {
      this.setText(String.format("%d°", angle));
      this.setOnAction((event) -> {
        for (OfficeData data : controllable.getSelectedFuncs()) {
          data.angleProperty().set(angle);
        }
      });
    }
  }

  /**
   * .
   */
  public static class MenuRotateAny extends MenuItem {

    /**
     * Constructor.
     *
     * @param controllable controllable
     */
    public MenuRotateAny(OfficeControllable controllable) {
      this.setText(I18nUtility.getI18nBundle("office").getString("menu.rotate.any"));
      this.setOnAction((event) -> {
        showDialog(controllable);
      });
    }

    protected void showDialog(OfficeControllable controllable) {
      AngleBean bean = new AngleBean(0);
      if (controllable.getSelectedFuncs().size() == 1) {
        bean.setAngle(
            (int) controllable.getSelectedFuncs().iterator().next().angleProperty().get());
      }

      FormDialog<AngleBean> formDialog = new FormDialog<MenuRotateGroup.AngleBean>(bean,
          I18nUtility.getI18nBundle("anglebean"),
          getParentMenu().getParentPopup().getOwnerWindow());
      formDialog.getDialogPane().setMinSize(250, 150);
      Optional<ButtonType> formResult = formDialog.showAndWaitWithCommit();
      if (formResult.get() != null && formResult.get() == ButtonType.APPLY) {
        for (OfficeData data : controllable.getSelectedFuncs()) {
          data.angleProperty().set(bean.getAngle());
        }
      }
    }
  }

  /**
   * .
   */
  public static class AngleBean {
    private IntegerProperty angle = new SimpleIntegerProperty(0);

    public AngleBean(int initValue) {
      angle.set(initValue);
    }

    @Min(0)
    @Max(359)
    public int getAngle() {
      return angle.get();
    }

    public void setAngle(int angle) {
      this.angle.set(angle);
    }
  }
}
