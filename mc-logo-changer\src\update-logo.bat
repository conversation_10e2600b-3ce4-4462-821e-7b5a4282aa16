@echo off
where /q python
if ERRORLEVEL 1 (
	echo Can not find program python!
	exit /b
)
python -c "print('Test python version')"
if ERRORLEVEL 1 (
	echo Python version is error!
	exit /b
)
where /q 7z
if ERRORLEVEL 1 (
	echo Can not find program 7z!
	exit /b
)
where /q jar
if ERRORLEVEL 1 (
	echo Can not find program jar!
	exit /bcdedit
)

echo Deleting old files......
rd /S /Q output
ping -n 2 127.0.0.1 > nul
echo Delete successful
echo Unzipping the files......
7z x %1 -o./output/ -aoa
echo Unzip successful
echo Copying files......

copy ..\oem\about_logo.png .\output\com\mc\tool\framework\img\about_logo.png
copy ..\oem\logo.png .\output\icons\logo.png
copy ..\oem\Logo.ico .\output\icons\Logo.ico
copy ..\oem\title_logo.png .\output\com\mc\tool\framework\img\title_logo.png
copy ..\oem\%3_logo.png .\output\com\mc\tool\%3\vpm
copy ..\oem\oeminfo.json .\output\oeminfo.json

echo Copy successful

echo Packing jar file......
jar cvfm %1.jar Output/META-INF/MANIFEST.MF -C output/ .

echo Wrapping to exe......
python create-config.py %1.jar %2 ..\oem\oeminfo.json launch4j_config.xml %3
.\launch4j\launch4jc.exe launch4j_config.xml.new
echo Wrap exe successful

echo Clear files
del %1.jar
del launch4j_config.xml.new
