package com.mc.tool.framework.utility;

import insidefx.undecorator.UndecoratorScene;
import javafx.scene.Scene;
import javafx.scene.control.DialogEx;
import javafx.scene.control.DialogPaneEx;
import javafx.scene.control.HeavyweightDialogEx;
import javafx.scene.layout.Priority;
import javafx.scene.layout.Region;
import javafx.scene.layout.VBox;
import javafx.stage.Stage;
import javafx.stage.StageStyle;
import javafx.stage.Window;

/**
 * .
 */
public class UndecoratedHeavyWeightDialog extends HeavyweightDialogEx {
  private VBox container = null;

  public UndecoratedHeavyWeightDialog(DialogEx<?> dialog) {
    super(dialog);
  }

  @Override
  public void setDialogPane(DialogPaneEx dialogPane) {
    this.dialogPane = dialogPane;

    container = new VBox();
    Region header = new Region();
    header.getStyleClass().add("dialog-title-bar");
    container.getChildren().addAll(header, dialogPane);
    VBox.setVgrow(dialogPane, Priority.ALWAYS);

    UndecoratorScene undecoratorScene =
        new UndecoratorScene(stage, StageStyle.UNDECORATED, container,
            "/com/mc/tool/framework/dialogdecoration.fxml", I18nUtility.getI18nBundle("main"));
    undecoratorScene.getStylesheets().add(Thread.currentThread().getContextClassLoader()
        .getResource("com/mc/tool/framework/dialogdecoration.css").toExternalForm());
    dialogPane.autosize();
    container.autosize();
    scene = undecoratorScene;
    stage.setScene(scene);
    stage.sizeToScene();
  }

  @Override
  public void show() {
    setDialogPane(dialogPane);
    stage.centerOnScreen();
    stage.show();
  }

  @Override
  public void showAndWait() {
    setDialogPane(dialogPane);
    stage.centerOnScreen();
    stage.showAndWait();
  }

  @Override
  protected Stage createStage() {
    return new Stage() {
      @Override
      public void centerOnScreen() {
        Window owner = super.getOwner();
        if (owner != null) {
          positionStage();
        } else {
          if (getWidth() > 0 && getHeight() > 0) {
            super.centerOnScreen();
          }
        }
      }
    };
  }

  private void positionStage() {
    double xval = getX();
    double yval = getY();

    // if the user has specified an x/y location, use it
    if (!Double.isNaN(xval) && !Double.isNaN(yval) && Double.compare(xval, prefX) != 0
        && Double.compare(yval, prefY) != 0) {
      // weird, but if I don't call setX/setY here, the stage
      // isn't where I expect it to be (in instances where a single
      // dialog is shown and closed multiple times). I expect the
      // second showing to be in the place the dialog was when it
      // was closed the first time, but on Windows it jumps to the
      // top-left of the screen.
      setX(xval);
      setY(yval);
      return;
    }

    // Firstly we need to force CSS and layout to happen, as the dialogPane
    // may not have been shown yet (so it has no dimensions)
    if (container == null) {
      return;
    }

    dialogPane.applyCss();
    dialogPane.layout();
    double width = dialogPane.prefWidth(-1);
    double height = dialogPane.prefHeight(width);
    dialogPane.setPrefSize(width, height);

    container.applyCss();
    container.layout();

    final Window owner = getOwner();
    final Scene ownerScene = owner.getScene();

    // scene.getY() seems to represent the y-offset from the top of the titlebar to the
    // start point of the scene, so it is the titlebar height
    final double titleBarHeight = ownerScene.getY();

    // because Stage does not seem to centre itself over its owner, we
    // do it here.

    // then we can get the dimensions and position the dialog appropriately.
    final double dialogWidth = container.prefWidth(-1);
    final double dialogHeight = container.prefHeight(dialogWidth);

    xval = owner.getX() + (ownerScene.getWidth() / 2.0) - (dialogWidth / 2.0);
    yval =
        owner.getY() + titleBarHeight / 2.0 + (ownerScene.getHeight() / 2.0) - (dialogHeight / 2.0);

    prefX = xval;
    prefY = yval;

    setX(xval);
    setY(yval);
  }


}
