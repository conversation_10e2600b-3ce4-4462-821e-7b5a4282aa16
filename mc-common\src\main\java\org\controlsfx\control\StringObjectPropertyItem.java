package org.controlsfx.control;

import javafx.beans.property.ObjectProperty;

public class StringObjectPropertyItem extends ObjectPropertyItem<String> {

  public StringObjectPropertyItem(ObjectProperty<String> value) {
    super(value, String.class);
  }
  
  public StringObjectPropertyItem(ObjectProperty<String> value, boolean needToUnbindValue) {
    super(value, String.class, needToUnbindValue);
  }

  @Override
  public void setValue(Object value) {
    if (clazz.isInstance(value)) {
      this.value.set(clazz.cast(value));
    }
  }
}
