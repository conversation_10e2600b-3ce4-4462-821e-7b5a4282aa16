package com.mc.tool.framework.systemedit.controller;

import com.mc.graph.interfaces.CellObject;
import com.mc.graph.interfaces.CellPropertyUpdater;
import com.mc.tool.framework.systemedit.datamodel.VisualEditNode;
import javafx.beans.value.ChangeListener;

/**
 * .
 */
public class CellVisibilityUpdater implements CellPropertyUpdater {
  private final CellObject cell;
  private VisualEditNode parent = null;
  private boolean attached = false;

  private ChangeListener<Boolean> changeListener;

  /**
   * Constructor.
   *
   * @param cell 需要更新visibility的cell
   */
  public CellVisibilityUpdater(CellObject cell) {
    this.cell = cell;
    changeListener = (obs, oldVal, newVal) -> this.cell.visibleProperty().set(getVisibility());
  }

  protected boolean getVisibility() {
    if (parent != null && parent.getCellObject() != null) {
      return !parent.collapseProperty().get() && parent.getCellObject().visibleProperty().get();
    } else {
      return false;
    }
  }

  /**
   * 设置父节点.
   *
   * @param parent 父节点
   */
  public void setParent(VisualEditNode parent) {
    if (this.parent == parent) {
      attach();
      return;
    }
    detach();
    this.parent = parent;
    attach();
  }

  @Override
  public void destroy() {
    detach();
    parent = null;
    changeListener = null;
  }

  @Override
  public void detach() {
    if (this.parent != null) {
      parent.collapseProperty().removeListener(changeListener);
      if (this.parent.getCellObject() != null) {
        this.parent.getCellObject().visibleProperty().removeListener(changeListener);
      }
    }
    attached = false;
  }

  @Override
  public void attach() {
    if (attached) {
      return;
    }
    if (this.parent != null) {
      parent.collapseProperty().addListener(changeListener);
      if (this.parent.getCellObject() != null) {
        this.parent.getCellObject().visibleProperty().addListener(changeListener);
      }
    }

    if (this.cell != null) {
      this.cell.visibleProperty().set(getVisibility());
    }
    attached = true;
  }
}
