package com.mc.tool.framework.operation.videowall.menu;

import com.mc.tool.framework.operation.videowall.controller.VideoSplitMode;
import com.mc.tool.framework.operation.videowall.controller.VideoWallControllable;
import com.mc.tool.framework.operation.videowall.datamodel.interfaces.VideoObject;
import com.mc.tool.framework.utility.I18nUtility;
import java.util.Collection;
import javafx.scene.control.Menu;

/**
 * .
 */
public class MenuSplitGroup extends Menu {

  /**
   * Constructor.
   *
   * @param controllable controllable
   */
  public MenuSplitGroup(VideoWallControllable controllable) {
    // 只选中一个而且视频源为空时才能用
    Collection<VideoObject> videos = controllable.getSelectedVideos();
    if (controllable.getSelectedVideos().size() != 1) {
      setDisable(true);
    } else if (videos.iterator().next().getSource().get() != null) {
      setDisable(true);
    }

    setText(I18nUtility.getI18nBundle("operation").getString("menu.split"));

    getItems().add(new MenuSplit(controllable, VideoSplitMode.SPLIT_2_2));
    getItems().add(new MenuSplit(controllable, VideoSplitMode.SPLIT_3_3));
    getItems().add(new MenuSplit(controllable, VideoSplitMode.SPLIT_4_4));
    getItems().add(new MenuSplit(controllable, VideoSplitMode.SPLIT_4_8));
    getItems().add(new MenuSplit(controllable, VideoSplitMode.SPLIT_BY_SCREEN));
  }
}
