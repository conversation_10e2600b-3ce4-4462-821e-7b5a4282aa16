<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.Label?>
<?import javafx.scene.layout.Region?>
<?import javafx.scene.layout.VBox?>
<VBox xmlns="http://javafx.com/javafx/8"
      xmlns:fx="http://javafx.com/fxml/1" stylesheets="@aboutview.css" minWidth="600" minHeight="450" id="root">
  <Region id="about-logo"/>
  <VBox id="textContainer">
    <Label fx:id="companyLabel"/>
    <Label fx:id="versionLabel"/>
    <Label fx:id="javaVersionLabel"/>
    <Label fx:id="copyRightLabel"/>
    <Region prefHeight="10"/>
    <Label fx:id="fullNameLabel"/>
    <Region prefHeight="10"/>
    <VBox fx:id="phoneBox">

    </VBox>
  </VBox>
</VBox>