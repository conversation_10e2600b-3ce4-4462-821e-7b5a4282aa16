package com.mc.graph.undo;

import com.mc.graph.McGraphModel;
import com.mc.graph.interfaces.CellObject;
import com.mc.graph.interfaces.UndoableChange;

public class CellMove implements UndoableChange {
  private final McGraphModel model;
  private int from;
  private int to;

  /**
   * Constructor.
   * @param model graph model
   * @param from 移动源的索引
   * @param to 移动目的索引
   */
  public CellMove(McGraphModel model, int from, int to) {
    this.model = model;
    this.from = from;
    this.to = to;
  }
  
  @Override
  public void execute() {
    if (from < 0 || from >= model.getObservableCells().size()) {
      return;
    }
    CellObject cell = model.getObservableCells().get(from);
    model.innerMoveCell(cell, to);
    int temp = from;
    from = to;
    to = temp;
  }

}
