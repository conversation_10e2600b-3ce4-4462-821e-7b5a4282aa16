package com.mc.tool.framework.systemedit.datamodel;

import javafx.collections.FXCollections;
import javafx.collections.ObservableList;

/**
 * .
 */
public class VisualEditFunc extends VisualEditGroup {

  @Override
  public ObservableList<VisualEditFunc> getAllFunctions() {
    ObservableList<VisualEditFunc> list = FXCollections.observableArrayList();
    list.add(this);
    return list;
  }

  /**
   * 是否可操作.
   *
   * @return 如果可操作，返回true
   */
  public boolean isOperatable() {
    return true;
  }
}
