package com.mc.graph.interfaces;

import java.util.Collection;

public interface SkinManager {
  CellSkin getCellSkin(CellObject cell);
  
  /**
   * 获取所有的cell skin.
   * @return 所有的cell skin的集合
   */
  Collection<CellSkin> getAllCellSkin();
  
  void setCellSkin(CellObject cell, CellSkin skin);
  
  void removeCellSkin(CellObject cell);
  
  LinkSkin getLinkSkin(LinkObject link);
  
  void setLinkSkin(LinkObject link, LinkSkin skin);
  
  void removeLinkSkin(LinkObject link);
  
  ConnectorSkin getConnectorSkin(Connector connector);
  
  void setConnectorSkin(Connector connector, ConnectorSkin skin);
  
  void destroy();
  
}
