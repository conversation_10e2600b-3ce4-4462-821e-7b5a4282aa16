package com.mc.tool.framework.view;

import com.dooapp.fxform.FXForm;
import com.dooapp.fxform.builder.FXFormBuilder;
import com.fasterxml.jackson.dataformat.yaml.YAMLGenerator.Feature;
import com.fasterxml.jackson.dataformat.yaml.YAMLMapper;
import com.google.common.net.InetAddresses;
import com.mc.tool.framework.DefaultLog4jConfigurationFactory;
import com.mc.tool.framework.SyslogConfig;
import com.mc.tool.framework.VpmConfig;
import com.mc.tool.framework.utility.I18nUtility;
import com.mc.tool.framework.utility.UndecoratedDialog;
import com.mc.tool.framework.view.config.BasicConfigBean;
import com.mc.tool.framework.view.config.SyslogBean;
import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.util.Optional;
import java.util.ResourceBundle;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.geometry.Side;
import javafx.scene.Node;
import javafx.scene.control.ButtonType;
import javafx.scene.control.Tab;
import javafx.scene.control.TabPane;
import javafx.stage.Modality;
import javafx.stage.Window;
import lombok.extern.slf4j.Slf4j;

/**
 * .
 */
@Slf4j
public class ConfigView implements Initializable {
  @FXML
  protected Tab basicTab;
  @FXML
  protected Tab configTab;
  @FXML
  protected TabPane tabPane;
  protected Node root = null;
  protected ObservableList<FXForm> forms = FXCollections.observableArrayList();

  protected ConfigView() {
    FXMLLoader loader = new FXMLLoader(Thread.currentThread().getContextClassLoader()
        .getResource("com/mc/tool/framework/configview.fxml"));
    try {
      loader.setController(this);
      loader.setResources(I18nUtility.getI18nBundle("main"));
      root = loader.load();
    } catch (IOException exception) {
      log.warn("Fail to load configview.fxml!", exception);
    }
  }

  protected Node getView() {
    return root;
  }

  /**
   * 显示配置对话框.
   */
  public static void show(Window owner) {
    UndecoratedDialog<ButtonType> dialog = new UndecoratedDialog<>();
    dialog.initModality(Modality.WINDOW_MODAL);
    dialog.initOwner(owner);
    dialog.setTitle(I18nUtility.getI18nBundle("main").getString("framework.menu.config.title"));

    ConfigView view = new ConfigView();
    dialog.getDialogPane().setContent(view.getView());
    dialog.getDialogPane().getButtonTypes().add(ButtonType.APPLY);
    dialog.getDialogPane().getButtonTypes().add(ButtonType.CANCEL);

    Optional<ButtonType> result = dialog.showAndWait();
    if (result.isPresent() && result.get() == ButtonType.APPLY) {
      view.commit();
      PromptView.show(owner);
    }
  }

  @Override
  public void initialize(URL location, ResourceBundle resources) {
    FXForm<BasicConfigBean> basicForm =
        new FXFormBuilder().source(new BasicConfigBean()).buffered(true, false)
            .resourceBundle(I18nUtility.getI18nBundle("configbean")).build();
    basicTab.setContent(basicForm);
    basicTab.setClosable(false);
    forms.add(basicForm);

    FXForm<SyslogBean> configForm =
        new FXFormBuilder().source(new SyslogBean()).buffered(true, false)
            .resourceBundle(I18nUtility.getI18nBundle("configbean")).build();
    configTab.setContent(configForm);
    configTab.setClosable(false);
    forms.add(configForm);

    tabPane.setSide(Side.LEFT);
  }

  /**
   * 提交修改.
   */
  public void commit() {
    for (FXForm form : forms) {
      form.commit();
      if (form.getSource() instanceof SyslogBean) {
        SyslogBean formSource = (SyslogBean) form.getSource();
        if (InetAddresses.isInetAddress(formSource.getHost()) && formSource.getPort() > 0) {
          try {
            File configFile = new File(DefaultLog4jConfigurationFactory.CONFIG_FILE);
            if (!configFile.exists()) {
              System.out.println("create New configFile File: " + configFile.createNewFile());
            }
            SyslogConfig syslog = new SyslogConfig();
            syslog.setHost(formSource.getHost());
            syslog.setPort(formSource.getPort());
            VpmConfig vpmConfig = new VpmConfig();
            vpmConfig.setSyslog(syslog);
            YAMLMapper mapper = new YAMLMapper();
            mapper.configure(Feature.WRITE_DOC_START_MARKER, false);
            mapper.writeValue(new File(DefaultLog4jConfigurationFactory.CONFIG_FILE), vpmConfig);
          } catch (IOException ex) {
            log.error(ex.getLocalizedMessage());
          }
        }

      }
    }
  }
}
