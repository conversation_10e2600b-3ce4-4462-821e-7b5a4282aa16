<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.ContextMenu?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.ListView?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.Region?>
<?import javafx.scene.layout.VBox?>
<fx:root type="javafx.scene.layout.VBox" xmlns="http://javafx.com/javafx/8"
         xmlns:fx="http://javafx.com/fxml/1" stylesheets="@objectlist.css"
         fx:controller="com.mc.tool.framework.controller.ObjectListController">
  <HBox styleClass="object-list-title">
    <Label styleClass="object-logo"/>
    <Label styleClass="object-list-title-blank2"/>
    <Label fx:id="titleLabel" styleClass="object-list-name"/>
    <Region HBox.hgrow="ALWAYS"/>
    <Label styleClass="object-menu" onMousePressed="#onMenu">
      <contextMenu>
        <ContextMenu fx:id="listContextMenu"/>
      </contextMenu>
    </Label>
  </HBox>
  <HBox id="listviewContainer">
    <ListView fx:id="listView" HBox.hgrow="ALWAYS"/>
  </HBox>
</fx:root>