package com.mc.graph.util;

import com.mc.graph.interfaces.ConnectorSkin;
import javafx.beans.binding.DoubleBinding;
import javafx.beans.value.ObservableValue;
import javafx.scene.Parent;
import javafx.scene.shape.CubicCurveTo;
import javafx.scene.shape.LineTo;
import javafx.scene.shape.MoveTo;
import javafx.scene.shape.Path;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class LinkUtil {

  /**
   * Create a curve path link with two node.
   * 
   * @param sender sender node
   * @param receiver receiver node
   * @param container TODO
   * @return created path
   */
  public static Path createCurveLinkPath(final ConnectorSkin sender, final ConnectorSkin receiver,
      final Parent parent, Parent container) {
    final DoubleBinding startXBinding = getXPosBinding(sender, parent, container);
    final DoubleBinding startYBinding = getYPosBinding(sender, parent, container);
    final DoubleBinding endXBinding = getXPosBinding(receiver, parent, container);
    final DoubleBinding endYBinding = getYPosBinding(receiver, parent, container);
    final DoubleBinding controlX1Binding = getAvgBinding(startXBinding, endXBinding);
    final DoubleBinding controlY1Binding = startYBinding;
    final DoubleBinding controlX2Binding = controlX1Binding;
    final DoubleBinding controlY2Binding = endYBinding;

    MoveTo moveTo = new MoveTo();
    CubicCurveTo curveTo = new CubicCurveTo();

    moveTo.xProperty().bind(startXBinding);
    moveTo.yProperty().bind(startYBinding);

    curveTo.controlX1Property().bind(controlX1Binding);
    curveTo.controlY1Property().bind(controlY1Binding);
    curveTo.controlX2Property().bind(controlX2Binding);
    curveTo.controlY2Property().bind(controlY2Binding);
    curveTo.xProperty().bind(endXBinding);
    curveTo.yProperty().bind(endYBinding);

    Path result = new Path(moveTo, curveTo);
    return result;
  }

  /**
   * Create a right angle path link with two node.
   * 
   * @param sender sender node
   * @param receiver receiver node
   * @param container TODO
   * @return created path
   */
  public static Path createRightAngleLinkPath(final ConnectorSkin sender,
      final ConnectorSkin receiver, final Parent parent, Parent container) {
    final DoubleBinding startXBinding = getXPosBinding(sender, parent, container);

    final DoubleBinding startYBinding = getYPosBinding(sender, parent, container);

    final DoubleBinding endXBinding = getXPosBinding(receiver, parent, container);

    final DoubleBinding endYBinding = getYPosBinding(receiver, parent, container);

    final DoubleBinding controlX1Binding = getAvgBinding(startXBinding, endXBinding);

    MoveTo moveTo = new MoveTo();
    LineTo l1 = new LineTo();
    LineTo l2 = new LineTo();
    LineTo l3 = new LineTo();

    moveTo.xProperty().bind(startXBinding);
    moveTo.yProperty().bind(startYBinding);

    l1.xProperty().bind(controlX1Binding);
    l1.yProperty().bind(startYBinding);

    l2.xProperty().bind(controlX1Binding);
    l2.yProperty().bind(endYBinding);

    l3.xProperty().bind(endXBinding);
    l3.yProperty().bind(endYBinding);



    Path result = new Path(moveTo, l1, l2, l3);
    return result;
  }

  /**
   * 获取skin在parent的x坐标位置属性.
   * 
   * @param skin skin
   * @param parent parent
   * @param container container
   * @return x坐标位置属性
   */
  public static DoubleBinding getXPosBinding(ConnectorSkin skin, Parent parent, Parent container) {
    return new DoubleBinding() {
      {
        super.bind(skin.getContainerXposProperty());
      }

      @Override
      protected double computeValue() {
        return grandParentToLocal(parent, container, skin.getContainerXposProperty().get(), true);
      }
    };
  }

  /**
   * 获取skin在parent的y坐标位置属性.
   * 
   * @param skin skin
   * @param parent parent
   * @param container container
   * @return y坐标位置属性
   */
  public static DoubleBinding getYPosBinding(ConnectorSkin skin, Parent parent, Parent container) {
    return new DoubleBinding() {
      {
        super.bind(skin.getContainerYposProperty());
      }

      @Override
      protected double computeValue() {
        return grandParentToLocal(parent, container, skin.getContainerYposProperty().get(), false);
      }
    };
  }

  /**
   * 获取平均值属性.
   * 
   * @param bindings 用于求平均值的属性
   * @return 平均值属性
   */
  @SafeVarargs
  public static DoubleBinding getAvgBinding(ObservableValue<Number>... bindings) {
    if (bindings.length == 0) {
      return null;
    }
    return new DoubleBinding() {
      {
        super.bind(bindings);
      }

      @Override
      protected double computeValue() {
        double sum = 0;
        for (ObservableValue<Number> binding : bindings) {
          sum += binding.getValue().doubleValue();
        }
        return sum / bindings.length;
      }
    };
  }

  /**
   * Transforms a point from the local coordinate space of parent into the coordinate space of its
   * grand parent.
   * 
   * @param parent parent
   * @param grandParent grand parent
   * @param value position value
   * @param xaxis value is x axis value if true, otherwise is y axis.
   * @return grand parent's coordinate value.
   */
  public static double localToGrandParent(Parent parent, Parent grandParent, double value,
      boolean xaxis) {
    Parent temp = parent;
    while (temp != null && temp != grandParent) {
      if (xaxis) {
        value = temp.localToParent(value, 0).getX();
      } else {
        value = temp.localToParent(0, value).getY();
      }
      temp = temp.getParent();
    }
    if (temp == null) {
      return 0;
    } else {
      return value;
    }
  }

  /**
   * Transforms a point from the coordinate space of grand parent into the local coordinate space of
   * parent.
   * 
   * @param parent parent
   * @param grandParent grand parent
   * @param value position value
   * @param xaxis value is x axis value if true, otherwise is y axis.
   * @return parent's coordinate value.
   */
  public static double grandParentToLocal(Parent parent, Parent grandParent, double value,
      boolean xaxis) {
    Parent temp = parent;
    List<Parent> list = new ArrayList<>();
    while (temp != null && temp != grandParent) {
      list.add(temp);
      temp = temp.getParent();
    }
    // parent is not a child of grandParent.
    if (temp == null) {
      return 0;
    }

    Collections.reverse(list);
    for (Parent item : list) {
      if (xaxis) {
        value = item.parentToLocal(value, 0).getX();
      } else {
        value = item.parentToLocal(0, value).getY();
      }
    }
    return value;
  }

}
