<?import javafx.scene.control.Label?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.Region?>
<HBox xmlns="http://javafx.com/javafx/8" xmlns:fx="http://javafx.com/fxml/1" prefWidth="50" minWidth="50"
      prefHeight="20" minHeight="20"
      stylesheets="@matrix_connector_skin.css" styleClass="matrix_connector" alignment="CENTER_RIGHT">
  <Region fx:id="indicator" prefWidth="10"/>
  <Label fx:id="portIndex" prefWidth="30" prefHeight="15" alignment="CENTER_RIGHT"/>
</HBox>